# 0. 导入依赖包
# 1. 数据集划分（训练集、验证集、测试集） 
# 2. 模型集成：
#    2.1 线性回归
#    2.2 随机森林
#    2.3 XGBoost
#    2.4 MLP（Day4）
# 3. 模型权重优化 
# 4. 策略回测与评估 

# ——————————————————————————————————————————————————————————————————————————————

# 0. 导入依赖包
import warnings
# 忽略urllib3的OpenSSL警告
warnings.filterwarnings('ignore', message='urllib3 v2 only supports OpenSSL 1.1.1+')
# 忽略numpy的FutureWarning
warnings.filterwarnings('ignore', category=FutureWarning)

import pandas as pd  # 导入pandas库，用于数据处理
import numpy as np  # 导入numpy库，用于数值计算
import matplotlib.pyplot as plt  # 导入matplotlib库，用于画图
import seaborn as sns  # 导入seaborn库，用于画统计图
from datetime import datetime, timedelta  # 导入datetime库，用于处理时间
import os  # 导入os库，用于文件路径操作
import sys  # 导入sys库，用于系统路径操作

from dotenv import load_dotenv, find_dotenv  # 导入dotenv库，用于加载环境变量

# Find the .env file in the parent directory
dotenv_path = find_dotenv("../../.env")  # 查找上上级目录下的.env文件
# Load it explicitly
load_dotenv(dotenv_path)  # 加载.env文件中的环境变量

# Add the parent directory to the sys.path list
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))  # 把父目录加入系统路径，方便导入自定义模块

from data_processing import load_data_year, flatten_yf_columns, standardize_columns  # 导入自定义的数据处理函数
from plotting import plot_results  # 导入自定义的画图函数
from strategy.buy_and_hold import BuyAndHoldStrategy  # 导入买入持有策略
from back_test import run_backtest  # 导入回测函数
import backtrader as bt  # 导入backtrader库，用于量化回测

# 设置显示选项
pd.set_option('display.float_format', lambda x: '%.4f' % x)  # 设置pandas显示小数点后4位
# 绘图风格（可选）
plt.style.use('seaborn-v0_8-bright')  # 设置matplotlib的风格为seaborn-bright
# 设置中文显示
plt.rcParams['font.sans-serif'] = ['PingFang HK']  # 设置中文字体
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号

import random  # 导入random库，用于随机数

# 固定全局随机种子
np.random.seed(42)  # 固定numpy的随机种子，保证实验可复现
random.seed(42)  # 固定python的随机种子

def ensure_file_exists(file_path, description="文件"):
    """确保文件存在，如果不存在则尝试替代路径或抛出错误"""
    if os.path.exists(file_path):
        print(f"找到{description}: {file_path}")
        return file_path
    
    # 尝试绝对路径
    abs_path = os.path.join("/Users/<USER>/MLQuant", file_path)
    if os.path.exists(abs_path):
        print(f"找到{description}(绝对路径): {abs_path}")
        return abs_path
    
    # 尝试不同的目录结构
    alt_path = file_path.replace("Quant_ML_Struc/cache", "cache")
    if os.path.exists(alt_path):
        print(f"找到{description}(替代目录): {alt_path}")
        return alt_path
    
    # 尝试不同的文件名变体
    variants = [
        file_path.replace("_v2.xlsx", ".xlsx"),
        file_path.replace("cleaned", "winsorized"),
        file_path.replace("winsorized", "cleaned"),
        file_path.replace("_v2", "")
    ]
    
    for variant in variants:
        if os.path.exists(variant):
            print(f"找到{description}(替代文件名): {variant}")
            return variant
    
    # 如果所有尝试都失败，抛出错误
    raise FileNotFoundError(f"无法找到{description}: {file_path}")

def safe_load_excel(file_path, description="数据文件"):
    """安全地加载Excel文件，处理可能的错误"""
    try:
        # 确保文件存在
        file_path = ensure_file_exists(file_path, description)
        
        # 尝试加载文件
        print(f"正在加载{description}...")
        df = pd.read_excel(file_path)
        print(f"成功加载{description}，形状: {df.shape}")
        
        # 检查数据是否为空
        if df.empty:
            raise ValueError(f"{description}为空")
        
        return df
    except Exception as e:
        print(f"加载{description}时出错: {e}")
        raise

# ——————————————————————————————————————————————————————————————————————————————

# 1. 数据获取与预处理 - 使用新的预处理数据源
# 读取训练数据（已经winsorized处理过的数据）
train_data_path = 'Quant_ML_Struc/cache/TSLA_5min_interday_merged_factors_winsorized_v2.xlsx'
df_train = safe_load_excel(train_data_path, "训练数据")

# 读取回测数据（原始价格数据）
backtest_data_path = '/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_5min_interday_merged_factors_cleaned_v2.xlsx'
df_backtest = safe_load_excel(backtest_data_path, "回测数据")

# 确保数据读取成功
if df_train.empty or df_backtest.empty:  # 如果有一个数据为空
    raise ValueError("数据加载失败，请检查Excel文件路径")  # 抛出异常

# 打印数据信息以便调试
print(f"训练数据形状: {df_train.shape}")  # 打印训练数据的行列数
print(f"回测数据形状: {df_backtest.shape}")  # 打印回测数据的行列数
print(f"训练数据列: {df_train.columns.tolist()}")
print(f"回测数据列: {df_backtest.columns.tolist()}")

# 设置时间索引
# 确保datetime/date列是datetime类型，而不是字符串
if 'datetime' in df_train.columns:  # 如果有datetime列
    df_train['datetime'] = pd.to_datetime(df_train['datetime'])  # 转换为datetime类型
    df_backtest['datetime'] = pd.to_datetime(df_backtest['datetime'])  # 转换为datetime类型
    df_train = df_train.set_index('datetime')  # 设置为索引
    df_backtest = df_backtest.set_index('datetime')  # 设置为索引
elif 'date' in df_train.columns:  # 如果有date列
    df_train['date'] = pd.to_datetime(df_train['date'])  # 转换为datetime类型
    df_backtest['date'] = pd.to_datetime(df_backtest['date'])  # 转换为datetime类型
    df_train = df_train.set_index('date')  # 设置为索引
    df_backtest = df_backtest.set_index('date')  # 设置为索引

# 移除非特征列（如果有的话）
exclude_cols = ['date', 'timestamp', 'close', 'open', 'high', 'low', 'volume', 'future_ret_1d']  # 不作为特征的列
feature_cols = [col for col in df_train.columns if col not in exclude_cols and col != df_train.index.name]  # 只保留特征列

# 确保future_ret_1d标签存在
if 'future_ret_1d' not in df_train.columns:  # 如果没有标签列
    raise ValueError("标签列'future_ret_1d'不存在，请检查数据源")  # 抛出异常

print("数据预览:")  # 打印提示
print(df_train.head())  # 打印前5行数据
print("\n特征列清单:")  # 打印提示
print(feature_cols)  # 打印特征列名

# 查看标签分布
plt.figure(figsize=(10, 5))  # 新建画布
sns.histplot(df_train['future_ret_1d'], bins=50)  # 画标签的直方图
plt.title('下期收益率分布')  # 设置标题
plt.xlabel('收益率')  # 设置x轴
plt.show()  # 显示图形

# 计算特征与目标变量的相关性
corr = df_train[feature_cols + ['future_ret_1d']].corr()['future_ret_1d'].sort_values(ascending=False)  # 计算相关性并排序
print("\n特征与目标变量相关性排名（前10个）:")  # 打印提示
print(corr.head(10))  # 打印前10个相关性最高的特征

# ——————————————————————————————————————————————————————————————————————————————
# 2. 划分训练集与测试集 - 直接使用全部特征
df = df_train.copy()

df = df.sort_index()
train_idx = int(len(df) * 0.6)
valid_idx = int(len(df) * 0.8)
train, valid, test = df.iloc[:train_idx], df.iloc[train_idx:valid_idx], df.iloc[valid_idx:]

print("训练集范围:", train.index.min(), "→", train.index.max())
print("验证集范围:", valid.index.min(), "→", valid.index.max())
print("测试集范围:", test.index.min(), "→", test.index.max())
print("\n训练集样本数:", len(train))
print("验证集样本数:", len(valid))
print("测试集样本数:", len(test))

# 可视化训练集和测试集的划分
plt.figure(figsize=(15, 6))
plt.plot(train.index, train['future_ret_1d'], label='训练集', color='blue')
plt.plot(valid.index, valid['future_ret_1d'], label='验证集', color='green')
plt.plot(test.index, test['future_ret_1d'], label='测试集', color='red')
plt.axvline(df.index[train_idx], color='black', linestyle='--', label='划分点')
plt.axvline(df.index[valid_idx], color='black', linestyle='--', label='划分点')
plt.title('训练集、验证集、测试集划分')
plt.xlabel('日期')
plt.ylabel('收益率')
plt.legend()
plt.grid(True)
plt.show()

print(f"训练集大小: {len(train)}")
print(f"验证集大小: {len(valid)}")
print(f"测试集大小: {len(test)}")

# ——————————————————————————————————————————————————————————————————————————————

# 3. Buy & Hold策略 - 使用回测数据
# 准备回测数据，需要使用原始价格数据
test_data_for_backtest = df_backtest.iloc[valid_idx:].copy()

# 确保回测数据有完整的OHLCV列
required_cols = ['open', 'high', 'low', 'close', 'volume']
if not all(col in test_data_for_backtest.columns for col in required_cols):
    raise ValueError(f"回测数据缺少必要的列: {required_cols}")

ticker = 'TSLA'
# 确保start_date和end_date是datetime对象而非字符串
start_date = pd.to_datetime(test_data_for_backtest.index.min())
end_date = pd.to_datetime(test_data_for_backtest.index.max())

bh_result, bh_cerebro = run_backtest(
    ticker=ticker,
    df=test_data_for_backtest,
    start_date=start_date,
    end_date=end_date,
    strategy=BuyAndHoldStrategy,
    initial_cash=100000,
    print_log=True,
    timeframe=bt.TimeFrame.Minutes,  # This is 5-minute data
    compression=1
)

# ===== 猴子补丁：为 numpy 添加 bool8 和 object 属性 =====
import numpy as np

if not hasattr(np, 'bool8'):
    np.bool8 = np.bool_  # 使用 numpy 自带的 bool_ 类型
if not hasattr(np, 'object_'):  # 注意这里使用object_，而不是object
    np.object_ = object  # 兼容 backtrader_plotting 的引用
# 使用更安全的方式处理 np.object
try:
    _ = np.object
except AttributeError:
    np.object = object  # 兼容 backtrader_plotting 的旧版引用


def plot_results(cerebro):
    try:
        cerebro.plot()
    except Exception as e:
        print(f"Plotting error: {e}")
        print("Using alternative plotting method...")

        # Create a simple performance chart with matplotlib
        strat = cerebro.runstrats[0][0]
        if hasattr(strat, 'analyzers'):
            returns = strat.analyzers.getbyname('returns')
            if returns:
                plt.figure(figsize=(10, 6))
                plt.plot(returns.get_analysis())
                plt.title('Strategy Returns')
                plt.grid(True)
                plt.savefig('strategy_returns.png')
                plt.show()


# 6. 比较策略和Buy&Hold
# 正确调整代码顺序，修复先使用后定义的问题
# 移除比较策略部分，该部分将在策略定义后执行

# 4. 模型训练与超参数优化
# 使用所有特征列训练模型
features = feature_cols
X_train = train[features].values
y_train = train['future_ret_1d'].values
X_val = valid[features].values
y_val = valid['future_ret_1d'].values
X_test = test[features].values
y_test = test['future_ret_1d'].values

print(f"特征矩阵形状: X_train: {X_train.shape}, X_val: {X_val.shape}, X_test: {X_test.shape}")

# 4.1 训练线性模型
import copy
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import ParameterGrid
from sklearn.pipeline import Pipeline
import optuna

# 建立 Pipeline（先缩放，再线性回归）
pipeline_lr = Pipeline([
    ('lr', LinearRegression())
])

# 定义线性模型的超参数搜索范围
param_grid_lr = {
    'lr__fit_intercept': [True, False]
}

# 遍历所有参数组合，寻找最佳线性模型（在验证集上评估）
best_score_lr = float('-inf')
best_params_lr = None
best_pipeline_lr = None

for params in ParameterGrid(param_grid_lr):
    pipeline_lr.set_params(**params)
    pipeline_lr.fit(X_train, y_train)

    # 在验证集上进行预测和评估
    valid_pred_lr = pipeline_lr.predict(X_val)
    valid_r2_lr = r2_score(y_val, valid_pred_lr)

    if valid_r2_lr > best_score_lr:
        best_score_lr = valid_r2_lr
        best_params_lr = params
        # 复制当前 pipeline，保存最佳模型
        best_pipeline_lr = copy.deepcopy(pipeline_lr)
        print("更新：", best_score_lr, best_params_lr)

print("最佳参数：", best_params_lr)

# 使用最佳模型在训练集和测试集上评估
y_pred_train_lr = best_pipeline_lr.predict(X_train)
y_pred_test_lr = best_pipeline_lr.predict(X_test)

train_mse_lr = mean_squared_error(y_train, y_pred_train_lr)
test_mse_lr = mean_squared_error(y_test, y_pred_test_lr)
train_r2_lr = r2_score(y_train, y_pred_train_lr)
test_r2_lr = r2_score(y_test, y_pred_test_lr)

print("==== 线性模型 - 训练集 ====")
print("MSE:", train_mse_lr)
print("R2: ", train_r2_lr)

print("==== 线性模型 - 测试集 ====")
print("MSE:", test_mse_lr)
print("R2: ", test_r2_lr)

# 查看训练后的回归系数和截距
print("Coefficients:", best_pipeline_lr.named_steps['lr'].coef_)
print("Intercept:", best_pipeline_lr.named_steps['lr'].intercept_)

# 4.2 训练随机森林
from sklearn.ensemble import RandomForestRegressor


def objective_rf(trial):
    params = {
        'max_depth': trial.suggest_int('max_depth', 3, 10),
        'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
        'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', 0.3, 0.5, 1.0]),
        'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
        'min_samples_split': trial.suggest_int('min_samples_split', 2, 10),
        'random_state': 42
    }
    model = RandomForestRegressor(**params)
    model.fit(X_train, y_train)
    preds = model.predict(X_val)
    return -mean_squared_error(y_val, preds)


study_rf = optuna.create_study(direction='minimize')
study_rf.optimize(objective_rf, n_trials=30)
best_params_rf = study_rf.best_params
best_pipeline_rf = RandomForestRegressor(**best_params_rf)
best_pipeline_rf.fit(X_train, y_train)

# 使用最佳模型在训练集和测试集上评估
y_pred_train_rf = best_pipeline_rf.predict(X_train)
y_pred_test_rf = best_pipeline_rf.predict(X_test)

train_mse_rf = mean_squared_error(y_train, y_pred_train_rf)
test_mse_rf = mean_squared_error(y_test, y_pred_test_rf)
train_r2_rf = r2_score(y_train, y_pred_train_rf)
test_r2_rf = r2_score(y_test, y_pred_test_rf)

print("==== 随机森林 - 训练集 ====")
print("MSE:", train_mse_rf)
print("R2 :", train_r2_rf)

print("==== 随机森林 - 测试集 ====")
print("MSE:", test_mse_rf)
print("R2 :", test_r2_rf)

# 查看特征重要性
rf_importances = best_pipeline_rf.feature_importances_
print("\nSorted Feature Importances (RandomForest):")
rf_imp_dict = {features[i]: rf_importances[i] for i in range(len(features))}
sorted_rf_imp = sorted(rf_imp_dict.items(), key=lambda x: x[1], reverse=True)
for feat, imp in sorted_rf_imp:
    print(f"{feat} -> {imp:.4f}")

# 4.3 训练XGBoost
from xgboost import XGBRegressor


def objective_xgb(trial):
    params = {
        'learning_rate': trial.suggest_float('learning_rate', 0.001, 0.1, log=True),
        'max_depth': trial.suggest_int('max_depth', 3, 8),
        'n_estimators': trial.suggest_int('n_estimators', 100, 500),
        'subsample': trial.suggest_float('subsample', 0.6, 1.0),
        'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
        'reg_alpha': trial.suggest_float('reg_alpha', 0, 1.0),
        'reg_lambda': trial.suggest_float('reg_lambda', 0.1, 10.0),
        'random_state': 42,
        'verbosity': 0
    }
    model = XGBRegressor(**params)
    model.fit(X_train, y_train)
    preds = model.predict(X_val)
    return -mean_squared_error(y_val, preds)


study_xgb = optuna.create_study(direction='minimize')
study_xgb.optimize(objective_xgb, n_trials=30)
best_params_xgb = study_xgb.best_params
best_pipeline_xgb = XGBRegressor(**best_params_xgb)
best_pipeline_xgb.fit(X_train, y_train)

# 使用最佳模型在训练集和测试集上评估
y_pred_train_xgb = best_pipeline_xgb.predict(X_train)
y_pred_test_xgb = best_pipeline_xgb.predict(X_test)

train_mse_xgb = mean_squared_error(y_train, y_pred_train_xgb)
test_mse_xgb = mean_squared_error(y_test, y_pred_test_xgb)
train_r2_xgb = r2_score(y_train, y_pred_train_xgb)
test_r2_xgb = r2_score(y_test, y_pred_test_xgb)

print("==== XGBoost - 训练集 ====")
print("MSE:", train_mse_xgb)
print("R2: ", train_r2_xgb)

print("==== XGBoost - 测试集 ====")
print("MSE:", test_mse_xgb)
print("R2: ", test_r2_xgb)

# 查看特征重要性
xgb_importances = best_pipeline_xgb.feature_importances_
print("\nSorted Feature Importances (XGBoost):")
xgb_imp_dict = {features[i]: xgb_importances[i] for i in range(len(features))}
sorted_xgb_imp = sorted(xgb_imp_dict.items(), key=lambda x: x[1], reverse=True)
for feat, imp in sorted_xgb_imp:
    print(f"{feat} -> {imp:.4f}")


# 4.4 训练MLP
from sklearn.neural_network import MLPRegressor

def objective_mlp(trial):
    params = {
        'hidden_layer_sizes': trial.suggest_categorical('hidden_layer_sizes',
                                                        [(64, 32), (128, 64), (64, 64), (128, 128), (256, 128)]),
        'activation': trial.suggest_categorical('activation', ['relu', 'tanh']),
        'alpha': trial.suggest_float('alpha', 1e-5, 1e-2, log=True),
        'learning_rate_init': trial.suggest_float('learning_rate_init', 1e-4, 1e-2, log=True),
        'max_iter': 5000,
        'early_stopping': True,
        'n_iter_no_change': 20,
        'batch_size': trial.suggest_categorical('batch_size', [32, 64, 128, 'auto']),
        'random_state': 42
    }
    model = MLPRegressor(**params)
    model.fit(X_train, y_train)
    preds = model.predict(X_val)
    return -mean_squared_error(y_val, preds)


study_mlp = optuna.create_study(direction='minimize')
study_mlp.optimize(objective_mlp, n_trials=30)
best_params_mlp = study_mlp.best_params
best_pipeline_mlp = MLPRegressor(**best_params_mlp)
best_pipeline_mlp.fit(X_train, y_train)

# 使用最优模型在训练集和测试集上评估
y_pred_train_mlp = best_pipeline_mlp.predict(X_train)
y_pred_test_mlp = best_pipeline_mlp.predict(X_test)

train_mse_mlp = mean_squared_error(y_train, y_pred_train_mlp)
test_mse_mlp = mean_squared_error(y_test, y_pred_test_mlp)
train_r2_mlp = r2_score(y_train, y_pred_train_mlp)
test_r2_mlp = r2_score(y_test, y_pred_test_mlp)

print("==== MLP - 训练集 ====")
print("MSE:", train_mse_mlp)
print("R2: ", train_r2_mlp)

print("==== MLP - 测试集 ====")
print("MSE:", test_mse_mlp)
print("R2: ", test_r2_mlp)

# 5. 模型集成与权重优化（用凸优化）
import cvxpy as cp


def optimize_weights_constrained(
        models,
        X_val,
        y_val,
        sum_to_1=True,  # 是否约束权重和=1
        nonnegative=True,  # 是否要求所有权重>=0
        alpha_l1=0.0,  # L1正则系数
        alpha_l2=0.0,  # L2正则系数
        verbose=True
):
    """
    用凸优化方式在验证集上寻找一组最优权重，使得加权后的预测最小化 MSE
    （或等效地最大化 R²），并可选地加入 L1/L2 正则，还可选地约束权重和=1、权重>=0。

    参数：
    - models: 传入已训练好的各个模型列表
    - X_val, y_val: 验证集特征和目标
    - sum_to_1: Boolean, 若为 True，则加上 sum(w) == 1 的约束
    - nonnegative: Boolean, 若为 True，则加上 w >= 0 的约束
    - alpha_l1, alpha_l2: L1、L2 正则化系数
    - verbose: 是否打印约束求解的一些信息

    返回：
    - w_opt: 优化得到的权重向量 (numpy array)
    - score_r2: 用该权重在验证集上得到的 R² 分数
    """
    # 1) 先得到在验证集上的预测矩阵 predictions: shape (N, M)
    predictions = np.column_stack([model.predict(X_val) for model in models])
    N, M = predictions.shape

    # 2) 定义优化变量 w: 大小 M
    #    如果 nonnegative=True，则需要 w >= 0
    if nonnegative:
        w = cp.Variable(M, nonneg=True)
    else:
        w = cp.Variable(M)

    # 3) 定义约束列表 constraints
    constraints = []
    if sum_to_1:
        # sum(w) == 1
        constraints.append(cp.sum(w) == 1)

    # 4) 定义目标函数（最小化 MSE + 正则项）
    #    MSE 可以写成 sum_squares(y_val - predictions @ w)
    residual = y_val - predictions @ w
    obj_mse = cp.sum_squares(residual)

    # 若要加 L1 正则：alpha_l1 * ||w||_1
    # 若要加 L2 正则：alpha_l2 * ||w||_2^2
    obj_reg = 0
    if alpha_l1 > 0:
        obj_reg += alpha_l1 * cp.norm1(w)
    if alpha_l2 > 0:
        obj_reg += alpha_l2 * cp.norm2(w) ** 2

    # 最终目标：Minimize(MSE + 正则)
    objective = cp.Minimize(obj_mse + obj_reg)

    # 5) 构建并求解凸优化问题
    problem = cp.Problem(objective, constraints)
    result = problem.solve(verbose=verbose)

    # 6) 拿到最优权重 w_opt
    w_opt = w.value
    # 计算该组合在验证集上的 r2_score
    y_val_pred = predictions @ w_opt
    score_r2 = r2_score(y_val, y_val_pred)

    if verbose:
        print(f"Optimal objective (MSE + reg) = {problem.value:.6f}")
        print("Optimized weights:", w_opt)
        print(f"sum of weights = {w_opt.sum():.4f}")
        print(f"R2 on validation set = {score_r2:.4f}")

    return w_opt, score_r2


# 使用示例
w_constrained, r2_constrained = optimize_weights_constrained(
    models=[best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb, best_pipeline_mlp],
    X_val=X_val,
    y_val=y_val,
    sum_to_1=True,
    nonnegative=True,
    alpha_l1=0.0,
    alpha_l2=1e-3,
    verbose=False
)

print("得到的约束权重 =", [f"{num:0.2f}" for num in w_constrained])
print("验证集 R² =", r2_constrained)

# 得到测试集上各模型的预测矩阵
predictions_test = np.column_stack([model.predict(X_test) for model in
                                    [best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb,
                                     best_pipeline_mlp]])

# 利用之前优化得到的权重 w_constrained 对测试集预测进行加权组合
y_test_pred = predictions_test @ w_constrained

# 计算测试集上的 R² 分数
r2_test = r2_score(y_test, y_test_pred)
print("测试集 R² =", r2_test)


# 5. Emsemble策略实现与回测
# 自定义成交量指标，把成交量数据单独显示在子图中
class MyVolumeIndicator(bt.Indicator):
    """
    简单示例，把data的volume包装成一个单独的子图指标。
    """
    lines = ('vol',)
    plotinfo = dict(subplot=True, plotname='Volume')  # 让它单独开子图

    def __init__(self):
        self.lines.vol = self.data.volume


# 正确实现回测策略
class MLEnsembleStrategy(bt.Strategy):
    params = (
        ('target_percent', 0.98),  # 目标仓位百分比
        ('models', []),  # 模型列表
        ('weights', []),  # 权重列表
        ('feature_names', []),  # 特征名称列表
    )

    def __init__(self):
        # 从params中获取参数
        self.models = self.params.models
        self.weights = self.params.weights
        self.feature_names = self.params.feature_names
        
        # 检查模型是否有predict方法
        for i, model in enumerate(self.models):
            if not hasattr(model, 'predict'):
                print(f"错误: 模型 {i} 没有predict方法")
                print(f"模型类型: {type(model)}")
                # 尝试修复常见问题
                if hasattr(model, 'best_estimator_'):
                    self.models[i] = model.best_estimator_
                    print(f"已修复: 使用best_estimator_替代")
                elif hasattr(model, 'estimator'):
                    self.models[i] = model.estimator
                    print(f"已修复: 使用estimator替代")
        
        # 记录模型期望的特征数量
        self.expected_feature_count = len(self.feature_names)
        
        # 打印特征信息以便调试
        print(f"模型期望的特征数量: {self.expected_feature_count}")
        print(f"特征列表: {self.feature_names}")
        
        # 初始化其他变量
        self.value_history_dates = []
        self.value_history_values = []

        # 关闭主图中Data自带的Volume绘制
        self.data.plotinfo.plotvolume = False

        # 自定义成交量指标以及其SMA指标
        self.myvol = MyVolumeIndicator(self.data)
        self.vol_5 = bt.indicators.SMA(self.myvol.vol, period=5)
        self.vol_5.plotinfo.subplot = True
        self.vol_10 = bt.indicators.SMA(self.myvol.vol, period=10)
        self.vol_10.plotinfo.subplot = True

        self.last_trade_type = None  # 记录上一次交易类型（buy/sell）

    def next(self):
        # 从回测数据中获取当前所有特征值
        X = []
        
        for feature in self.feature_names:
            if hasattr(self.datas[0].lines, feature):
                feature_value = getattr(self.datas[0].lines, feature)[0]
                # 处理NaN和无穷大
                if np.isnan(feature_value) or np.isinf(feature_value):
                    if len(self.value_history_dates) == 0:  # 只在第一次打印
                        print(f"警告: 特征 {feature} 包含NaN或无穷大值，使用0替代")
                    feature_value = 0.0
                X.append(feature_value)
            else:
                X.append(0.0)
        
        X = np.array([X], dtype=float)
        
        # 再次检查是否有NaN
        if np.isnan(X).any() or np.isinf(X).any():
            print(f"错误: 处理后的特征仍包含NaN或无穷大值: {X}")
            return  # 跳过这个时间点
        
        # 实时对特征进行winsorize处理，与训练时保持一致
        # 假设我们对每个特征进行1%和99%分位数的winsorize
        for i in range(X.shape[1]):
            X[:, i] = np.clip(X[:, i], -3, 3)  # 简化版winsorize，可根据实际训练时的参数调整
        
        # 获取各模型的预测值
        try:
            predictions = []
            for i, model in enumerate(self.models):
                try:
                    pred = model.predict(X)[0]
                    predictions.append(pred)
                except Exception as e:
                    print(f"模型 {i} 预测失败: {e}")
                    predictions.append(0)  # 使用0作为默认预测
            
            predictions = np.array(predictions)
            # 加权平均得到集成预测
            pred_ret = np.sum(predictions * self.weights)
        except Exception as e:
            print(f"预测过程出错: {e}")
            print(f"特征值: {X}")
            pred_ret = 0

        # 获取当前持仓状态
        current_position = self.getposition().size

        if pred_ret > 0 and current_position == 0:
            # 只有当当前没有仓位时，才执行买入
            self.order_target_percent(target=self.p.target_percent)
            self.last_trade_type = "BUY"
            print(f"{self.datas[0].datetime.date(0)} => BUY signal, pred_ret={pred_ret:.6f}")

        elif pred_ret <= 0 and current_position > 0:
            # 只有当当前有仓位时，才执行卖出
            self.order_target_percent(target=0.0)
            self.last_trade_type = "SELL"
            print(f"{self.datas[0].datetime.date(0)} => SELL signal, pred_ret={pred_ret:.6f}")

        # 只在交易执行时打印仓位信息
        if self.last_trade_type:
            print(f"Current position size: {self.getposition().size}, Value: {self.broker.getvalue()}")

        dt = self.data.datetime.date(0)
        self.value_history_dates.append(dt)
        self.value_history_values.append(self.broker.getvalue())


# 为回测准备数据...
print("\n为回测准备数据...")

# 将所有特征添加到回测数据中
for feature in features:
    if feature not in test_data_for_backtest.columns:
        if feature in df_backtest.columns:
            test_data_for_backtest[feature] = df_backtest.loc[test_data_for_backtest.index, feature]
            print(f"已添加特征: {feature}")
        else:
            print(f"警告: 特征 {feature} 在回测数据源中不存在")


# 创建自定义DataFeed类
class FeatureDataFeed(bt.feeds.PandasData):
    """A PandasData feed that can handle additional feature columns."""

    # Define lines dynamically based on features
    params = dict(
        datetime=None,  # Use index as datetime
        open='open',
        high='high',
        low='low',
        close='close',
        volume='volume',
        # Add each custom feature
        **{feature: feature for feature in features}
    )

    # This is crucial - define lines with the features
    lines = tuple(features)


# 创建数据源
print("创建带特征的DataFeed...")
data_feed = FeatureDataFeed(dataname=test_data_for_backtest)

# 初始化Cerebro引擎
print("初始化Ensemble策略回测...")
ensemble_cerebro = bt.Cerebro()
ensemble_cerebro.adddata(data_feed)
ensemble_cerebro.broker.setcash(100000.0)
ensemble_cerebro.broker.setcommission(commission=0.0005)  # 0.05%手续费
ensemble_cerebro.broker.set_slippage_perc(perc=0.0002)  # 0.02%滑点

# 添加策略
ensemble_cerebro.addstrategy(
    MLEnsembleStrategy,
    models=[best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb, best_pipeline_mlp],
    weights=w_constrained,
    feature_names=features
)

# 添加分析器
ensemble_cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe_ratio')
ensemble_cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
ensemble_cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')

# 运行回测
print("开始执行Ensemble策略回测...")
ml_ensemble_results = ensemble_cerebro.run()
ml_strategy_instance = ml_ensemble_results[0]

# 输出结果
print("Ensemble策略回测结果:")
print(f"最终资产价值: {ensemble_cerebro.broker.getvalue():.2f}")
print(f"Sharpe比率: {ml_strategy_instance.analyzers.sharpe_ratio.get_analysis()['sharperatio']:.4f}")
print(f"最大回撤: {ml_strategy_instance.analyzers.drawdown.get_analysis()['max']['drawdown']:.2f}%")
print(f"年化收益率: {ml_strategy_instance.analyzers.returns.get_analysis()['ravg'] * 100:.2f}%")

# 可视化回测结果
try:
    ensemble_cerebro.plot()
except RuntimeError as e:
    print(f"Plotting error: {e}")
    print("Skipping plot due to custom data feed incompatibility")

# 6. 比较策略和Buy&Hold
print("\n买入持有策略与集成模型策略比较:")

# 再次运行Buy & Hold回测以确保公平比较
bh_cerebro = bt.Cerebro()
bh_cerebro.adddata(data_feed)  # 使用相同的数据
bh_cerebro.broker.setcash(100000.0)
bh_cerebro.addstrategy(BuyAndHoldStrategy)
bh_cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe_ratio')
bh_cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
bh_cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')

bh_results = bh_cerebro.run()
bh_strategy_instance = bh_results[0]

print("买入持有策略结果:")
print(f"最终资产价值: {bh_cerebro.broker.getvalue():.2f}")
print(f"Sharpe比率: {bh_strategy_instance.analyzers.sharpe_ratio.get_analysis()['sharperatio']:.4f}")
print(f"最大回撤: {bh_strategy_instance.analyzers.drawdown.get_analysis()['max']['drawdown']:.2f}%")
print(f"年化收益率: {bh_strategy_instance.analyzers.returns.get_analysis()['ravg'] * 100:.2f}%")

# 绘制对比图
plt.figure(figsize=(12, 6))
plt.plot(ml_strategy_instance.value_history_dates, ml_strategy_instance.value_history_values,
         label='集成模型策略')
plt.plot(bh_strategy_instance.value_history_dates, bh_strategy_instance.value_history_values,
         label='买入持有策略')
plt.xlabel('时间')
plt.ylabel('资产净值')
plt.title('策略回报曲线对比')
plt.legend()
plt.grid(True)
plt.show()

# 总结结果
print("\n策略比较总结:")
print("==================================")
print(f"{'指标':<15}{'集成模型':>15}{'买入持有':>15}")
print("----------------------------------")
print(f"{'最终资产'::<15}{ensemble_cerebro.broker.getvalue():.2f}{bh_cerebro.broker.getvalue():>15.2f}")
print(
    f"{'Sharpe比率'::<15}{ml_strategy_instance.analyzers.sharpe_ratio.get_analysis()['sharperatio']:.4f}{bh_strategy_instance.analyzers.sharpe_ratio.get_analysis()['sharperatio']:>15.4f}")
print(
    f"{'最大回撤(%)'::<15}{ml_strategy_instance.analyzers.drawdown.get_analysis()['max']['drawdown']:.2f}{bh_strategy_instance.analyzers.drawdown.get_analysis()['max']['drawdown']:>15.2f}")
print(
    f"{'年化收益率(%)'::<15}{ml_strategy_instance.analyzers.returns.get_analysis()['ravg'] * 100:.2f}{bh_strategy_instance.analyzers.returns.get_analysis()['ravg'] * 100:>15.2f}")
print("==================================")

from sklearn.ensemble import StackingRegressor


def build_stacking_ensemble(base_models, meta_model, X, y, cv):
    stack = StackingRegressor(
        estimators=base_models,
        final_estimator=meta_model,
        cv=cv,
        passthrough=True
    )
    stack.fit(X, y)
    return stack


class RiskMetricsAnalyzer(bt.Analyzer):
    def __init__(self): ...

    def stop(self): ...


import joblib

# 创建模型保存目录
model_save_dir = "/Users/<USER>/MLQuant/Quant_ML_Struc/cache/models"
os.makedirs(model_save_dir, exist_ok=True)

# 保存模型
ensemble_model = {
    'lr': best_pipeline_lr,
    'rf': best_pipeline_rf,
    'xgb': best_pipeline_xgb,
    'mlp': best_pipeline_mlp,
    'weights': w_constrained,
    'features': features
}
joblib.dump(ensemble_model, os.path.join(model_save_dir, 'ensemble_model.pkl'))
joblib.dump(features, os.path.join(model_save_dir, 'features.pkl'))

import shap

model = best_pipeline_xgb  # 或 best_pipeline_rf, best_pipeline_lr, best_pipeline_mlp
X = X_test  # 或 X_val, X_train

explainer = shap.Explainer(model, X)
shap_values = explainer(X)
shap.summary_plot(shap_values, X)


def objective_ridge(trial):
    alpha = trial.suggest_float('alpha', 1e-4, 10, log=True)
    model = Ridge(alpha=alpha, fit_intercept=True)
    model.fit(X_train, y_train)
    preds = model.predict(X_val)
    return -r2_score(y_val, preds)


study_ridge = optuna.create_study(direction='minimize')
study_ridge.optimize(objective_ridge, n_trials=30)
best_params_ridge = study_ridge.best_params
best_pipeline_lr = Ridge(**best_params_ridge)
best_pipeline_lr.fit(X_train, y_train)
