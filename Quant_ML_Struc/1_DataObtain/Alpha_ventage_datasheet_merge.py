import os
import glob
import pandas as pd
import numpy as np

# 1. 设置数据目录
cache_dir = "cache"

# 2. 搜索所有Alpha Vantage下载的TSLA 5分钟pkl文件（假设文件名格式为 av_TSLA_YYYYMM_5min.pkl）
#    这样可以自动找到所有月份的pkl文件
pkl_files = sorted(glob.glob(os.path.join(cache_dir, 'av_TSLA_*_5min.pkl')))

# 3. 读取每月pkl文件，并存入列表
all_dfs = []
for file in pkl_files:
    df = pd.read_pickle(file)  # 读取pkl文件
    # 确保有时间戳列，且为datetime类型
    if 'datetime' in df.columns:
        df['datetime'] = pd.to_datetime(df['datetime'])  # 转换为datetime类型
        df = df.set_index('datetime')  # 设置为索引
    elif df.index.name != 'datetime':
        df.index = pd.to_datetime(df.index)  # 如果索引不是datetime，强制转换
        df.index.name = 'datetime'
    all_dfs.append(df)  # 加入列表
    # 每月原始pkl文件已保留，无需另存

# 4. 合并所有月份的数据为一个DataFrame
df_all = pd.concat(all_dfs)  # 合并所有数据
df_all = df_all[~df_all.index.duplicated(keep='first')]  # 去除重复时间戳
df_all = df_all.sort_index()  # 按时间排序

# 5. 输出未补齐缺失股票信息的Excel
df_all_reset = df_all.reset_index().rename(columns={'index': 'datetime'})  # 索引变为普通列
output_excel_raw = os.path.join(cache_dir, "TSLA_5min_merged_raw.xlsx")
df_all_reset.to_excel(output_excel_raw, index=False)
print(f"已导出未补齐缺失的原始数据到 {output_excel_raw}")

# 只处理 TSLA_5min_merged_raw.xlsx
df = pd.read_excel("cache/TSLA_5min_merged_raw.xlsx")

# 除了datetime列，其余全部转为float64
for col in df.columns:
    if col != 'datetime':
        df[col] = pd.to_numeric(df[col], errors='coerce').astype('float64')

# 新增：描述性统计（原始数据）- 排除datetime列
numeric_df = df.drop(columns=['datetime'])  # 创建不包含datetime列的数据框
desc_stats = numeric_df.describe()

# 添加3σ异常值百分比行到描述性统计表格
outlier_percents = {}

for col in numeric_df.columns:
    mean = numeric_df[col].mean()
    std = numeric_df[col].std()
    lower_bound = mean - 3 * std
    upper_bound = mean + 3 * std
    
    outliers = numeric_df[(numeric_df[col] < lower_bound) | (numeric_df[col] > upper_bound)]
    n_outliers = len(outliers)
    total = len(numeric_df)
    outlier_percent = (n_outliers / total) * 100
    
    outlier_percents[col] = f"{outlier_percent:.4f}%"

# 将异常值百分比添加到描述性统计表格
desc_stats_with_outliers = desc_stats.copy()
outlier_row = pd.Series(outlier_percents, name="3σ异常值(%)")
desc_stats_with_outliers = pd.concat([desc_stats_with_outliers, pd.DataFrame([outlier_row])])

print(desc_stats_with_outliers)
