'''
我需要你帮我处理一下/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_raw_stockdata这个文件夹里面开头为av_TSLA的pkl文件。

在5min_handling里写代码文件，对这av_TSLA文件做如下操作，
1. 打印每个pkl文件的前两行和后两行，并告诉我每个pkl文件有多少样本（行）。
2. 融合这些pkl文件，并保存为TSLA_5m.xlsx
3. 检查融合后的的TSLA_5m.xlsx文件，有多少行时重复的，有没有NaN，或者空白数据，打印出结果。
   要保证TSLA_5m.xlsx文件的行数在时间上是顺序的，不能比如2024年3月1日的时间下面一行接2024年3月8日的时间，这样就乱了。
   因为是股票数据，所有有几天没有交易，但整体上从第一行到最后一行，时间顺序必须从早期到现在，也不能有超过5天的断层，否则就是有问题，就要报错。
   
4. 删除TSLA_5m.xlsx文件中重复的行，删除含有空白数据的行，删除含有NaN的行，保存为TSLA_5m_clean.xlsx
5. 打印出TSLA_5m_clean.xlsx文件的行数，并告诉我TSLA_5m_clean.xlsx文件有多少行，有没有空白和NaN数据。
   TSLA_5m_clean.xlsx里面的数据从左到右是open,high,low,close,volume,time，这5列组成，每一行都是特斯拉股票每5min的股票数据，但是如果我要训练模型，我需要基于这5列特征（time可以不算做一个特征），
   在网页和论文中，广泛查找资料，找能用利用这5分钟的open，high，low，close，volume数据衍生计算出的量化指标，我要你将这些量化指标分类，先打印在终端里面，
   然后计算出来，生成新xlsx文件，叫做TSLA_5m_clean_factors.xlsx。
   你做的量化因子，不仅要分类好，还要分类全面和多样化，要至少有4类根据open,high,low,close,volume数据计算出的因子，每个大类因子，至少得有5个不同的量化因子在不同尺度做衡量。
   并且计算下一个5分钟股票收益率是涨是跌，涨多少，跌多少，记为futrue_ret_5m，这是未来我训练LSTM模型的标签数据（目标变量，label）。
   
最终输出的winsorized文件我是要用于训练LSTM模型的，因此特征之间的量纲包括目标变量（标签，future_ret_5m）不能差别太大，否则影响收敛。

照如下顺序转换数据：
1. 先用原始没有转换过的数据（open，close，low，high，volume）计算技术指标，
2. 对原始数据进行标准化
3. 对技术指标进行标准化
4. 确保所有特征都在相似量纲范围内，考虑用最合适的标准化或数据处理方式
5. 保存标准化参数用于实际预测，最终保存的winsorized数据要删除NaN和空白行，让我直接用于训练LSTM模型。


6. 模仿/Users/<USER>/MLQuant/Quant_ML_Struc/Features_Engineering3_Full_Factors_ReadyForUse.py对机器学习数据做出预处理的方式，
对TSLA_5m_clean_factors.xlsx文件进行数据处理，要移除含有NaN和空白数据的行，并且打印出你移除了多少行。
打印出你对TSLA_5m_clean_factors.xlsx文件的处理的方式，然后用描述性统计和/Users/<USER>/MLQuant/Quant_ML_Struc/Features_Engineering3_Full_Factors_ReadyForUse.py里面描述机器学习的方式，打印出或者生成图片结果，展示最终可直接用于机器学习的数据前5行。
处理完的结果文件输出为TSLA_5m_clean_factors_winsorized.xlsx。

TSLA_5m_clean_factors_winsorized.xlsx这个文件一定要可以直接用于后面的机器学习，MLP，LSTM，深度神经网络，XGBoost，随机森林，等等。

8. 特别是，我之后要将最终输出的xlsx文件输出，训练LSTM模型，预测未来下一个5min的股票预期收益率，future_ret_5m,以此来决定是否买入或者卖出，以及买入或卖出到多。

9. 所有文件都保存到/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_5min这个文件中。

10. 以上输出的数据可以是xlsx方式，也可以是xlsx方式，也可以是pkl等方式，重要的是减少内存占用，加快数据展示，计算要减少算力和计算资源，减少电量。

11. 用#这个符号在每行代码后面加注释，让我知道你每行代码是如何操作、展示数据的。对于量化因子，你可以简要解释，但对于数据转化，和LSTM等模型的衔接相关的代码，你要深度解释。

12. 做一个预训练的迷你小训练集，命名为".....winsorized_5k"，只保留5000行数据，保存为excel格式。

'''

import pandas as pd
import numpy as np
import os
import glob
from datetime import datetime, timedelta
import talib
from scipy import stats
from sklearn.preprocessing import PowerTransformer, StandardScaler, QuantileTransformer
import joblib
import matplotlib.pyplot as plt
import seaborn as sns
import math

# 设置随机种子
SEED = 42
np.random.seed(SEED)

# 设置文件路径
base_dir = "/Users/<USER>/MLQuant/Quant_ML_Struc"
raw_data_dir = os.path.join(base_dir, "cache/TSLA_raw_stockdata")
output_dir = os.path.join(base_dir, "cache/TSLA_5min")

# 确保输出目录存在
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

def load_and_print_pkl_info():
    """加载并打印每个pkl文件的信息"""
    pkl_files = glob.glob(os.path.join(raw_data_dir, "av_TSLA_*.pkl"))
    all_data = []
    
    # 按年份和月份排序
    pkl_files.sort()
    
    for pkl_file in pkl_files:
        try:
            df = pd.read_pickle(pkl_file)
            # 将索引转换为列
            df = df.reset_index()
            
            # 检查数据是否重复
            if len(all_data) > 0:
                last_df = all_data[-1]
                if df.equals(last_df):
                    print(f"警告: {os.path.basename(pkl_file)} 与上一个文件数据重复，已跳过")
                    continue
            
            print(f"\n处理文件: {os.path.basename(pkl_file)}")
            print(f"前两行:\n{df.head(2)}")
            print(f"后两行:\n{df.tail(2)}")
            print(f"样本数: {len(df)}")
            all_data.append(df)
        except Exception as e:
            print(f"处理文件 {pkl_file} 时出错: {str(e)}")
    
    return pd.concat(all_data, ignore_index=True)

def merge_and_save_data(df):
    """合并数据并保存为Excel文件"""
    # 确保时间列是datetime类型
    df['index'] = pd.to_datetime(df['index'])
    
    # 按时间排序
    df = df.sort_values('index')
    
    # 重命名列
    df = df.rename(columns={'index': 'time'})
    
    # 保存合并后的数据
    output_file = os.path.join(output_dir, "TSLA_5m.xlsx")
    df.to_excel(output_file, index=False)
    print(f"\n合并后的数据已保存到: {output_file}")
    
    return df

def check_data_quality(df):
    """检查数据质量"""
    print("\n数据质量检查:")
    print(f"总行数: {len(df)}")
    print(f"重复行数: {df.duplicated().sum()}")
    print(f"NaN值数量:\n{df.isnull().sum()}")
    
    # 检查时间连续性
    print("\n检查时间连续性:")
    time_diff = df['time'].diff()
    gaps = time_diff[time_diff > timedelta(days=4)]
    
    if not gaps.empty:
        print("\n发现超过4天的时间断层:")
        print(gaps)
        raise ValueError("数据中存在超过4天的时间断层")
    else:
        print("未发现超过4天的时间断层")
        
    # 打印时间范围
    print(f"\n数据时间范围:")
    print(f"开始时间: {df['time'].min()}")
    print(f"结束时间: {df['time'].max()}")
    
    # 检查数据范围
    print("\n数据范围检查:")
    for col in ['open', 'high', 'low', 'close', 'volume']:
        print(f"{col}: [{df[col].min()}, {df[col].max()}]")

def clean_data(df, save_intermediate=False):
    """统一的数据清洗函数"""
    print("\n=== 开始数据清洗 ===")
    print(f"清洗前行数: {len(df)}")
    
    # 1. 删除重复行
    df = df.drop_duplicates()
    print(f"删除重复行后行数: {len(df)}")
    
    # 2. 删除包含NaN的行
    df = df.dropna()
    print(f"删除NaN后行数: {len(df)}")
    
    # 3. 删除包含0的行（除了time列）
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    df = df[~df[numeric_columns].isin([0]).any(axis=1)]
    print(f"删除0值后行数: {len(df)}")
    
    # 4. 删除包含空值的行
    df = df.replace('', np.nan).dropna()
    print(f"删除空值后行数: {len(df)}")
    
    # 5. 确保时间顺序
    df = df.sort_values('time')
    
    if save_intermediate:
        output_file = os.path.join(output_dir, "TSLA_5m_clean.xlsx")
        df.to_excel(output_file, index=False)
        print(f"清洗后的数据已保存到: {output_file}")
    
    return df

def calculate_technical_indicators(df):
    """计算技术指标"""
    # 确保数据类型正确
    for col in ['open', 'high', 'low', 'close', 'volume']:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 1. 趋势类指标
    df['SMA_5'] = talib.SMA(df['close'], timeperiod=5)
    df['SMA_10'] = talib.SMA(df['close'], timeperiod=10)
    df['EMA_5'] = talib.EMA(df['close'], timeperiod=5)
    df['EMA_10'] = talib.EMA(df['close'], timeperiod=10)
    df['MACD'], df['MACD_signal'], df['MACD_hist'] = talib.MACD(df['close'])
    
    # 2. 动量类指标
    df['RSI_14'] = talib.RSI(df['close'], timeperiod=14)
    df['MOM_5'] = talib.MOM(df['close'], timeperiod=5)
    df['ROC_5'] = talib.ROC(df['close'], timeperiod=5)
    df['WILLR_14'] = talib.WILLR(df['high'], df['low'], df['close'], timeperiod=14)
    df['CCI_14'] = talib.CCI(df['high'], df['low'], df['close'], timeperiod=14)
    
    # 3. 波动率类指标
    df['ATR_14'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=14)
    df['NATR_14'] = talib.NATR(df['high'], df['low'], df['close'], timeperiod=14)
    df['TRANGE'] = talib.TRANGE(df['high'], df['low'], df['close'])
    df['VOLATILITY_5'] = df['close'].pct_change().rolling(5).std()
    df['VOLATILITY_10'] = df['close'].pct_change().rolling(10).std()
    
    # 4. 成交量类指标
    df['OBV'] = talib.OBV(df['close'], df['volume'])
    df['AD'] = talib.AD(df['high'], df['low'], df['close'], df['volume'])
    df['ADOSC'] = talib.ADOSC(df['high'], df['low'], df['close'], df['volume'])
    df['VOLUME_SMA_5'] = talib.SMA(df['volume'], timeperiod=5)
    df['VOLUME_SMA_10'] = talib.SMA(df['volume'], timeperiod=10)
    
    # 计算未来5分钟收益率
    df['future_ret_5m'] = df['close'].shift(-1) / df['close'] - 1
    
    return df

def save_transformers(price_scaler, volume_scaler, tech_scaler):
    """保存数据转换器"""
    transformers_dir = os.path.join(output_dir, "transformers")
    if not os.path.exists(transformers_dir):
        os.makedirs(transformers_dir)
    
    # 保存各个转换器
    joblib.dump(price_scaler, os.path.join(transformers_dir, "price_scaler.joblib"))
    joblib.dump(volume_scaler, os.path.join(transformers_dir, "volume_scaler.joblib"))
    joblib.dump(tech_scaler, os.path.join(transformers_dir, "tech_scaler.joblib"))

def standardize_data(df):
    """标准化数据"""
    print("\n开始数据标准化...")
    
    # 1. 对原始价格数据进行标准化
    price_cols = ['open', 'high', 'low', 'close']
    price_scaler = StandardScaler()
    df[price_cols] = price_scaler.fit_transform(df[price_cols])
    
    # 2. 对成交量进行标准化
    volume_scaler = StandardScaler()
    df['volume'] = volume_scaler.fit_transform(df[['volume']])
    
    # 3. 对技术指标进行标准化
    tech_cols = [col for col in df.columns if col not in price_cols + ['time', 'volume', 'future_ret_5m']]
    tech_scaler = StandardScaler()
    if tech_cols:
        df[tech_cols] = tech_scaler.fit_transform(df[tech_cols])
    
    # 保存转换器
    save_transformers(price_scaler, volume_scaler, tech_scaler)
    
    return df

def preprocess_data(df):
    """数据预处理"""
    print("\n=== 开始数据预处理 ===")
    
    # 1. 计算技术指标
    df = calculate_technical_indicators(df)
    
    # 2. 数据标准化
    df = standardize_data(df)
    
    # 3. 最终数据清洗
    df = clean_data(df, save_intermediate=False)
    
    return df

def create_mini_dataset(df, n_samples=5000):
    """创建迷你训练集"""
    # 确保数据按时间排序
    df = df.sort_values('time')
    
    # 随机选择起始点
    start_idx = np.random.randint(0, len(df) - n_samples)
    mini_df = df.iloc[start_idx:start_idx + n_samples]
    
    output_file = os.path.join(output_dir, "TSLA_5m_clean_factors_winsorized_5k.xlsx")
    mini_df.to_excel(output_file, index=False)
    print(f"\n迷你训练集已保存到: {output_file}")
    
    # 打印迷你数据集的基本信息
    print("\n迷你数据集信息:")
    print(f"时间范围: {mini_df['time'].min()} 到 {mini_df['time'].max()}")
    print(f"样本数: {len(mini_df)}")
    print(f"特征数: {len(mini_df.columns) - 2}")  # 减去time和future_ret_5m

def save_data(df, filename, description=""):
    """统一的数据保存函数"""
    output_file = os.path.join(output_dir, filename)
    df.to_excel(output_file, index=False)
    print(f"\n{description}已保存到: {output_file}")

def main():
    # 1. 加载数据
    print("\n=== 开始加载数据 ===")
    df = load_and_print_pkl_info()
    
    # 2. 合并数据
    print("\n=== 开始合并数据 ===")
    df = merge_and_save_data(df)
    
    # 3. 检查数据质量
    print("\n=== 开始检查数据质量 ===")
    check_data_quality(df)
    
    # 4. 清洗数据
    print("\n=== 开始清洗数据 ===")
    df = clean_data(df, save_intermediate=True)
    
    # 5. 计算技术指标和预处理
    df = preprocess_data(df)
    
    # 6. 保存最终数据
    save_data(df, "TSLA_5m_clean_factors_winsorized.xlsx", "最终数据")
    
    # 7. 创建迷你训练集
    print("\n=== 创建迷你训练集 ===")
    create_mini_dataset(df)

if __name__ == "__main__":
    main()

