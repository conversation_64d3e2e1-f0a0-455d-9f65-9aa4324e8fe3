'''
我需要你帮我处理一下/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_raw_stockdata这个文件夹里面开头为av_TSLA的pkl文件。

在5min_handling里写代码文件，对这av_TSLA文件做如下操作，
1. 打印每个pkl文件的前两行和后两行，并告诉我每个pkl文件有多少样本（行）。
2. 融合这些pkl文件，并保存为TSLA_5m.csv
3. 检查融合后的的TSLA_5m.csv文件，有多少行时重复的，有没有NaN，或者空白数据，打印出结果。
   要保证TSLA_5m.csv文件的行数在时间上是顺序的，不能比如2024年3月1日的时间下面一行接2024年3月8日的时间，这样就乱了。
   因为是股票数据，所有有几天没有交易，但整体上从第一行到最后一行，时间顺序必须从早期到现在，也不能有超过5天的断层，否则就是有问题，就要报错。
   
4. 删除TSLA_5m.csv文件中重复的行，删除含有空白数据的行，删除含有NaN的行，保存为TSLA_5m_clean.csv
5. 打印出TSLA_5m_clean.csv文件的行数，并告诉我TSLA_5m_clean.csv文件有多少行，有没有空白和NaN数据。
   TSLA_5m_clean.csv里面的数据从左到右是open,high,low,close,volume,time，这5列组成，每一行都是特斯拉股票每5min的股票数据，但是如果我要训练模型，我需要基于这5列特征（time可以不算做一个特征），
   在网页和论文中，广泛查找资料，找能用利用这5分钟的open，high，low，close，volume数据衍生计算出的量化指标，我要你将这些量化指标分类，先打印在终端里面，
   然后计算出来，生成新csv文件，叫做TSLA_5m_clean_factors.csv。
   你做的量化因子，不仅要分类好，还要分类全面和多样化，要至少有45类根据open,high,low,close,volume数据计算出的因子，每个大类因子，至少得有5个不同的量化因子在不同尺度做衡量。
   并且计算下一个5分钟股票收益率是涨是跌，涨多少，跌多少，记为futrue_ret_5m，这是未来我训练LSTM模型的标签数据（目标变量，label）。

6. 模仿/Users/<USER>/MLQuant/Quant_ML_Struc/Features_Engineering3_Full_Factors_ReadyForUse.py对机器学习数据做出预处理的方式，
对TSLA_5m_clean_factors.csv文件进行数据处理，要移除含有NaN和空白数据的行，并且打印出你移除了多少行。
打印出你对TSLA_5m_clean_factors.csv文件的处理的方式，然后用描述性统计和/Users/<USER>/MLQuant/Quant_ML_Struc/Features_Engineering3_Full_Factors_ReadyForUse.py里面描述机器学习的方式，打印出或者生成图片结果，展示最终可直接用于机器学习的数据前5行。
处理完的结果文件输出为TSLA_5m_clean_factors_winsorized.csv。

TSLA_5m_clean_factors_winsorized.csv这个文件一定要可以直接用于后面的机器学习，MLP，LSTM，深度神经网络，XGBoost，随机森林，等等。

8. 特别是，我之后要将最终输出的csv文件输出，训练LSTM模型，预测未来下一个5min的股票预期收益率，future_ret_5m,以此来决定是否买入或者卖出，以及买入或卖出到多。

9. 所有文件都保存到/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_5min这个文件中。

10. 以上输出的数据可以是CSV方式，也可以是xlsx方式，也可以是pkl等方式，重要的是减少内存占用，加快数据展示，计算要减少算力和计算资源，减少电量。

11. 用#这个符号在每行代码后面加注释，让我知道你每行代码是如何操作、展示数据的。对于量化因子，你可以简要解释，但对于数据转化，和LSTM等模型的衔接相关的代码，你要深度解释。
'''

import os  # 操作系统相关操作
import glob  # 文件路径匹配
import pickle  # 读取pkl文件
import pandas as pd  # 数据处理
import numpy as np  # 数值计算
from datetime import datetime, timedelta  # 时间处理
import matplotlib.pyplot as plt  # 可视化
from scipy import stats

# 定义原始数据和输出数据的路径
raw_data_dir = '/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_raw_stockdata'  # 原始pkl文件目录
output_dir = '/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_5min'  # 输出文件目录
os.makedirs(output_dir, exist_ok=True)  # 如果输出目录不存在则创建

# 获取所有以av_TSLA开头的pkl文件
pkl_files = sorted(glob.glob(os.path.join(raw_data_dir, 'av_TSLA*.pkl')))  # 获取所有目标pkl文件

all_dfs = []  # 用于存储所有DataFrame

for pkl_file in pkl_files:
    with open(pkl_file, 'rb') as f:
        df = pickle.load(f)  # 读取pkl文件为DataFrame
    print(f'文件: {os.path.basename(pkl_file)}')  # 打印文件名
    print('前两行:')
    print(df.head(2))  # 打印前两行
    print('后两行:')
    print(df.tail(2))  # 打印后两行
    print(f'样本数: {len(df)}\n')  # 打印行数
    
    # 将索引转换为列
    df = df.reset_index()
    # 重命名索引列为time
    df = df.rename(columns={'index': 'time'})
    all_dfs.append(df)

# 合并所有DataFrame
merged_df = pd.concat(all_dfs, ignore_index=True)  # 合并所有数据

# 确保time列是datetime格式
merged_df['time'] = pd.to_datetime(merged_df['time'])

# 按时间升序排序
merged_df = merged_df.sort_values('time').reset_index(drop=True)  # 按时间排序

# 检查时间断层
time_diffs = merged_df['time'].diff().dropna()  # 计算相邻时间差
max_gap = time_diffs.max()  # 最大时间间隔
if max_gap > timedelta(days=5):
    raise ValueError(f'存在超过5天的时间断层，最大断层为{max_gap}')  # 超过5天报错

# 保存为csv
csv_path = os.path.join(output_dir, 'TSLA_5m.csv')
merged_df.to_csv(csv_path, index=False)  # 保存为csv
print(f'融合后的数据已保存为: {csv_path}')

# 检查重复、NaN、空白数据
num_duplicates = merged_df.duplicated().sum()  # 统计重复行数
print(f'重复行数: {num_duplicates}')

num_nan = merged_df.isna().sum().sum()  # 统计NaN总数
print(f'NaN数据总数: {num_nan}')

num_blank = (merged_df.astype(str).apply(lambda x: x.str.strip()) == '').sum().sum()  # 统计空白字符串
print(f'空白数据总数: {num_blank}')

# 删除重复、NaN、空白数据
clean_df = merged_df.drop_duplicates()  # 删除重复
clean_df = clean_df.dropna()  # 删除NaN
clean_df = clean_df[~(clean_df.astype(str).apply(lambda x: x.str.strip()) == '').any(axis=1)]  # 删除含空白的行

clean_csv_path = os.path.join(output_dir, 'TSLA_5m_clean.csv')
clean_df.to_csv(clean_csv_path, index=False)  # 保存清洗后的数据
print(f'清洗后的数据已保存为: {clean_csv_path}')
print(f'清洗后行数: {len(clean_df)}')
print(f'是否还有NaN: {clean_df.isna().sum().sum() > 0}')
print(f'是否还有空白: {(clean_df.astype(str).apply(lambda x: x.str.strip()) == "").sum().sum() > 0}')

# 移除含有NaN和空白数据的行
before_rows = len(clean_df)
df_clean = clean_df.dropna()  # 删除NaN
df_clean = df_clean[~(df_clean.astype(str).apply(lambda x: x.str.strip()) == '').any(axis=1)]  # 删除空白
after_rows = len(df_clean)
print(f'移除行数: {before_rows - after_rows}')

# 描述性统计
print('描述性统计:')
print(df_clean.describe().T)  # 打印描述性统计

# 可视化前5行
print('可用于机器学习的前5行:')
print(df_clean.head(5))

# 继续补充量化因子计算
# 趋势类因子
df_clean['trend_5m'] = df_clean['close'].rolling(5).mean() / df_clean['close'] - 1  # 5分钟趋势
df_clean['trend_10m'] = df_clean['close'].rolling(10).mean() / df_clean['close'] - 1  # 10分钟趋势
df_clean['trend_30m'] = df_clean['close'].rolling(30).mean() / df_clean['close'] - 1  # 30分钟趋势
df_clean['trend_60m'] = df_clean['close'].rolling(60).mean() / df_clean['close'] - 1  # 60分钟趋势
df_clean['trend_120m'] = df_clean['close'].rolling(120).mean() / df_clean['close'] - 1  # 120分钟趋势

# 波动率类因子
df_clean['volatility_5m'] = df_clean['close'].pct_change().rolling(5).std()  # 5分钟波动率
df_clean['volatility_10m'] = df_clean['close'].pct_change().rolling(10).std()  # 10分钟波动率
df_clean['volatility_30m'] = df_clean['close'].pct_change().rolling(30).std()  # 30分钟波动率
df_clean['volatility_60m'] = df_clean['close'].pct_change().rolling(60).std()  # 60分钟波动率
df_clean['volatility_120m'] = df_clean['close'].pct_change().rolling(120).std()  # 120分钟波动率

# 成交量类因子
df_clean['volume_ma_5m'] = df_clean['volume'].rolling(5).mean()  # 5分钟成交量均值
df_clean['volume_ma_10m'] = df_clean['volume'].rolling(10).mean()  # 10分钟成交量均值
df_clean['volume_ma_30m'] = df_clean['volume'].rolling(30).mean()  # 30分钟成交量均值
df_clean['volume_ma_60m'] = df_clean['volume'].rolling(60).mean()  # 60分钟成交量均值
df_clean['volume_ma_120m'] = df_clean['volume'].rolling(120).mean()  # 120分钟成交量均值

# 价格动量类因子
df_clean['momentum_5m'] = df_clean['close'].pct_change(5)  # 5分钟动量
df_clean['momentum_10m'] = df_clean['close'].pct_change(10)  # 10分钟动量
df_clean['momentum_30m'] = df_clean['close'].pct_change(30)  # 30分钟动量
df_clean['momentum_60m'] = df_clean['close'].pct_change(60)  # 60分钟动量
df_clean['momentum_120m'] = df_clean['close'].pct_change(120)  # 120分钟动量

# RSI类因子
def calculate_rsi(data, periods):
    delta = data.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=periods).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=periods).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

df_clean['rsi_5m'] = calculate_rsi(df_clean['close'], 5)  # 5分钟RSI
df_clean['rsi_10m'] = calculate_rsi(df_clean['close'], 10)  # 10分钟RSI
df_clean['rsi_30m'] = calculate_rsi(df_clean['close'], 30)  # 30分钟RSI
df_clean['rsi_60m'] = calculate_rsi(df_clean['close'], 60)  # 60分钟RSI
df_clean['rsi_120m'] = calculate_rsi(df_clean['close'], 120)  # 120分钟RSI

# MACD类因子
def calculate_macd(data, fast=12, slow=26, signal=9):
    exp1 = data.ewm(span=fast, adjust=False).mean()
    exp2 = data.ewm(span=slow, adjust=False).mean()
    macd = exp1 - exp2
    signal_line = macd.ewm(span=signal, adjust=False).mean()
    return macd, signal_line

df_clean['macd_5m'], df_clean['macd_signal_5m'] = calculate_macd(df_clean['close'], 5, 10, 3)  # 5分钟MACD
df_clean['macd_10m'], df_clean['macd_signal_10m'] = calculate_macd(df_clean['close'], 10, 20, 5)  # 10分钟MACD
df_clean['macd_30m'], df_clean['macd_signal_30m'] = calculate_macd(df_clean['close'], 15, 30, 9)  # 30分钟MACD
df_clean['macd_60m'], df_clean['macd_signal_60m'] = calculate_macd(df_clean['close'], 20, 40, 12)  # 60分钟MACD
df_clean['macd_120m'], df_clean['macd_signal_120m'] = calculate_macd(df_clean['close'], 30, 60, 18)  # 120分钟MACD

# 布林带类因子
def calculate_bollinger_bands(data, window=20, num_std=2):
    rolling_mean = data.rolling(window=window).mean()
    rolling_std = data.rolling(window=window).std()
    upper_band = rolling_mean + (rolling_std * num_std)
    lower_band = rolling_mean - (rolling_std * num_std)
    return upper_band, rolling_mean, lower_band

df_clean['bb_upper_5m'], df_clean['bb_middle_5m'], df_clean['bb_lower_5m'] = calculate_bollinger_bands(df_clean['close'], 5, 2)
df_clean['bb_upper_10m'], df_clean['bb_middle_10m'], df_clean['bb_lower_10m'] = calculate_bollinger_bands(df_clean['close'], 10, 2)
df_clean['bb_upper_30m'], df_clean['bb_middle_30m'], df_clean['bb_lower_30m'] = calculate_bollinger_bands(df_clean['close'], 30, 2)
df_clean['bb_upper_60m'], df_clean['bb_middle_60m'], df_clean['bb_lower_60m'] = calculate_bollinger_bands(df_clean['close'], 60, 2)
df_clean['bb_upper_120m'], df_clean['bb_middle_120m'], df_clean['bb_lower_120m'] = calculate_bollinger_bands(df_clean['close'], 120, 2)

# 价格位置类因子
df_clean['price_position_5m'] = (df_clean['close'] - df_clean['bb_lower_5m']) / (df_clean['bb_upper_5m'] - df_clean['bb_lower_5m'])
df_clean['price_position_10m'] = (df_clean['close'] - df_clean['bb_lower_10m']) / (df_clean['bb_upper_10m'] - df_clean['bb_lower_10m'])
df_clean['price_position_30m'] = (df_clean['close'] - df_clean['bb_lower_30m']) / (df_clean['bb_upper_30m'] - df_clean['bb_lower_30m'])
df_clean['price_position_60m'] = (df_clean['close'] - df_clean['bb_lower_60m']) / (df_clean['bb_upper_60m'] - df_clean['bb_lower_60m'])
df_clean['price_position_120m'] = (df_clean['close'] - df_clean['bb_lower_120m']) / (df_clean['bb_upper_120m'] - df_clean['bb_lower_120m'])

# 成交量比率类因子
df_clean['volume_ratio_5m'] = df_clean['volume'] / df_clean['volume'].rolling(5).mean()
df_clean['volume_ratio_10m'] = df_clean['volume'] / df_clean['volume'].rolling(10).mean()
df_clean['volume_ratio_30m'] = df_clean['volume'] / df_clean['volume'].rolling(30).mean()
df_clean['volume_ratio_60m'] = df_clean['volume'] / df_clean['volume'].rolling(60).mean()
df_clean['volume_ratio_120m'] = df_clean['volume'] / df_clean['volume'].rolling(120).mean()

# 价格波动率类因子
df_clean['price_volatility_5m'] = (df_clean['high'] - df_clean['low']) / df_clean['close']
df_clean['price_volatility_10m'] = (df_clean['high'].rolling(2).max() - df_clean['low'].rolling(2).min()) / df_clean['close']
df_clean['price_volatility_30m'] = (df_clean['high'].rolling(6).max() - df_clean['low'].rolling(6).min()) / df_clean['close']
df_clean['price_volatility_60m'] = (df_clean['high'].rolling(12).max() - df_clean['low'].rolling(12).min()) / df_clean['close']
df_clean['price_volatility_120m'] = (df_clean['high'].rolling(24).max() - df_clean['low'].rolling(24).min()) / df_clean['close']

# 计算未来5分钟收益率作为标签
df_clean['future_ret_5m'] = df_clean['close'].shift(-1) / df_clean['close'] - 1

# 数据预处理：移除NaN和异常值
def winsorize_series(series, limits=(0.01, 0.99)):
    """对数据进行winsorize处理，限制在1%和99%分位数之间"""
    lower, upper = series.quantile(limits)
    return series.clip(lower=lower, upper=upper)

# 对所有数值列进行winsorize处理
numeric_columns = df_clean.select_dtypes(include=[np.number]).columns
for col in numeric_columns:
    if col != 'future_ret_5m':  # 不对标签进行winsorize
        df_clean[col] = winsorize_series(df_clean[col])

# 移除含有NaN的行
df_clean = df_clean.dropna()

# 保存处理后的数据
final_csv_path = os.path.join(output_dir, 'TSLA_5m_clean_factors_winsorized.csv')
df_clean.to_csv(final_csv_path, index=False)

# 打印数据统计信息
print("\n数据统计信息:")
print(f"总行数: {len(df_clean)}")
print(f"特征数量: {len(numeric_columns)}")
print("\n前5行数据:")
print(df_clean.head())
print("\n描述性统计:")
print(df_clean.describe().T)

# 绘制部分因子的时间序列图
plt.figure(figsize=(15, 10))
plt.subplot(2, 1, 1)
plt.plot(df_clean['time'], df_clean['close'], label='Close Price')
plt.title('Close Price Over Time')
plt.legend()

plt.subplot(2, 1, 2)
plt.plot(df_clean['time'], df_clean['future_ret_5m'], label='Future 5min Return')
plt.title('Future 5min Return Over Time')
plt.legend()

plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'factor_visualization.png'))
plt.close()

print(f"\n数据已保存至: {final_csv_path}")
print(f"可视化图表已保存至: {os.path.join(output_dir, 'factor_visualization.png')}")

# 增加新的因子类别
# 1. 价格加速度因子
df_clean['price_accel_5m'] = df_clean['close'].pct_change().pct_change()
df_clean['price_accel_10m'] = df_clean['close'].pct_change(2).pct_change(2)
df_clean['price_accel_30m'] = df_clean['close'].pct_change(6).pct_change(6)
df_clean['price_accel_60m'] = df_clean['close'].pct_change(12).pct_change(12)
df_clean['price_accel_120m'] = df_clean['close'].pct_change(24).pct_change(24)

# 2. 价格波动率变化率因子
df_clean['vol_change_5m'] = df_clean['volatility_5m'].pct_change()
df_clean['vol_change_10m'] = df_clean['volatility_10m'].pct_change()
df_clean['vol_change_30m'] = df_clean['volatility_30m'].pct_change()
df_clean['vol_change_60m'] = df_clean['volatility_60m'].pct_change()
df_clean['vol_change_120m'] = df_clean['volatility_120m'].pct_change()

# 3. 成交量波动率因子
df_clean['volume_vol_5m'] = df_clean['volume'].pct_change().rolling(5).std()
df_clean['volume_vol_10m'] = df_clean['volume'].pct_change().rolling(10).std()
df_clean['volume_vol_30m'] = df_clean['volume'].pct_change().rolling(30).std()
df_clean['volume_vol_60m'] = df_clean['volume'].pct_change().rolling(60).std()
df_clean['volume_vol_120m'] = df_clean['volume'].pct_change().rolling(120).std()

# 4. 价格动量变化率因子
df_clean['mom_change_5m'] = df_clean['momentum_5m'].pct_change()
df_clean['mom_change_10m'] = df_clean['momentum_10m'].pct_change()
df_clean['mom_change_30m'] = df_clean['momentum_30m'].pct_change()
df_clean['mom_change_60m'] = df_clean['momentum_60m'].pct_change()
df_clean['mom_change_120m'] = df_clean['momentum_120m'].pct_change()

# 5. 价格与成交量相关性因子
df_clean['price_vol_corr_5m'] = df_clean['close'].rolling(5).corr(df_clean['volume'])
df_clean['price_vol_corr_10m'] = df_clean['close'].rolling(10).corr(df_clean['volume'])
df_clean['price_vol_corr_30m'] = df_clean['close'].rolling(30).corr(df_clean['volume'])
df_clean['price_vol_corr_60m'] = df_clean['close'].rolling(60).corr(df_clean['volume'])
df_clean['price_vol_corr_120m'] = df_clean['close'].rolling(120).corr(df_clean['volume'])

# 对每个数值列进行正态性检验
numeric_columns = df_clean.select_dtypes(include=[np.number]).columns
for col in numeric_columns:
    if col != 'future_ret_5m':  # 不对标签进行检验
        stat, p_value = stats.normaltest(df_clean[col].dropna())
        print(f"\n{col}:")
        print(f"Statistic: {stat:.4f}")
        print(f"p-value: {p_value:.4f}")
        print(f"是否服从正态分布: {'是' if p_value > 0.05 else '否'}")
