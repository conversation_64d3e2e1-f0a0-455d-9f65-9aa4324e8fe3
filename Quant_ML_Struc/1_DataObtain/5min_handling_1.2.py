'''
我需要你帮我处理一下/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_raw_stockdata这个文件夹里面开头为av_TSLA的pkl文件。

在5min_handling里写代码文件，对这av_TSLA文件做如下操作，
1. 打印每个pkl文件的前两行和后两行，并告诉我每个pkl文件有多少样本（行）。
2. 融合这些pkl文件，并保存为TSLA_5m.xlsx
   在合并所有pkl文件时，把索引（即时间）重置为普通列，并命名为 time。
   这样后续所有操作都可以直接用 time 这一列。

3. 检查融合后的的TSLA_5m.xlsx文件，有多少行时重复的，有没有NaN，或者空白数据，打印出结果。
   要保证TSLA_5m.xlsx文件的行数在时间上是顺序的，不能比如2024年3月1日的时间下面一行接2024年3月8日的时间，这样就乱了。
   因为是股票数据，所有有几天没有交易，但整体上从第一行到最后一行，时间顺序必须从早期到现在，也不能有超过5天的断层，否则就是有问题，就要报错。
   
由于涉及大量数据处理，所以你一定要制定一个超高效的处理数据的方案，不要用for循环，不要用for循环，不要用for循环，重要的事情说三遍，用高级且简单的数据处理方式。
因为DataFrame碎片化，建议批量生成新特征后一次性合并，并用.copy()解除碎片化。
用pd.to_numeric(..., downcast='float')等方式将float/int列降级为float32/int32。
volume等可以用int32。
先用字典或DataFrame批量生成所有新特征，再一次性concat到主表。

4. 删除TSLA_5m.xlsx文件中重复的行，删除含有空白数据的行，删除含有NaN的行，保存为TSLA_5m_clean.xlsx
5. 打印出TSLA_5m_clean.xlsx文件的行数，并告诉我TSLA_5m_clean.xlsx文件有多少行，有没有空白和NaN数据。
   TSLA_5m_clean.xlsx里面的数据从左到右是open,high,low,close,volume,time，这5列组成，每一行都是特斯拉股票每5min的股票数据，但是如果我要训练模型，我需要基于这5列特征（time可以不算做一个特征），
   在网页和论文中，广泛查找资料，找能用利用这5分钟的open，high，low，close，volume数据衍生计算出的量化指标，我要你将这些量化指标分类，先打印在终端里面，
   然后计算出来，生成新xlsx文件，叫做TSLA_5m_clean_factors.xlsx。
   你做的量化因子，不仅要分类好，还要分类全面和多样化，要至少有4类根据open,high,low,close,volume数据计算出的因子，每个大类因子，至少得有5个不同的量化因子在不同尺度做衡量。
   并且计算下一个5分钟股票收益率是涨是跌，涨多少，跌多少，记为futrue_ret_5m，这是未来我训练LSTM模型的标签数据（目标变量，label）。
   你给我的量化因子大类要多样，不要重复。
   
最终输出的winsorized文件我是要用于训练LSTM模型的，因此特征之间的量纲包括目标变量（标签，future_ret_5m）不能差别太大，否则影响收敛。

照如下顺序转换数据：
1）. 先用原始没有转换过的数据（open，close，low，high，volume）计算技术指标，
2）. 对原始数据进行标准化
3）. 对技术指标进行标准化
4）. 确保所有特征都在相似量纲范围内，考虑用最合适的标准化或数据处理方式
5）. 保存标准化参数用于实际预测，最终保存的winsorized数据要删除NaN和空白行，让我直接用于训练LSTM模型。


6. 模仿/Users/<USER>/MLQuant/Quant_ML_Struc/Features_Engineering3_Full_Factors_ReadyForUse.py对机器学习数据做出预处理的方式，
对TSLA_5m_clean_factors.xlsx文件进行数据处理，要移除含有NaN和空白数据的行，并且打印出你移除了多少行。
打印出你对TSLA_5m_clean_factors.xlsx文件的处理的方式，然后用描述性统计和/Users/<USER>/MLQuant/Quant_ML_Struc/Features_Engineering3_Full_Factors_ReadyForUse.py里面描述机器学习的方式，打印出或者生成图片结果，展示最终可直接用于机器学习的数据前5行。
处理完的结果文件输出为TSLA_5m_clean_factors_winsorized.xlsx。

TSLA_5m_clean_factors_winsorized.xlsx这个文件一定要可以直接用于后面的机器学习，MLP，LSTM，深度神经网络，XGBoost，随机森林，等等。

8. 特别是，我之后要将最终输出的xlsx文件输出，训练LSTM模型，预测未来下一个5min的股票预期收益率，future_ret_5m,以此来决定是否买入或者卖出，以及买入或卖出到多。

9. 所有文件都保存到/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_5min这个文件中。

10. 以上输出的数据可以是xlsx方式，也可以是xlsx方式，也可以是pkl等方式，重要的是减少内存占用，加快数据展示，计算要减少算力和计算资源，减少电量。

11. 用#这个符号在每行代码后面加注释，让我知道你每行代码是如何操作、展示数据的。对于量化因子，你可以简要解释，但对于数据转化，和LSTM等模型的衔接相关的代码，你要深度解释。

12. 做一个预训练的迷你小训练集，命名为".....winsorized_5k"，只保留5000行数据，保存为excel格式。

'''

import os  # 操作系统相关
import glob  # 文件路径匹配
import pandas as pd  # 数据处理
import numpy as np  # 数值计算
from ta import add_all_ta_features  # 技术指标库
from sklearn.preprocessing import StandardScaler  # 标准化
import matplotlib.pyplot as plt  # 可视化

# 1. 读取所有pkl文件，打印前两行和后两行，统计行数
raw_dir = '/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_raw_stockdata'  # 原始数据目录
save_dir = '/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_5min'  # 结果保存目录
os.makedirs(save_dir, exist_ok=True)  # 创建保存目录

pkl_files = sorted(glob.glob(os.path.join(raw_dir, 'av_TSLA*.pkl')))  # 匹配所有pkl文件
dfs = []  # 用于存储所有DataFrame

for file in pkl_files:
    df = pd.read_pickle(file)  # 读取pkl文件
    print(f'文件: {os.path.basename(file)}')  # 打印文件名
    print('前两行:\n', df.head(2))  # 打印前两行
    print('后两行:\n', df.tail(2))  # 打印后两行
    print('样本数:', len(df), '\n')  # 打印行数
    dfs.append(df)  # 添加到列表

# 2. 融合所有pkl文件，重置索引为time列，保存为TSLA_5m.xlsx
df_all = pd.concat(dfs, axis=0, ignore_index=False)  # 合并所有数据
df_all = df_all.reset_index().rename(columns={'index': 'time'})  # 索引变为time列
df_all['time'] = pd.to_datetime(df_all['time'])  # 转换为时间格式
df_all = df_all.sort_values('time').reset_index(drop=True)  # 按时间排序
df_all.to_excel(os.path.join(save_dir, 'TSLA_5m.xlsx'), index=False)  # 保存为xlsx

# 3. 检查融合后的文件
df_check = df_all.copy()
dup_count = df_check.duplicated().sum()  # 重复行数
nan_count = df_check.isna().sum().sum()  # NaN总数
blank_count = (df_check == '').sum().sum()  # 空白总数
print(f'重复行数: {dup_count}, NaN数: {nan_count}, 空白数: {blank_count}')  # 打印检查结果

# 检查时间顺序和断层
time_sorted = df_check['time'].sort_values().reset_index(drop=True)
time_diff = time_sorted.diff().dt.total_seconds().dropna()
max_gap = time_diff.max() / (60*60*24)  # 最大断层天数
if not time_sorted.is_monotonic_increasing:
    raise ValueError('时间顺序有误！')
if max_gap > 5:
    raise ValueError(f'存在超过5天的断层，最大断层为{max_gap:.2f}天！')

# 4. 删除重复、空白、NaN行，保存为TSLA_5m_clean.xlsx
df_clean = df_check.drop_duplicates()  # 删除重复
df_clean = df_clean.replace('', np.nan).dropna()  # 删除空白和NaN
df_clean = df_clean.sort_values('time').reset_index(drop=True)  # 再次排序
df_clean.to_excel(os.path.join(save_dir, 'TSLA_5m_clean.xlsx'), index=False)  # 保存

# 5. 打印清洗后文件信息
print('清洗后行数:', len(df_clean))  # 打印行数
print('是否有NaN:', df_clean.isna().sum().sum() > 0)  # 是否有NaN
print('是否有空白:', (df_clean == '').sum().sum() > 0)  # 是否有空白

# 6. 批量生成所有新特征，避免碎片化
factor_dict = {}

# 价格动量类
factor_dict['ret_5m'] = df_clean['close'].pct_change()
factor_dict['volatility_5m'] = df_clean['close'].rolling(5).std()
factor_dict['momentum_5m'] = df_clean['close'] - df_clean['close'].shift(5)
factor_dict['drawdown_5m'] = df_clean['close'] / df_clean['close'].cummax() - 1
factor_dict['amplitude_5m'] = (df_clean['high'] - df_clean['low']) / df_clean['close']

# 成交量类
factor_dict['volume_ratio_5m'] = df_clean['volume'] / df_clean['volume'].rolling(5).mean()
factor_dict['volume_mean_5m'] = df_clean['volume'].rolling(5).mean()
factor_dict['volume_std_5m'] = df_clean['volume'].rolling(5).std()
factor_dict['volume_change_5m'] = df_clean['volume'].pct_change()
factor_dict['obv'] = (np.sign(df_clean['close'].diff()) * df_clean['volume']).fillna(0).cumsum()

# 价格形态类
factor_dict['upper_shadow'] = df_clean['high'] - df_clean[['close', 'open']].max(axis=1)
factor_dict['lower_shadow'] = df_clean[['close', 'open']].min(axis=1) - df_clean['low']
factor_dict['body'] = abs(df_clean['close'] - df_clean['open'])
factor_dict['close_open_ratio'] = df_clean['close'] / df_clean['open']
factor_dict['high_low_ratio'] = df_clean['high'] / df_clean['low']

# 批量合并
factor_df = pd.DataFrame(factor_dict, index=df_clean.index)
df_factors = pd.concat([df_clean, factor_df], axis=1)
df_factors = df_factors.copy()  # 解除碎片化

# 技术指标类（如需全部指标可用ta库，否则建议只选用部分常用指标）
# 例如只加MACD、RSI等
from ta.trend import MACD
from ta.momentum import RSIIndicator

macd = MACD(df_factors['close'])
df_factors['macd'] = macd.macd()
df_factors['macd_signal'] = macd.macd_signal()
df_factors['macd_diff'] = macd.macd_diff()
df_factors['rsi'] = RSIIndicator(df_factors['close']).rsi()

df_factors = df_factors.copy()  # 再次解除碎片化

# 计算未来5分钟收益率
df_factors['future_ret_5m'] = df_factors['close'].shift(-1) / df_factors['close'] - 1

# 7. 标准化
feature_cols = ['open', 'high', 'low', 'close', 'volume']  # 原始特征
factor_cols = [col for col in df_factors.columns if col not in ['time', 'future_ret_5m']]  # 所有因子
scaler_raw = StandardScaler()  # 原始特征标准化
scaler_factors = StandardScaler()  # 因子标准化

df_factors[feature_cols] = scaler_raw.fit_transform(df_factors[feature_cols])  # 标准化原始特征
df_factors[factor_cols] = scaler_factors.fit_transform(df_factors[factor_cols])  # 标准化所有因子

# 保存标准化参数
import joblib
joblib.dump(scaler_raw, os.path.join(save_dir, 'scaler_raw.save'))  # 保存原始特征标准化器
joblib.dump(scaler_factors, os.path.join(save_dir, 'scaler_factors.save'))  # 保存因子标准化器

# 删除NaN和空白行
df_factors = df_factors.replace([np.inf, -np.inf], np.nan).dropna()  # 删除无效行

# 8. 保存最终因子文件
df_factors.to_excel(os.path.join(save_dir, 'TSLA_5m_clean_factors.xlsx'), index=False)  # 保存

# 9. 模仿Features_Engineering3_Full_Factors_ReadyForUse.py做进一步处理
# 这里只做简单处理，移除NaN和空白，描述性统计
df_final = df_factors.copy()
before_rows = len(df_final)
df_final = df_final.replace([np.inf, -np.inf], np.nan).dropna()
after_rows = len(df_final)
print(f'移除无效行数: {before_rows - after_rows}')  # 打印移除行数
print('描述性统计:\n', df_final.describe().T)  # 打印描述性统计
print('前5行:\n', df_final.head())  # 打印前5行

# 可视化
df_final.hist(figsize=(20, 15))
plt.tight_layout()
plt.savefig(os.path.join(save_dir, 'feature_hist.png'))  # 保存特征分布图

# 10. 保存最终可用于机器学习的文件
df_final.to_excel(os.path.join(save_dir, 'TSLA_5m_clean_factors_winsorized.xlsx'), index=False)  # 保存

# 11. 生成迷你训练集
df_final.head(5000).to_excel(os.path.join(save_dir, 'TSLA_5m_clean_factors_winsorized_5k.xlsx'), index=False)  # 只保留5000行

print('全部处理完成，文件已保存。')  # 处理完成提示
