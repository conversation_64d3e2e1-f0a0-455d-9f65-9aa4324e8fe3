#0. 导入依赖包
import yfinance as yf
import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta

import os
import talib
import sys

from dotenv import load_dotenv, find_dotenv
# Find the .env file in the parent directory
dotenv_path = find_dotenv("../../.env")
# Load it explicitly
load_dotenv(dotenv_path)

# Add the parent directory to the sys.path list
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))

from data_processing import load_data_multi_year, flatten_yf_columns, standardize_columns
from plotting import plot_results
from strategy.buy_and_hold import BuyAndHoldStrategy
from back_test import run_backtest
import backtrader as bt

# 设置显示选项
pd.set_option('display.float_format', lambda x: '%.4f' % x)
# 绘图风格（可选）
plt.style.use('seaborn-v0_8-bright')
# 设置中文显示
plt.rcParams['font.sans-serif'] = ['PingFang HK']
plt.rcParams['axes.unicode_minus'] = False

import random
# 固定全局随机种子
SEED = 42
random.seed(SEED)
np.random.seed(SEED)  


import torch
def seed_everything(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)

    # 如果使用 GPU，需要额外设置
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)

    # 保证 cuDNN 的确定性
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

seed_everything(SEED)






# 1. 数据准备与特征工程

# Read the Excel file
# df_5min = pd.read_excel("cache/TSLA_5min_merged_raw.xlsx")
# df_day = pd.read_excel("cache/TSLA_day.xlsx")

print("\n缓存数据预览: ")
# print(df_5min.head())

factors_list = []

for trading_date, group in df_5min.groupby(df_5min.index.date):
    # 这里 group 是当天的所有5分钟bar数据
    day_open = group.iloc[0]['open']
    day_close = group.iloc[-1]['close']
    day_high = group['high'].max()
    day_low = group['low'].min()
    total_vol = group['volume'].sum()

    # ~~~~~~~~~ 1) VAWP及偏离 ~~~~~~~~~
    # 最常见的 VWAP 计算方法：
    # 对于每个 5 分钟 bar，先定义 bar 的"价格"可用 (high+low+close)/3 或 (open+high+low+close)/4 等值近似。
    # 然后加权求和除以总成交量。

    group['typical_price'] = (group['high'] + group['low'] + group['close']) / 3
    sum_price_vol = (group['typical_price'] * group['volume']).sum()
    daily_vwap = sum_price_vol / total_vol if total_vol > 0 else np.nan

    # 收盘相对 VAWP 的偏离
    # 例如: (close - VAWP) / VAWP
    close_vawp_dev = (day_close - daily_vwap) / daily_vwap if daily_vwap and not np.isnan(daily_vwap) else np.nan

    # ~~~~~~~~~ 2) Extreme Bar 统计 ~~~~~~~~~
    # 例如定义 "极端波动" 阈值 1%（可以根据需要修改为 2%，或基于均值/标准差等方式动态设置）
    threshold = 0.01
    group['is_extreme'] = group['ret_5min'].abs() > threshold
    extreme_bar_count = group['is_extreme'].sum()

    # ~~~~~~~~~ 3) 日内新高 / 新低次数 ~~~~~~~~~
    # 思路: 对 high、low 分别做 cummax、cummin，判断当下 bar 是否突破之前最高/最低
    group['cum_max_high'] = group['high'].cummax()
    # 当本 bar 的 high > 之前任意 bar 的最高 => 刷新新高
    # 注意 shift(1) 避免把本 bar 自己包含进去
    group['is_new_high'] = group['high'] > group['cum_max_high'].shift(1).fillna(-np.inf)
    new_high_count = group['is_new_high'].sum()

    group['cum_min_low'] = group['low'].cummin()
    group['is_new_low'] = group['low'] < group['cum_min_low'].shift(1).fillna(np.inf)
    new_low_count = group['is_new_low'].sum()

    # ~~~~~~~~~ 4) 日内最高价 / 最低价出现的时间 ~~~~~~~~~
    # 定位最高价所在 bar、最低价所在 bar
    idx_high = group['high'].idxmax()
    idx_low = group['low'].idxmin()
    # 取对应时间（直接用索引）
    high_time = idx_high
    low_time = idx_low

    time_of_high = high_time.hour + high_time.minute / 60.0
    time_of_low = low_time.hour + low_time.minute / 60.0

    group['ii_bar'] = ((2 * group['close'] - group['high'] - group['low']) / ((group['high'] - group['low']).replace(0, np.nan) )) * group['volume']

    ii_sum = group['ii_bar'].sum()
    # Intraday_intensity_index (日级别)
    intraday_intensity = ii_sum / total_vol if total_vol > 0 else np.nan

    realized_vol = np.sqrt(np.sum(group['ret_5min']**2))
    intraday_range_pct = (day_high - day_low) / day_open * 100     #高低点偏幅占开盘价的百分比
    intraday_ret = day_close - day_open - 1           # XXXXgap

    # 切分前后半段（前面实例）
    half_n = len(group) // 2
    group_am = group.iloc[:half_n]
    group_pm = group.iloc[half_n:]
    am_ret = group_am.iloc[-1]['close']/day_open - 1 if len(group_am) else np.nan
    pm_ret = day_close/group_am.iloc[-1]['close'] - 1 if len(group_am) else np.nan
    am_vol_ratio = group_am['volume'].sum() / total_vol if total_vol else 0
    pm_vol_ratio = group_pm['volume'].sum() / total_vol if total_vol else 0

    #收集结果
    factors_list.append({
        'date': trading_date,
        'daily_vwap': daily_vwap,
        'close_vwap_dev': close_vawp_dev,
        'extreme_bar_count': extreme_bar_count,
        'new_high_count': new_high_count,
        'new_low_count': new_low_count,
        'time_of_high': time_of_high,
        'time_of_low': time_of_low,
        'intraday_intensity': intraday_intensity,
        'realized_vol': realized_vol,
        'intraday_range_pct': intraday_range_pct,
        'intraday_ret': intraday_ret,
        'am_ret': am_ret,
        'pm_ret': pm_ret,
        'am_vol_ratio': am_vol_ratio,
        'pm_vol_ratio': pm_vol_ratio
    })

# 整理并与日频合并
df_factors = pd.DataFrame(factors_list)
df_factors['date'] = pd.to_datetime(df_factors['date'])
df_day['date'] = pd.to_datetime(df_day['datetime'])
df_merged = pd.merge(df_day, df_factors, on='date', how='left')
df_merged.sort_values('date', inplace=True)

print(df_merged.head(10))


# 保存融合后的数据到 Excel 文件
output_path = "cache/TSLA_merged_factors.xlsx"
df_merged.to_excel(output_path, index=False)
print(f"融合后的数据已保存到: {output_path}")

# 检查每一列是否有缺失值，统计特征数和样本数
print("\n每列缺失值统计：")
print(df_merged.isnull().sum())
print(f"\n特征（列）数: {df_merged.shape[1]}")
print(f"样本（行）数: {df_merged.shape[0]}")

# 统一datetime和date格式为YYYY-MM-DD
df_merged['datetime'] = pd.to_datetime(df_merged['datetime']).dt.strftime('%Y-%m-%d')
df_merged['date'] = pd.to_datetime(df_merged['date']).dt.strftime('%Y-%m-%d')

# 检查每行datetime和date是否一致
not_match = df_merged[df_merged['datetime'] != df_merged['date']]
print("\n前10个datetime和date不一致的行：")
print(not_match.head(10))

# 如需保存格式化后的数据，可再次保存
df_merged.to_excel(output_path, index=False)









# 加入日间因子，布林带、RIS等动量、反转因子，进一步增加features，并且融合到之前的excel文件中。

import pandas as pd
import talib

# 1. 读取原始融合表
df_merged = pd.read_excel("cache/TSLA_merged_factors.xlsx")
df_merged['date'] = pd.to_datetime(df_merged['date'])

# 2. 复制一份用于生成新因子
df = df_merged.copy()

# 3. 生成新因子
df['Volume'] = df['Volume'].astype(str).str.replace(',', '').astype(float)
df['momentum_5_lower'] = df['Close'] / df['Close'].shift(5) - 1
df['vol_ratio_lower'] = (df['Volume'].rolling(5).mean()) / (df['Volume'].rolling(10).mean()) - 1
df['RSI_14_lower'] = talib.RSI(df['Close'], timeperiod=14)
upper, middle, lower = talib.BBANDS(df['Close'], timeperiod=20, nbdevup=2, nbdevdn=2, matype=0)
df['BB_upper'] = upper
df['BB_middle'] = middle
df['BB_lower'] = lower
df['momentum_3_lower'] = df['Close'] / df['Close'].shift(3) - 1
df['momentum_10_lower'] = df['Close'] / df['Close'].shift(10) - 1
df['reversal_1'] = - (df['Close'].pct_change(1))
df['reversal_3'] = - (df['Close'] / df['Close'].shift(3) - 1)

# 4. 只保留新因子和date列
factor_cols = [
    'date', 'momentum_3_lower', 'momentum_5_lower', 'momentum_10_lower',
    'reversal_1', 'reversal_3', 'vol_ratio_lower', 'RSI_14_lower', 'BB_upper', 'BB_middle', 'BB_lower'
]
df_factors = df[factor_cols]

# 5. 合并到原始表，按date左连接，保证顺序和行数不变
df_final = pd.merge(df_merged, df_factors, on='date', how='left', suffixes=('', '_new'))

# 6. 保存新表格
df_final['future_ret_1d'] = df_final['Close'].shift(-1) / df_final['Close'] - 1
df_final.to_excel("cache/TSLA_5min_interday_merged_factors.xlsx", index=False)
print("融合完成，已保存到 cache/TSLA_5min_interday_merged_factors.xlsx")

# 读取融合后的新表格
df = pd.read_csv("/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_5min_interday_merged_factors_forMLP.csv")

# 打印所有列名
print("所有列名：")
print(df.columns.tolist())

# 正确提取特征列（排除标签）
non_feature_cols = ['date', 'datetime', 'future_ret_1d']
feature_cols = [col for col in df.columns if col not in non_feature_cols and pd.api.types.is_numeric_dtype(df[col])]

print(f"\n特征（features）数量: {len(feature_cols)}")
print("特征列名：")
print(feature_cols)

# 去除缺失
df = df.dropna(subset=feature_cols + ['future_ret_1d'])

# 按比例分割
train_idx = int(len(df) * 0.6)
valid_idx = int(len(df) * 0.8)
train_data = df.iloc[:train_idx]
val_data = df.iloc[train_idx:valid_idx]
test_data = df.iloc[valid_idx:]

X_train = train_data[feature_cols].values
y_train = train_data['future_ret_1d'].values
X_val = val_data[feature_cols].values
y_val = val_data['future_ret_1d'].values
X_test = test_data[feature_cols].values
y_test = test_data['future_ret_1d'].values

# 4. 标准化（如有需要）
from sklearn.preprocessing import StandardScaler
scaler = StandardScaler()
X_train = scaler.fit_transform(X_train)
X_val = scaler.transform(X_val)
X_test = scaler.transform(X_test)

# 4. 模型训练与超参数优化
features = feature_cols
X_train = train_data[features].values
y_train = train_data['future_ret_1d'].values
X_val = val_data[features].values
y_val = val_data['future_ret_1d'].values
X_test = test_data[features].values
y_test = test_data['future_ret_1d'].values

# 4.1 训练线性模型

import copy
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import ParameterGrid
from sklearn.pipeline import Pipeline

# 假设 X_train, y_train, X_valid, y_valid, X_test, y_test 已经定义

######################################
# 1. 建立 Pipeline（先缩放，再线性回归）
######################################
pipeline_lr = Pipeline([
    ('lr', LinearRegression())
])

######################################
# 2. 定义线性模型的超参数搜索范围
#    这里只调节 fit_intercept 参数，你可以根据需要添加其他模型或参数
######################################
param_grid_lr = {
    'lr__fit_intercept': [True, False]
}

######################################
# 3. 遍历所有参数组合，寻找最佳线性模型（在验证集上评估）
######################################
best_score_lr = float('-inf')
best_params_lr = None
best_pipeline_lr = None

for params in ParameterGrid(param_grid_lr):
    pipeline_lr.set_params(**params)
    pipeline_lr.fit(X_train, y_train)

    # 在验证集上进行预测和评估
    valid_pred_lr = pipeline_lr.predict(X_val)
    valid_r2_lr = r2_score(y_val, valid_pred_lr)

    if valid_r2_lr > best_score_lr:
        best_score_lr = valid_r2_lr
        best_params_lr = params
        # 复制当前 pipeline，保存最佳模型
        best_pipeline_lr = copy.deepcopy(pipeline_lr)
        print("更新：", best_score_lr, best_params_lr)

print("最佳参数：", best_params_lr)

######################################
# 4. 使用最佳模型在训练集和测试集上评估
######################################
y_pred_train_lr = best_pipeline_lr.predict(X_train)
y_pred_test_lr = best_pipeline_lr.predict(X_test)

train_mse_lr = mean_squared_error(y_train, y_pred_train_lr)
test_mse_lr = mean_squared_error(y_test, y_pred_test_lr)
train_r2_lr = r2_score(y_train, y_pred_train_lr)
test_r2_lr = r2_score(y_test, y_pred_test_lr)

print("==== 线性模型 - 训练集 ====")
print("MSE:", train_mse_lr)
print("R2: ", train_r2_lr)

print("==== 线性模型 - 测试集 ====")
print("MSE:", test_mse_lr)
print("R2: ", test_r2_lr)

# 查看训练后的回归系数和截距
print("Coefficients:", best_pipeline_lr.named_steps['lr'].coef_)
print("Intercept:", best_pipeline_lr.named_steps['lr'].intercept_)


# 4.2 训练随机森林
import copy
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import ParameterGrid
from sklearn.pipeline import Pipeline

# 假设 X_train, y_train, X_valid, y_valid, X_test, y_test 已经定义
# 同时 features 变量也定义了各特征名称

######################################
# 1. 建立 Pipeline（可选标准化，对 RF 来说不一定必要，但便于与其他模型比较）
######################################
pipeline_rf = Pipeline([
    ('rf', RandomForestRegressor(random_state=42))
])

######################################
# 2. 定义 RF 的超参数搜索范围
######################################
param_grid_rf = {
    'rf__n_estimators': [1000],
    'rf__max_depth': [3, 5, 10, 20],
    'rf__min_samples_split': [2, 5, 10, 20],
    'rf__min_samples_leaf': [1, 2, 4, 8],
    'rf__max_features': [0.1, 0.3, 'sqrt']
}

######################################
# 3. 遍历所有参数组合，在验证集上寻找最佳 RF 模型
######################################
best_score_rf = float('-inf')
best_params_rf = None
best_pipeline_rf = None

for params in ParameterGrid(param_grid_rf):
    # 设置参数并训练模型
    pipeline_rf.set_params(**params)
    pipeline_rf.fit(X_train, y_train)

    # 在验证集上进行预测并计算 R2 得分
    valid_pred_rf = pipeline_rf.predict(X_val)
    valid_r2_rf = r2_score(y_val, valid_pred_rf)

    if valid_r2_rf > best_score_rf:
        best_score_rf = valid_r2_rf
        best_params_rf = params
        best_pipeline_rf = copy.deepcopy(pipeline_rf)
        print("更新：", best_score_rf, best_params_rf)

print("最佳参数：", best_params_rf)

######################################
# 4. 使用最佳模型在训练集和测试集上评估
######################################
y_pred_train_rf = best_pipeline_rf.predict(X_train)
y_pred_test_rf = best_pipeline_rf.predict(X_test)

train_mse_rf = mean_squared_error(y_train, y_pred_train_rf)
test_mse_rf = mean_squared_error(y_test, y_pred_test_rf)
train_r2_rf = r2_score(y_train, y_pred_train_rf)
test_r2_rf = r2_score(y_test, y_pred_test_rf)

print("==== 随机森林 - 训练集 ====")
print("MSE:", train_mse_rf)
print("R2 :", train_r2_rf)

print("==== 随机森林 - 测试集 ====")
print("MSE:", test_mse_rf)
print("R2 :", test_r2_rf)

######################################
# 5. 查看特征重要性
######################################
feature_importances = best_pipeline_rf.named_steps['rf'].feature_importances_
for f, imp in zip(features, feature_importances):
    print(f"Feature: {f}, Importance: {imp:.4f}")

# 按重要性排序输出
sorted_idx = np.argsort(feature_importances)[::-1]
print("\nSorted Feature Importances:")
for idx in sorted_idx:
    print(f"{features[idx]} -> {feature_importances[idx]:.4f}")

# 3.3 训练XGBoost
import copy
import numpy as np
from xgboost import XGBRegressor
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import ParameterGrid
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler

# 假设 X_train, y_train, X_valid, y_valid, X_test, y_test 已经定义
# 同时 features 列表也定义了各特征名称

######################################
# 1. 建立 Pipeline（XGBoost 通常不需要标准化）
######################################
pipeline_xgb = Pipeline([
    ('xgb', XGBRegressor(random_state=42, verbosity=0))
])

######################################
# 2. 定义 XGBoost 的超参数搜索范围
######################################
param_grid_xgb = {
    'xgb__n_estimators': [100, 500, 1000],
    'xgb__learning_rate': [0.01, 0.05, 0.1],
    'xgb__max_depth': [3, 5, 20],
    'xgb__subsample': [1.0, 0.8]
}

######################################
# 3. 遍历所有参数组合，在验证集上寻找最佳 XGBoost 模型
######################################
best_score_xgb = float('-inf')
best_params_xgb = None
best_pipeline_xgb = None

for params in ParameterGrid(param_grid_xgb):
    pipeline_xgb.set_params(**params)
    pipeline_xgb.fit(X_train, y_train)

    # 在验证集上进行预测并计算 R² 得分
    valid_pred_xgb = pipeline_xgb.predict(X_val)
    valid_r2_xgb = r2_score(y_val, valid_pred_xgb)

    if valid_r2_xgb > best_score_xgb:
        best_score_xgb = valid_r2_xgb
        best_params_xgb = params
        best_pipeline_xgb = copy.deepcopy(pipeline_xgb)
        print("更新：", best_score_xgb, best_params_xgb)

print("最佳参数：", best_params_xgb)

######################################
# 4. 使用最佳模型在训练集和测试集上评估
######################################
y_pred_train_xgb = best_pipeline_xgb.predict(X_train)
y_pred_test_xgb = best_pipeline_xgb.predict(X_test)

train_mse_xgb = mean_squared_error(y_train, y_pred_train_xgb)
test_mse_xgb = mean_squared_error(y_test, y_pred_test_xgb)
train_r2_xgb = r2_score(y_train, y_pred_train_xgb)
test_r2_xgb = r2_score(y_test, y_pred_test_xgb)

print("==== XGBoost - 训练集 ====")
print("MSE:", train_mse_xgb)
print("R2: ", train_r2_xgb)

print("==== XGBoost - 测试集 ====")
print("MSE:", test_mse_xgb)
print("R2: ", test_r2_xgb)

######################################
# 5. 查看特征重要性
######################################
feature_importances_xgb = best_pipeline_xgb.named_steps['xgb'].feature_importances_
for f, imp in zip(features, feature_importances_xgb):
    print(f"Feature: {f}, Importance: {imp:.4f}")

# 按重要性排序输出
sorted_idx_xgb = np.argsort(feature_importances_xgb)[::-1]
print("\nSorted Feature Importances (XGBoost):")
for idx in sorted_idx_xgb:
    print(f"{features[idx]} -> {feature_importances_xgb[idx]:.4f}")

# 3.4 训练MLP（改进版）
import copy
import numpy as np
from sklearn.neural_network import MLPRegressor
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import ParameterGrid
from sklearn.pipeline import Pipeline

######################################
# 1. 建立 Pipeline（MLP回归）
######################################
pipeline_mlp = Pipeline([
    ('mlp', MLPRegressor(
        hidden_layer_sizes=(16, 8),  # 更小的网络结构
        alpha=1.0,  # 强正则化
        learning_rate='adaptive',  # 自适应学习率
        learning_rate_init=0.001,  # 较小的初始学习率
        max_iter=5000,  # 增加迭代次数
        early_stopping=True,  # 早停机制
        validation_fraction=0.2,  # 验证集比例
        random_state=42
    ))
])

######################################
# 2. 训练MLP模型（直接在标准化后的数据上训练）
######################################
scaler = StandardScaler()
X_train = scaler.fit_transform(X_train)
X_val = scaler.transform(X_val)
X_test = scaler.transform(X_test)

pipeline_mlp.fit(X_train, y_train)

# 定义 best_pipeline_mlp 为训练好的 pipeline_mlp
best_pipeline_mlp = pipeline_mlp

######################################
# 3. 在训练集、验证集和测试集上评估
######################################
y_pred_train_mlp = best_pipeline_mlp.predict(X_train)
y_pred_val_mlp = best_pipeline_mlp.predict(X_val)
y_pred_test_mlp = best_pipeline_mlp.predict(X_test)

train_mse_mlp = mean_squared_error(y_train, y_pred_train_mlp)
val_mse_mlp = mean_squared_error(y_val, y_pred_val_mlp)
test_mse_mlp = mean_squared_error(y_test, y_pred_test_mlp)
train_r2_mlp = r2_score(y_train, y_pred_train_mlp)
val_r2_mlp = r2_score(y_val, y_pred_val_mlp)
test_r2_mlp = r2_score(y_test, y_pred_test_mlp)

print("==== MLP - 训练集 ====")
print("MSE:", train_mse_mlp)
print("R2: ", train_r2_mlp)

print("==== MLP - 验证集 ====")
print("MSE:", val_mse_mlp)
print("R2: ", val_r2_mlp)

print("==== MLP - 测试集 ====")
print("MSE:", test_mse_mlp)
print("R2: ", test_r2_mlp)

# 4. 模型集成与权重优化（用凸优化）
import numpy as np
import cvxpy as cp
from sklearn.metrics import mean_squared_error, r2_score


def optimize_weights_constrained(
        models,
        X_val,
        y_val,
        sum_to_1=True,
        nonnegative=True,
        alpha_l1=0.0,
        alpha_l2=0.0,
        verbose=True
):
    """
    用凸优化方式在验证集上寻找一组最优权重，使得加权后的预测最小化 MSE
    （或等效地最大化 R²），并可选地加入 L1/L2 正则，还可选地约束权重和=1、权重>=0。

    参数：
    - models: 传入已训练好的各个模型列表
    - X_val, y_val: 验证集特征和目标
    - sum_to_1: Boolean, 若为 True，则加上 sum(w) == 1 的约束
    - nonnegative: Boolean, 若为 True，则加上 w >= 0 的约束
    - alpha_l1, alpha_l2: L1、L2 正则化系数
    - verbose: 是否打印约束求解的一些信息

    返回：
    - w_opt: 优化得到的权重向量 (numpy array)
    - score_r2: 用该权重在验证集上得到的 R² 分数
    """
    # 1) 先得到在验证集上的预测矩阵 predictions: shape (N, M)
    predictions = np.column_stack([model.predict(X_val) for model in models])
    N, M = predictions.shape

    # 2) 定义优化变量 w: 大小 M
    #    如果 nonnegative=True，则需要 w >= 0
    if nonnegative:
        w = cp.Variable(M, nonneg=True)
    else:
        w = cp.Variable(M)

    # 3) 定义约束列表 constraints
    constraints = []
    if sum_to_1:
        # sum(w) == 1
        constraints.append(cp.sum(w) == 1)

    # 4) 定义目标函数（最小化 MSE + 正则项）
    #    MSE 可以写成 sum_squares(y_val - predictions @ w)
    residual = y_val - predictions @ w
    obj_mse = cp.sum_squares(residual)

    # 若要加 L1 正则：alpha_l1 * ||w||_1
    # 若要加 L2 正则：alpha_l2 * ||w||_2^2
    obj_reg = 0
    if alpha_l1 > 0:
        obj_reg += alpha_l1 * cp.norm1(w)
    if alpha_l2 > 0:
        obj_reg += alpha_l2 * cp.norm2(w) ** 2

    # 最终目标：Minimize(MSE + 正则)
    objective = cp.Minimize(obj_mse + obj_reg)

    # 5) 构建并求解凸优化问题
    problem = cp.Problem(objective, constraints)
    result = problem.solve(verbose=verbose)

    # 6) 拿到最优权重 w_opt
    w_opt = w.value
    # 计算该组合在验证集上的 r2_score
    y_val_pred = predictions @ w_opt
    score_r2 = r2_score(y_val, y_val_pred)

    if verbose:
        print(f"Optimal objective (MSE + reg) = {problem.value:.6f}")
        print("Optimized weights:", w_opt)
        print(f"sum of weights = {w_opt.sum():.4f}")
        print(f"R2 on validation set = {score_r2:.4f}")

    return w_opt, score_r2


# =======================
# 使用示例
# =======================
# 假设你已经在训练集上训练好了 4 个模型：models = [m1, m2, m3, m4]
# 并且有验证集 X_val, y_val

# 比如我们想：
#   - 权重和 = 1
#   - 权重 >= 0
#   - 加一点儿 L2 正则以防止极端权重
#   - 不打印太详细的求解日志 => verbose=False
w_constrained, r2_constrained = optimize_weights_constrained(
    models=[best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb, best_pipeline_mlp],
    X_val=X_val,
    y_val=y_val,
    sum_to_1=True,
    nonnegative=True,
    alpha_l1=0.0,
    alpha_l2=1e-3,
    verbose=False
)

print("得到的约束权重 =", [f"{num:0.2f}" for num in w_constrained])
print("验证集 R² =", r2_constrained)

# 1. 得到测试集上各模型的预测矩阵
predictions_test = np.column_stack([model.predict(X_test) for model in
                                    [best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb,
                                     best_pipeline_mlp]])

# 2. 利用之前优化得到的权重 w_constrained 对测试集预测进行加权组合
y_test_pred = predictions_test @ w_constrained

# 3. 计算测试集上的 R² 分数
r2_test = r2_score(y_test, y_test_pred)
print("测试集 R² =", r2_test)


# 5. Emsemble策略实现与回测
import backtrader as bt


# 自定义成交量指标，把成交量数据单独显示在子图中
class MyVolumeIndicator(bt.Indicator):
    """
    简单示例，把data的volume包装成一个单独的子图指标。
    """
    lines = ('vol',)
    plotinfo = dict(subplot=True, plotname='Volume')  # 让它单独开子图

    def __init__(self):
        self.lines.vol = self.data.volume


class MLEnsembleStrategy(bt.Strategy):
    params = (
        ('target_percent', 0.98),  # 目标仓位百分比
    )

    def __init__(self):
        # 使用预计算的集成预测值
        if hasattr(self.data, 'ensemble_pred'):
            self.ensemble_pred = self.data.ensemble_pred
        else:
            print("Warning: ensemble_pred not found in data, using default value 0")
            self.ensemble_pred = 0
        
        self.value_history_dates = []
        self.value_history_values = []

    def next(self):
        # 获取当前的集成预测值
        pred_ret = self.ensemble_pred[0] if hasattr(self.ensemble_pred, '__getitem__') else self.ensemble_pred
        
        # 获取当前持仓状态
        current_position = self.getposition().size

        if pred_ret > 0 and current_position == 0:
            # 只有当当前没有仓位时，才执行买入
            self.order_target_percent(target=self.p.target_percent)
            print(f"{self.datas[0].datetime.date(0)} => BUY signal, pred_ret={pred_ret:.6f}")
        elif pred_ret <= 0 and current_position > 0:
            # 只有当当前有仓位时，才执行卖出
            self.order_target_percent(target=0.0)
            print(f"{self.datas[0].datetime.date(0)} => SELL signal, pred_ret={pred_ret:.6f}")

        # 记录资产净值历史
        dt = self.data.datetime.date(0)
        self.value_history_dates.append(dt)
        self.value_history_values.append(self.broker.getvalue())


# 定义回测所需的 ticker 和时间区间
ticker = "TSLA"                              # 字符串形式
# 假设 test_data 已经包含 'date' 列且为 datetime 类型
start_date = test_data['date'].iloc[0]       # 第一条记录日期
end_date   = test_data['date'].iloc[-1]      # 最后一条记录日期

# ———— edit_1：构造回测用 DataFrame ————
# 1) 先把 test_data 的 'date' 变成 datetime，并设为 index
df_bt = test_data.copy()
df_bt['date'] = pd.to_datetime(df_bt['date'])
df_bt.set_index('date', inplace=True)

# 2) 重命名列为 backtrader 默认识别的 open, high, low, close, volume
df_bt.rename(columns={
    'Open':   'open',
    'High':   'high',
    'Low':    'low',
    'Close':  'close',
    'Volume': 'volume'
}, inplace=True)

# 3) 添加 ensemble_pred 列（ML 模型加权后的预测值）
df_bt['ensemble_pred'] = y_test_pred

# 4) 只保留 backtrader 会用到的几列
df_bt = df_bt[['open','high','low','close','volume','ensemble_pred']]
# ———— edit_1 end ————

# 调用回测
ml_ensemble_result, ml_ensemble_cerebro = run_backtest(
    ticker=ticker,
    df=df_bt,
    start_date=start_date,
    end_date=end_date,
    strategy=MLEnsembleStrategy,
    initial_cash=100000,
    strategy_params={'target_percent': 0.98},
    print_log=True,
)

# 如需进一步查看回测结果或可视化结果，可进一步操作 ml_ensemble_cerebro 或 ml_ensemble_result

# ===== 猴子补丁：为 numpy 添加 bool8 和 object 属性 =====
import numpy as np

if not hasattr(np, 'bool8'):
    np.bool8 = np.bool_  # 使用 numpy 自带的 bool_ 类型
if not hasattr(np, 'object'):
    np.object = object  # 兼容 backtrader_plotting 的引用
plot_results(ml_ensemble_cerebro)

# 6. 比较策略和Buy&Hold
results = ml_ensemble_cerebro.run()  # cerebro.run() 返回一个列表，每个元素是一个策略实例
ml_strategy_instance = results[0]  # 如果你只有一个策略，就取第一个

results = bh_cerebro.run()
bh_strategy_instance = results[0]

import matplotlib.pyplot as plt

plt.figure(figsize=(12, 6))
plt.plot(ml_strategy_instance.value_history_dates, ml_strategy_instance.value_history_values,
         label='集成模型')
plt.plot(bh_strategy_instance.value_history_dates, bh_strategy_instance.value_history_values,
         label='买入并持有')
plt.xlabel('时间')
plt.ylabel('资产净值')
plt.title('回报曲线对比')
plt.legend()
plt.show()

# 用 tensorflow.keras 构建一个更灵活的 MLP
import tensorflow as tf
from tensorflow.keras import layers, regularizers, callbacks, optimizers

def build_huber_model(input_dim):
    return tf.keras.Sequential([
        layers.Input(shape=(input_dim,)),
        layers.Dense(128, activation='relu',
                     kernel_regularizer=regularizers.l2(1e-4)),
        layers.BatchNormalization(),
        layers.Dropout(0.4),
        layers.Dense(64, activation='relu',
                     kernel_regularizer=regularizers.l2(1e-4)),
        layers.BatchNormalization(),
        layers.Dropout(0.3),
        layers.Dense(32, activation='relu',
                     kernel_regularizer=regularizers.l2(1e-4)),
        layers.BatchNormalization(),
        layers.Dropout(0.2),
        layers.Dense(1)
    ])

input_dim = X_train.shape[1]
model = build_huber_model(input_dim)
model.compile(optimizer=optimizers.Adam(learning_rate=1e-3),
              loss=tf.keras.losses.Huber(), 
              metrics=['RootMeanSquaredError'])

es   = callbacks.EarlyStopping(patience=20, restore_best_weights=True)
rlr  = callbacks.ReduceLROnPlateau(patience=10, factor=0.5, min_lr=1e-5)
history = model.fit(
    X_train, y_train,
    validation_data=(X_val, y_val),
    epochs=500,
    batch_size=64,
    callbacks=[es, rlr],
    verbose=2
)

val_pred = model.predict(X_val)
print("验证集 R² =", r2_score(y_val, val_pred))

from scikeras.wrappers import KerasRegressor
from sklearn.model_selection import RandomizedSearchCV
from tensorflow.keras import regularizers

def build_fn(hidden_sizes=(64,32), dropout=0.3, l2=1e-4):
    model = tf.keras.Sequential()
    model.add(layers.Input(shape=(input_dim,)))
    for h in hidden_sizes:
        model.add(layers.Dense(h, activation='relu',
                               kernel_regularizer=regularizers.l2(l2)))
        model.add(layers.BatchNormalization())
        model.add(layers.Dropout(dropout))
    model.add(layers.Dense(1))
    model.compile(optimizer='adam', loss='mse')
    return model

keras_reg = KerasRegressor(model=build_fn, epochs=200, validation_split=0.2, verbose=0)
param_dist = {
    'hidden_sizes': [(64,32),(128,64,32),(32,16)],
    'dropout': [0.2,0.3,0.4],
    'l2': [1e-4,1e-3],
    'batch_size': [32,64,128]
}

search = RandomizedSearchCV(
    keras_reg,
    param_distributions=param_dist,
    n_iter=20,
    cv=3,
    verbose=2,
    random_state=SEED
)
search.fit(X_train, y_train)
print("最好参数：", search.best_params_)
print("CV 平均分：", search.best_score_)

from sklearn.decomposition import PCA
from sklearn.pipeline import Pipeline

pipe = Pipeline([
    ('pca', PCA(n_components=0.95, random_state=SEED)),
    ('mlp', tf.keras.wrappers.scikit_learn.KerasRegressor(
        model=build_fn, epochs=200, batch_size=64, verbose=0))
])
pipe.fit(X_train, y_train)
val_pred = pipe.predict(X_val)
print("降维后验证 R² =", r2_score(y_val, val_pred))

# —————— 进阶整合版 MLP（Optuna + Keras） ——————
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import QuantileTransformer, StandardScaler
import optuna
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, r2_score, roc_curve, auc
import tensorflow as tf
from tensorflow.keras import layers, regularizers, callbacks, optimizers

# （1）预处理：先分位数->正态，再标准化
preprocessor = Pipeline([
    ('quantile', QuantileTransformer(output_distribution='normal', random_state=SEED)),
    ('scaler'  , StandardScaler())
])
X_train_pt = preprocessor.fit_transform(X_train)
X_val_pt   = preprocessor.transform(  X_val)
X_test_pt  = preprocessor.transform(  X_test)

# 方向分类用：把连续回报转成0/1
y_val_bin  = (y_val > 0).astype(int)
y_test_bin = (y_test > 0).astype(int)

# （2）构建模型的工厂函数
def build_model(input_dim: int, trial: optuna.Trial):
    # 搜索空间
    n_layers     = trial.suggest_int("n_layers", 1, 4)
    hidden_units = [trial.suggest_int(f"n_units_l{i}", 16, 256, step=16)
                    for i in range(n_layers)]
    dropout_rate = trial.suggest_float("dropout_rate", 0.0, 0.5)
    l2_coef      = trial.suggest_loguniform("l2_coef", 1e-6, 1e-2)
    lr           = trial.suggest_loguniform("lr",      1e-4, 1e-2)
    clipnorm     = trial.suggest_float("clipnorm", 0.5, 5.0)
    batch_size   = trial.suggest_categorical("batch_size", [32, 64, 128])

    # 搭建网络
    model = tf.keras.Sequential()
    model.add(layers.Input(shape=(input_dim,)))
    for h in hidden_units:
        model.add(layers.Dense(h,
                               activation='relu',
                               kernel_regularizer=regularizers.l2(l2_coef)))
        model.add(layers.BatchNormalization())
        model.add(layers.Dropout(dropout_rate))
    model.add(layers.Dense(1))
    # Adam + Huber + 梯度裁剪
    opt = optimizers.Adam(learning_rate=lr, clipnorm=clipnorm)
    model.compile(optimizer=opt, loss='huber')
    return model

# （3）Optuna 目标函数：返回验证集 R²
def objective(trial: optuna.Trial):
    model = build_model(X_train_pt.shape[1], trial)
    es = callbacks.EarlyStopping(patience=10, restore_best_weights=True)
    # fit
    model.fit(
        X_train_pt, y_train,
        validation_data=(X_val_pt, y_val),
        epochs=100,
        batch_size=trial.params["batch_size"],
        callbacks=[es],
        verbose=0
    )
    # 验证集预测
    y_pred_val = model.predict(X_val_pt).flatten()
    return r2_score(y_val, y_pred_val)

# （4）执行超参数搜索
study = optuna.create_study(direction="maximize")
study.optimize(objective, n_trials=30, show_progress_bar=True)

print(">>> Optuna 最佳参数：", study.best_trial.params)
print(">>> 验证集 R²(max)：", study.best_value)

# （5）用最佳参数重训练模型（train+val）
best_p = study.best_trial.params
hidden_units = [best_p[f"n_units_l{i}"] for i in range(best_p["n_layers"])]
dropout_rate = best_p["dropout_rate"]
l2_coef      = best_p["l2_coef"]
lr           = best_p["lr"]
clipnorm     = best_p["clipnorm"]
batch_size   = best_p["batch_size"]

# 重建最终模型
final_model = tf.keras.Sequential()
final_model.add(layers.Input(shape=(X_train_pt.shape[1],)))
for h in hidden_units:
    final_model.add(layers.Dense(h,
                                 activation='relu',
                                 kernel_regularizer=regularizers.l2(l2_coef)))
    final_model.add(layers.BatchNormalization())
    final_model.add(layers.Dropout(dropout_rate))
final_model.add(layers.Dense(1))
opt_final = optimizers.Adam(learning_rate=lr, clipnorm=clipnorm)
final_model.compile(optimizer=opt_final, loss='huber')

# 合并 train+val 训练
X_trval = np.vstack([X_train_pt, X_val_pt])
y_trval = np.concatenate([y_train, y_val])
es = callbacks.EarlyStopping(patience=10, restore_best_weights=True)
final_model.fit(
    X_trval, y_trval,
    epochs=200,
    batch_size=batch_size,
    callbacks=[es],
    verbose=2
)

# （6）测试集评估：MSE、R²
y_pred_test = final_model.predict(X_test_pt).flatten()
test_mse = mean_squared_error(y_test, y_pred_test)
test_r2  = r2_score(y_test, y_pred_test)
print("\n==== 最终模型测试集 ====")
print(f"MSE: {test_mse:.6f}   R²: {test_r2:.6f}")

# （7）方向分类评估：ROC + AUC
fpr, tpr, _ = roc_curve(y_test_bin, y_pred_test)
roc_auc = auc(fpr, tpr)
plt.figure(figsize=(6,5))
plt.plot(fpr, tpr, label=f"AUC = {roc_auc:.3f}")
plt.plot([0,1],[0,1],'--',color='gray')
plt.xlabel("False Positive Rate")
plt.ylabel("True Positive Rate")
plt.title("最终模型 测试集 ROC Curve")
plt.legend(loc="lower right")
plt.show()
# —————— 结束 ——————





