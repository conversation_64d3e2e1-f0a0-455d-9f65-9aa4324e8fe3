"""
Original file is located at
    https://colab.research.google.com/drive/1sDspYg3mg0cLIicmFsH9EfNKp78gRuD1

#!pip install tensorflow-gpu==1.15.0 tensorflow==1.15.0 stable-baselines gym-anytrading gym

#Optimize
#- Limitation
#- Fixed Window Size VS Increasing Window Size
#   - Meta-Labeling

'''

关于如何优化模型以提高预测准确性，我有以下建议：

1. **特征工程优化**：
   - 添加技术指标：如RSI、MACD、布林带等
   - 添加市场情绪指标：如社交媒体情绪分析
   - 添加基本面数据：如公司财务数据、行业数据等
   - 添加市场宏观指标：如利率、通货膨胀率等

2. **模型架构优化**：
   - 使用更复杂的网络结构：如LSTM或Transformer替代简单的MLP
   - 实现多时间尺度分析：同时考虑短期和长期趋势
   - 添加注意力机制：关注重要的市场特征

3. **训练策略优化**：
   - 增加训练数据量：使用更长时间跨度的历史数据
   - 实现交叉验证：使用多个时间窗口进行验证
   - 优化奖励函数：考虑风险调整后的收益
   - 实现早停机制：防止过拟合

4. **风险控制优化**：
   - 添加止损机制
   - 实现仓位管理
   - 考虑交易成本
   - 添加风险度量指标

5. **集成学习优化**：
   - 使用多个模型集成
   - 实现模型投票机制
   - 使用不同时间尺度的模型组合

6. **超参数优化**：
   - 使用网格搜索或贝叶斯优化
   - 优化学习率
   - 调整batch size
   - 优化网络层数和神经元数量

7. **数据预处理优化**：
   - 实现更好的数据标准化方法
   - 处理异常值和缺失值
   - 添加数据增强技术

8. **实时更新机制**：
   - 实现模型在线学习
   - 定期重新训练
   - 动态调整预测策略

这些优化建议可以根据具体需求选择性实施，建议从最重要的几个方面开始，逐步改进模型性能。
"""

# Gym library: https://github.com/AminHP/gym-anytrading
# A2C library wiki https://github.com/Stable-Baselines-Team/stable-baselines
# Original paper: https://arxiv.org/abs/1602.01783

# 导入必要的库
import gymnasium as gym  # 导入强化学习环境库
import gym_anytrading  # 导入交易环境库，用于股票交易模拟

# 导入A2C算法相关库
from stable_baselines3 import A2C  # 导入A2C(Advantage Actor-Critic)算法

# 导入数据处理和可视化相关库
import numpy as np  # 导入numpy用于数值计算
import pandas as pd  # 导入pandas用于数据处理
import pandas_datareader as web  # 导入pandas_datareader用于获取股票数据
from matplotlib import pyplot as plt  # 导入matplotlib用于数据可视化

# 读取本地Excel数据
df = pd.read_excel('/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_day.xlsx', engine='openpyxl')  # 读取特斯拉股票数据

# 数据预处理
df.columns = [col.capitalize() for col in df.columns]  # 将列名首字母大写
df.rename(columns={'Opne': 'Open'}, inplace=True)  # 修正'Open'列的拼写错误
df['Datetime'] = pd.to_datetime(df['Datetime'])  # 将日期列转换为datetime格式
df.set_index('Datetime', inplace=True)  # 将日期列设置为索引
df = df[['Open', 'High', 'Low', 'Close', 'Volume']]  # 选择需要的列并确保顺序

# 划分训练和测试数据集
train_df = df.loc['2020-01-02':'2024-01-01']  # 训练集：2020-2024年数据
test_df = df.loc['2024-01-02':'2025-04-28']  # 测试集：2024-2025年数据

# 可视化股票收盘价历史数据
# plt.figure(figsize=(16, 8))  # 设置图形大小
# plt.title('Close Price History', fontsize = 25)  # 设置标题
# plt.plot(df['Close'])  # 绘制收盘价走势图
# plt.xlabel('Date', fontsize = 18)  # 设置x轴标签
# plt.ylabel('Close Price USD($)', fontsize = 18)  # 设置y轴标签
# plt.show()  # 显示图形

# 创建训练环境
env = gym.make('stocks-v0', df=train_df, frame_bound=(15, len(train_df)), window_size=15)  # 创建股票交易环境

# 训练模型
model = A2C('MlpPolicy', env, verbose=1)  # 创建A2C模型
model.learn(total_timesteps=60000)  # 训练模型，设置总步数为60000

# 回测/验证
eval_env = gym.make('stocks-v0', df=test_df, frame_bound=(15, len(test_df)), window_size=15)  # 创建测试环境
obs, _ = eval_env.reset()  # 重置环境
done = False
while not done:  # 开始回测循环
    action, _states = model.predict(obs)  # 预测动作
    obs, rewards, terminated, truncated, info = eval_env.step(action)  # 执行动作
    done = terminated or truncated  # 检查是否结束
    # eval_env.render()  # 渲染环境

# 可视化回测结果
plt.figure(figsize=(15,6))  # 设置图形大小
eval_env.unwrapped.render_all()  # 渲染所有交易
plt.title('Backtest Result', fontsize=25)  # 设置标题
plt.xlabel('Bound', fontsize=18)  # 设置x轴标签
plt.ylabel('Close Price USD($)', fontsize=18)  # 设置y轴标签
plt.show()  # 显示图形

