"""
Original file is located at
    https://colab.research.google.com/drive/1sDspYg3mg0cLIicmFsH9EfNKp78gRuD1

#!pip install tensorflow-gpu==1.15.0 tensorflow==1.15.0 stable-baselines gym-anytrading gym

#Optimize
#- Limitation
#- Fixed Window Size VS Increasing Window Size
#   - Meta-Labeling

'''

关于如何优化模型以提高预测准确性，我有以下建议：

1. **特征工程优化**：
   - 添加技术指标：如RSI、MACD、布林带等
   - 添加市场情绪指标：如社交媒体情绪分析
   - 添加基本面数据：如公司财务数据、行业数据等
   - 添加市场宏观指标：如利率、通货膨胀率等

2. **模型架构优化**：
   - 使用更复杂的网络结构：如LSTM或Transformer替代简单的MLP
   - 实现多时间尺度分析：同时考虑短期和长期趋势
   - 添加注意力机制：关注重要的市场特征

3. **训练策略优化**：
   - 增加训练数据量：使用更长时间跨度的历史数据
   - 实现交叉验证：使用多个时间窗口进行验证
   - 优化奖励函数：考虑风险调整后的收益
   - 实现早停机制：防止过拟合

4. **风险控制优化**：
   - 添加止损机制
   - 实现仓位管理
   - 考虑交易成本
   - 添加风险度量指标

5. **集成学习优化**：
   - 使用多个模型集成
   - 实现模型投票机制
   - 使用不同时间尺度的模型组合

6. **超参数优化**：
   - 使用网格搜索或贝叶斯优化
   - 优化学习率
   - 调整batch size
   - 优化网络层数和神经元数量

7. **数据预处理优化**：
   - 实现更好的数据标准化方法
   - 处理异常值和缺失值
   - 添加数据增强技术

8. **实时更新机制**：
   - 实现模型在线学习
   - 定期重新训练
   - 动态调整预测策略

这些优化建议可以根据具体需求选择性实施，建议从最重要的几个方面开始，逐步改进模型性能。
"""

# Gym library: https://github.com/AminHP/gym-anytrading
# A2C library wiki https://github.com/Stable-Baselines-Team/stable-baselines
# Original paper: https://arxiv.org/abs/1602.01783

# 导入必要的库
import gymnasium as gym  # 导入强化学习环境库
import gym_anytrading  # 导入交易环境库，用于股票交易模拟
import talib  # 用于计算技术指标
from scipy import stats
import os
import random
import numpy as np
import torch
import torch.nn as nn
from stable_baselines3.common.callbacks import BaseCallback
from stable_baselines3.common.logger import configure
import time
from stable_baselines3 import PPO  # 改用PPO算法
from stable_baselines3.common.vec_env import DummyVecEnv
from stable_baselines3.common.torch_layers import BaseFeaturesExtractor
from sklearn.feature_selection import SelectKBest, f_classif

# 导入数据处理和可视化相关库
import pandas as pd  # 导入pandas用于数据处理
import pandas_datareader as web  # 导入pandas_datareader用于获取股票数据
from matplotlib import pyplot as plt  # 导入matplotlib用于数据可视化

# 读取本地Excel数据
df = pd.read_excel('/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_day.xlsx', engine='openpyxl')  # 读取特斯拉股票数据

# 数据预处理
df.columns = [col.capitalize() for col in df.columns]  # 将列名首字母大写
df.rename(columns={'Opne': 'Open'}, inplace=True)  # 修正'Open'列的拼写错误
df['Datetime'] = pd.to_datetime(df['Datetime'])  # 将日期列转换为datetime格式
df.set_index('Datetime', inplace=True)  # 将日期列设置为索引
df = df[['Open', 'High', 'Low', 'Close', 'Volume']]  # 选择需要的列并确保顺序

# 在数据预处理后添加技术指标计算
def add_technical_indicators(df):
    print("\n=== 开始添加技术指标 ===")
    
    # 1. 趋势类指标
    print("1. 添加趋势类指标...")
    # 移动平均线
    df['SMA_5'] = talib.SMA(df['Close'], timeperiod=5)
    df['SMA_10'] = talib.SMA(df['Close'], timeperiod=10)
    df['SMA_20'] = talib.SMA(df['Close'], timeperiod=20)
    df['SMA_50'] = talib.SMA(df['Close'], timeperiod=50)
    df['SMA_200'] = talib.SMA(df['Close'], timeperiod=200)
    
    # EMA
    df['EMA_5'] = talib.EMA(df['Close'], timeperiod=5)
    df['EMA_10'] = talib.EMA(df['Close'], timeperiod=10)
    df['EMA_20'] = talib.EMA(df['Close'], timeperiod=20)
    
    # MACD
    macd, macd_signal, macd_hist = talib.MACD(df['Close'])
    df['MACD'] = macd
    df['MACD_Signal'] = macd_signal
    df['MACD_Hist'] = macd_hist
    
    # 2. 动量类指标
    print("2. 添加动量类指标...")
    # RSI
    df['RSI_6'] = talib.RSI(df['Close'], timeperiod=6)
    df['RSI_12'] = talib.RSI(df['Close'], timeperiod=12)
    df['RSI_24'] = talib.RSI(df['Close'], timeperiod=24)
    
    # 随机指标
    df['STOCH_K'], df['STOCH_D'] = talib.STOCH(df['High'], df['Low'], df['Close'])
    
    # CCI
    df['CCI'] = talib.CCI(df['High'], df['Low'], df['Close'])
    
    # 3. 波动率指标
    print("3. 添加波动率指标...")
    # 布林带
    df['BB_Upper'], df['BB_Middle'], df['BB_Lower'] = talib.BBANDS(df['Close'])
    
    # ATR
    df['ATR'] = talib.ATR(df['High'], df['Low'], df['Close'])
    
    # 4. 成交量指标
    print("4. 添加成交量指标...")
    # OBV
    df['OBV'] = talib.OBV(df['Close'], df['Volume'])
    
    # 成交量移动平均
    df['Volume_SMA_5'] = talib.SMA(df['Volume'], timeperiod=5)
    df['Volume_SMA_20'] = talib.SMA(df['Volume'], timeperiod=20)
    
    # 5. 价格形态指标
    print("5. 添加价格形态指标...")
    # DOJI
    df['DOJI'] = talib.CDLDOJI(df['Open'], df['High'], df['Low'], df['Close'])
    
    # HAMMER
    df['HAMMER'] = talib.CDLHAMMER(df['Open'], df['High'], df['Low'], df['Close'])
    
    # ENGULFING
    df['ENGULFING'] = talib.CDLENGULFING(df['Open'], df['High'], df['Low'], df['Close'])
    
    # 6. 自定义技术指标
    print("6. 添加自定义技术指标...")
    # 价格动量
    df['Price_Momentum'] = df['Close'].pct_change(periods=5)
    
    # 波动率
    df['Volatility'] = df['Close'].rolling(window=20).std()
    
    # 价格范围
    df['Price_Range'] = (df['High'] - df['Low']) / df['Close']
    
    # 7. 统计指标
    print("7. 添加统计指标...")
    # Z-Score
    df['Price_ZScore'] = (df['Close'] - df['Close'].rolling(window=20).mean()) / df['Close'].rolling(window=20).std()
    df['Volume_ZScore'] = (df['Volume'] - df['Volume'].rolling(window=20).mean()) / df['Volume'].rolling(window=20).std()
    
    # 8. 趋势强度指标
    print("8. 添加趋势强度指标...")
    # ADX
    df['ADX'] = talib.ADX(df['High'], df['Low'], df['Close'])
    
    # DI
    df['PLUS_DI'] = talib.PLUS_DI(df['High'], df['Low'], df['Close'])
    df['MINUS_DI'] = talib.MINUS_DI(df['High'], df['Low'], df['Close'])
    
    # 新增趋势强度指标
    df['ADX_14'] = talib.ADX(df['High'], df['Low'], df['Close'], timeperiod=14)
    df['PLUS_DI_14'] = talib.PLUS_DI(df['High'], df['Low'], df['Close'], timeperiod=14)
    df['MINUS_DI_14'] = talib.MINUS_DI(df['High'], df['Low'], df['Close'], timeperiod=14)
    
    # 新增趋势确认指标
    df['TRIX'] = talib.TRIX(df['Close'], timeperiod=30)
    df['TEMA'] = talib.TEMA(df['Close'], timeperiod=20)
    
    # 新增动量指标
    df['MOM_10'] = talib.MOM(df['Close'], timeperiod=10)
    df['ROC_10'] = talib.ROC(df['Close'], timeperiod=10)
    df['WILLR_14'] = talib.WILLR(df['High'], df['Low'], df['Close'], timeperiod=14)
    
    # 新增反转因子
    df['reversal_5'] = - (df['Close'] / df['Close'].shift(5) - 1)
    df['reversal_10'] = - (df['Close'] / df['Close'].shift(10) - 1)
    
    # 新增波动率指标
    df['NATR_14'] = talib.NATR(df['High'], df['Low'], df['Close'], timeperiod=14)
    df['TRANGE'] = talib.TRANGE(df['High'], df['Low'], df['Close'])
    df['VOLATILITY_10'] = df['Close'].pct_change().rolling(10).std()
    
    # 新增成交量指标
    df['AD'] = talib.AD(df['High'], df['Low'], df['Close'], df['Volume'])
    df['ADOSC'] = talib.ADOSC(df['High'], df['Low'], df['Close'], df['Volume'])
    df['MFI_14'] = talib.MFI(df['High'], df['Low'], df['Close'], df['Volume'], timeperiod=14)
    
    # 数据标准化
    print("\n9. 数据标准化...")
    # 对价格数据进行标准化
    price_columns = ['Open', 'High', 'Low', 'Close']
    for col in price_columns:
        mean = df[col].mean()
        std = df[col].std()
        df[col] = (df[col] - mean) / (std + 1e-8)  # 添加小量防止除零
    
    # 对成交量进行标准化
    mean_volume = df['Volume'].mean()
    std_volume = df['Volume'].std()
    df['Volume'] = (df['Volume'] - mean_volume) / (std_volume + 1e-8)
    
    # 对技术指标进行标准化
    tech_columns = [col for col in df.columns if col not in price_columns + ['Volume']]
    for col in tech_columns:
        mean = df[col].mean()
        std = df[col].std()
        if std != 0:
            df[col] = (df[col] - mean) / (std + 1e-8)
        else:
            df[col] = 0
    
    # 处理异常值
    for col in df.columns:
        df[col] = df[col].clip(-10, 10)  # 将值限制在[-10, 10]范围内
    
    print("\n=== 特征工程完成 ===")
    print(f"总共添加了 {len(df.columns) - 5} 个技术指标")
    
    print("\n添加的指标类别：")
    print("1. 趋势类指标 (SMA, EMA, MACD)")
    print("2. 动量类指标 (RSI, STOCH, CCI)")
    print("3. 波动率指标 (BB, ATR)")
    print("4. 成交量指标 (OBV, Volume SMA)")
    print("5. 价格形态指标 (DOJI, HAMMER, ENGULFING)")
    print("6. 自定义技术指标 (Price Momentum, Volatility, Price Range)")
    print("7. 统计指标 (Z-Score)")
    print("8. 趋势强度指标 (ADX, DI)")
    print("9. 数据标准化")
    
    return df

# 在数据预处理后调用
df = add_technical_indicators(df)

# 处理NaN值
df = df.ffill()  # 使用前向填充处理缺失值
df = df.bfill()  # 使用后向填充处理剩余的缺失值

# 打印数据信息
print("\n=== 数据信息 ===")
print(f"数据形状: {df.shape}")
print("\n列名列表:")
for col in df.columns:
    print(f"- {col}")

# 划分训练和测试数据集
train_df = df.loc['2020-01-02':'2024-01-01']  # 训练集：2020-2024年数据
test_df = df.loc['2024-01-02':'2025-04-28']  # 测试集：2024-2025年数据

# 创建模型存储目录
model_storage_dir = '/Users/<USER>/MLQuant/Quant_ML_Struc/Reinforcement/RLmodel_storage'
tensorboard_log_dir = '/Users/<USER>/MLQuant/Quant_ML_Struc/Reinforcement/tensorboard_logs'
os.makedirs(model_storage_dir, exist_ok=True)
os.makedirs(tensorboard_log_dir, exist_ok=True)

# 设置模型保存路径
model_path = os.path.join(model_storage_dir, 'a2c_stocks_model')

# 设置随机种子
def set_seed(seed):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)

# 自定义回调函数
class TimeRemainingCallback(BaseCallback):
    def __init__(self, total_timesteps, verbose=0):
        super().__init__(verbose)
        self.total_timesteps = total_timesteps
        self.start_time = None
        self.last_update = 0
        self.update_interval = 10000
        
    def _on_training_start(self):
        self.start_time = time.time()
        elapsed_time = time.time() - self.start_time
        steps_per_second = self.num_timesteps / elapsed_time if elapsed_time > 0 else 0
        remaining_steps = self.total_timesteps - self.num_timesteps
        remaining_time = remaining_steps / steps_per_second if steps_per_second > 0 else 0
        print(f"\n开始训练...预计总时间: {remaining_time/60:.1f}分钟")
        
    def _on_step(self):
        if self.num_timesteps - self.last_update >= self.update_interval:
            self.last_update = self.num_timesteps
            elapsed_time = time.time() - self.start_time
            steps_per_second = self.num_timesteps / elapsed_time
            remaining_steps = self.total_timesteps - self.num_timesteps
            remaining_time = remaining_steps / steps_per_second
            
            progress = self.num_timesteps / self.total_timesteps * 100
            if progress <= 1 or (progress >= 49 and progress <= 51):
                print(f"\n当前进度: {progress:.1f}% | 剩余时间: {remaining_time/60:.1f}分钟")
        
        return True

class LearningRateDecayCallback(BaseCallback):
    def __init__(self, initial_lr, min_lr, decay_rate, verbose=0):
        super().__init__(verbose)
        self.initial_lr = initial_lr
        self.min_lr = min_lr
        self.decay_rate = decay_rate
        self.current_lr = initial_lr
        
    def _on_step(self):
        self.current_lr = max(self.min_lr, self.current_lr * self.decay_rate)
        self.model.learning_rate = self.current_lr
        return True

class EarlyStoppingCallback(BaseCallback):
    def __init__(self, patience, threshold, verbose=0):
        super().__init__(verbose)
        self.patience = patience
        self.threshold = threshold
        self.best_reward = -np.inf
        self.no_improvement_count = 0
        
    def _on_step(self):
        if len(self.model.ep_info_buffer) > 0:
            current_reward = np.mean([ep_info["r"] for ep_info in self.model.ep_info_buffer])
            
            if current_reward > self.best_reward + self.threshold:
                self.best_reward = current_reward
                self.no_improvement_count = 0
            else:
                self.no_improvement_count += 1
                
            if self.no_improvement_count >= self.patience:
                return False
        return True

# 自定义特征提取器
class CustomCNN(BaseFeaturesExtractor):
    def __init__(self, observation_space, features_dim=128):  # 增加特征维度
        super().__init__(observation_space, features_dim)
        n_input_channels = observation_space.shape[0]
        
        # 添加残差连接
        self.cnn = nn.Sequential(
            # 第一个卷积块
            nn.Conv1d(n_input_channels, 64, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(0.2),  # 添加dropout
            
            # 第二个卷积块
            nn.Conv1d(64, 128, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.2),
            
            # 第三个卷积块
            nn.Conv1d(128, 256, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.2),
            
            nn.Flatten(),
        )
        
        # 计算CNN输出维度
        with torch.no_grad():
            n_flatten = self.cnn(
                torch.as_tensor(observation_space.sample()[None]).float()
            ).shape[1]
        
        # 简化MLP
        self.linear = nn.Sequential(
            nn.Linear(n_flatten, 128),
            nn.LayerNorm(128),
            nn.ReLU(),
            nn.Linear(128, features_dim),
            nn.LayerNorm(features_dim),
            nn.ReLU()
        )
        
        # 初始化权重
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            nn.init.kaiming_normal_(module.weight, mode='fan_in', nonlinearity='relu')
            if module.bias is not None:
                module.bias.data.zero_()
        elif isinstance(module, nn.Conv1d):
            nn.init.kaiming_normal_(module.weight, mode='fan_in', nonlinearity='relu')
            if module.bias is not None:
                module.bias.data.zero_()
    
    def forward(self, observations):
        return self.linear(self.cnn(observations))

# 自定义交易环境
class CustomTradingEnv(gym.Env):
    def __init__(self, df, window_size=15, frame_bound=(15, 1000), initial_balance=10000, transaction_fee_percent=0.001):
        assert len(frame_bound) == 2, "frame_bound should be a tuple of (start, end)"
        
        self.df = df
        self.window_size = window_size
        self.frame_bound = frame_bound
        self.initial_balance = initial_balance
        self.transaction_fee_percent = transaction_fee_percent
        
        # 动作空间：0=持有，1=买入，2=卖出
        self.action_space = gym.spaces.Discrete(3)
        
        # 观察空间：价格数据 + 技术指标
        self.observation_space = gym.spaces.Box(
            low=-np.inf, high=np.inf, shape=(len(df.columns), window_size), dtype=np.float32
        )
        
        # 交易状态
        self.balance = initial_balance
        self.shares_held = 0
        self.cost_basis = 0
        self.total_shares_sold = 0
        self.total_sales_value = 0
        self.current_step = frame_bound[0]
        
        # 记录历史数据
        self.history = []
        
    def _get_observation(self):
        obs = self.df.iloc[self.current_step-self.window_size:self.current_step].values.T
        return obs.astype(np.float32)
    
    def reset(self, seed=None, options=None):
        super().reset(seed=seed)
        self.balance = self.initial_balance
        self.shares_held = 0
        self.cost_basis = 0
        self.total_shares_sold = 0
        self.total_sales_value = 0
        self.current_step = self.frame_bound[0]
        self.history = []
        
        # 获取初始观察值
        obs = self._get_observation()
        
        # 返回观察值和信息字典
        info = {
            'current_price': self.df.iloc[self.current_step]['Close'],
            'shares_held': self.shares_held,
            'balance': self.balance,
            'portfolio_value': self.balance + self.shares_held * self.df.iloc[self.current_step]['Close'],
            'total_profit': 0
        }
        
        return obs, info
    
    def step(self, action):
        # 执行交易
        current_price = self.df.iloc[self.current_step]['Close']
        
        if action == 1:  # 买入
            if self.balance > 0:
                shares_to_buy = self.balance / current_price
                self.shares_held += shares_to_buy
                self.balance = 0
                self.cost_basis = current_price
        elif action == 2:  # 卖出
            if self.shares_held > 0:
                self.balance += self.shares_held * current_price
                self.total_shares_sold += self.shares_held
                self.total_sales_value += self.shares_held * current_price
                self.shares_held = 0
        
        # 计算奖励
        reward = self._calculate_reward(action)
        
        # 更新步骤
        self.current_step += 1
        
        # 检查是否结束
        done = self.current_step >= self.frame_bound[1]
        
        # 获取观察值
        obs = self._get_observation()
        
        # 额外信息
        info = {
            'current_price': current_price,
            'shares_held': self.shares_held,
            'balance': self.balance,
            'portfolio_value': self.balance + self.shares_held * current_price,
            'total_profit': (self.balance + self.shares_held * current_price) - self.initial_balance
        }
        
        return obs, reward, done, False, info

    def _calculate_reward(self, action):
        # 获取当前价格和历史数据
        current_price = self.df.iloc[self.current_step]['Close']
        
        # 计算持仓价值
        portfolio_value = self.balance + self.shares_held * current_price
        
        # 计算收益率（限制在[-1, 1]范围内）
        returns = np.clip((portfolio_value - self.initial_balance) / self.initial_balance, -1, 1)
        
        # 计算交易成本
        transaction_cost = 0
        if action == 1:  # 买入
            transaction_cost = current_price * self.transaction_fee_percent
        elif action == 2:  # 卖出
            transaction_cost = current_price * self.transaction_fee_percent
        
        # 计算趋势奖励
        trend_reward = 0
        if self.current_step > self.window_size:
            # 使用ADX和DI指标
            adx = self.df.iloc[self.current_step]['ADX_14']
            plus_di = self.df.iloc[self.current_step]['PLUS_DI_14']
            minus_di = self.df.iloc[self.current_step]['MINUS_DI_14']
            
            if adx > 25:  # 强趋势
                if plus_di > minus_di:  # 上升趋势
                    trend_reward = 0.2
                else:  # 下降趋势
                    trend_reward = -0.2
        
        # 计算技术指标奖励
        tech_reward = 0
        if self.current_step > self.window_size:
            # RSI指标
            rsi = self.df.iloc[self.current_step]['RSI_12']
            if (action == 1 and rsi < 30) or (action == 2 and rsi > 70):
                tech_reward += 0.1
            
            # MACD指标
            macd = self.df.iloc[self.current_step]['MACD']
            macd_signal = self.df.iloc[self.current_step]['MACD_Signal']
            if (action == 1 and macd > macd_signal) or (action == 2 and macd < macd_signal):
                tech_reward += 0.1
            
            # 布林带指标
            bb_upper = self.df.iloc[self.current_step]['BB_Upper']
            bb_lower = self.df.iloc[self.current_step]['BB_Lower']
            if (action == 1 and current_price < bb_lower) or (action == 2 and current_price > bb_upper):
                tech_reward += 0.1
            
            # 成交量指标
            mfi = self.df.iloc[self.current_step]['MFI_14']
            if (action == 1 and mfi < 20) or (action == 2 and mfi > 80):
                tech_reward += 0.1
        
        # 计算波动率惩罚
        volatility_penalty = 0
        if self.current_step > self.window_size:
            atr = self.df.iloc[self.current_step]['ATR']
            natr = self.df.iloc[self.current_step]['NATR_14']
            if natr > 0.02:  # 高波动率
                volatility_penalty = 0.1
        
        # 综合奖励
        reward = (
            returns * 0.3 +                    # 收益率权重
            trend_reward * 0.2 +              # 趋势奖励权重
            tech_reward * 0.3 -               # 技术指标奖励权重
            transaction_cost * 0.1 -          # 交易成本权重
            volatility_penalty * 0.1          # 波动率惩罚权重
        )
        
        # 最终限制奖励在[-1, 1]范围内
        reward = np.clip(reward, -1, 1)
        
        # 记录历史数据
        self.history.append({
            'step': self.current_step,
            'action': action,
            'reward': reward,
            'portfolio_value': portfolio_value,
            'shares_held': self.shares_held,
            'balance': self.balance
        })
        
        return reward

# 创建训练环境
env = CustomTradingEnv(
    df=train_df,
    window_size=15,
    frame_bound=(15, len(train_df)),
    initial_balance=10000,
    transaction_fee_percent=0.001
)

# 将环境包装在DummyVecEnv中
env = DummyVecEnv([lambda: env])

# 设置随机种子
set_seed(42)

# 创建回调函数列表
callbacks = [
    TimeRemainingCallback(total_timesteps=500000),
    LearningRateDecayCallback(
        initial_lr=0.0001,
        min_lr=0.00001,
        decay_rate=0.999
    ),
    EarlyStoppingCallback(
        patience=10,
        threshold=0.01
    )
]

# 检查是否存在已训练的模型
if os.path.exists(model_path + '.zip'):
    try:
        print("尝试加载已训练的模型...")
        model = PPO.load(model_path, env=env, tensorboard_log=tensorboard_log_dir)
        print("成功加载模型！")
    except ValueError as e:
        print(f"无法加载旧模型: {e}")
        print("创建新模型...")
        model = PPO(
            'CnnPolicy',
            env,
            verbose=1,
            tensorboard_log=tensorboard_log_dir,
            learning_rate=0.0003,  # 略微提高学习率
            n_steps=4096,         # 增加步数
            batch_size=128,       # 增加batch size
            n_epochs=15,          # 增加训练轮数
            gamma=0.99,
            gae_lambda=0.95,
            clip_range=0.2,
            ent_coef=0.01,        # 降低熵系数
            vf_coef=0.5,
            max_grad_norm=0.5,
            policy_kwargs=dict(
                features_extractor_class=CustomCNN,
                features_extractor_kwargs=dict(features_dim=128),
                net_arch=dict(
                    pi=[256, 128],  # 增加网络容量
                    vf=[256, 128]
                ),
                activation_fn=nn.ReLU
            )
        )
else:
    print("创建新模型...")
    model = PPO(
        'CnnPolicy',
        env,
        verbose=1,
        tensorboard_log=tensorboard_log_dir,
        learning_rate=0.0003,  # 略微提高学习率
        n_steps=4096,         # 增加步数
        batch_size=128,       # 增加batch size
        n_epochs=15,          # 增加训练轮数
        gamma=0.99,
        gae_lambda=0.95,
        clip_range=0.2,
        ent_coef=0.01,        # 降低熵系数
        vf_coef=0.5,
        max_grad_norm=0.5,
        policy_kwargs=dict(
            features_extractor_class=CustomCNN,
            features_extractor_kwargs=dict(features_dim=128),
            net_arch=dict(
                pi=[256, 128],  # 增加网络容量
                vf=[256, 128]
            ),
            activation_fn=nn.ReLU
        )
    )

# 训练模型
print("开始训练模型...")
model.learn(
    total_timesteps=500000,
    callback=callbacks
)

# 保存训练好的模型
model.save(model_path)
print(f"模型已保存到: {model_path}")

# 回测/验证
eval_env = CustomTradingEnv(
    df=test_df,
    window_size=15,
    frame_bound=(15, len(test_df)),
    initial_balance=10000,
    transaction_fee_percent=0.001
)
eval_env = DummyVecEnv([lambda: eval_env])

# 设置评估环境的随机种子
set_seed(42)

# 初始化环境
obs = eval_env.reset()[0]
done = False
total_reward = 0
trades = []
portfolio_values = []  # 记录投资组合价值

while not done:
    # 预测动作
    action, _states = model.predict(obs, deterministic=True)  # 使用确定性策略
    
    # 执行动作
    obs, rewards, dones, infos = eval_env.step([action[0]])
    
    # 更新状态
    done = dones[0]
    total_reward += rewards[0]
    
    # 记录交易信息
    if action[0] != 0:  # 记录非持有动作
        trades.append({
            'action': action[0],
            'price': infos[0]['current_price'],
            'reward': rewards[0],
            'portfolio_value': infos[0]['portfolio_value']
        })
    
    # 记录投资组合价值
    portfolio_values.append(infos[0]['portfolio_value'])

print(f"\n回测结果:")
print(f"总奖励: {total_reward:.2f}")
print(f"总收益: {infos[0]['total_profit']:.2f}")
print(f"交易次数: {len(trades)}")
print(f"最终资金: {infos[0]['portfolio_value']:.2f}")

# 计算一些额外的指标
if len(trades) > 0:
    winning_trades = sum(1 for trade in trades if trade['reward'] > 0)
    win_rate = winning_trades / len(trades) * 100
    print(f"胜率: {win_rate:.2f}%")
    
    avg_profit = sum(trade['reward'] for trade in trades) / len(trades)
    print(f"平均每笔交易收益: {avg_profit:.2f}")

# 可视化回测结果
plt.figure(figsize=(15,10))

# 绘制价格和交易点
plt.subplot(2,1,1)
prices = test_df['Close'].values[15:]
plt.plot(prices, label='Price', color='blue', alpha=0.6)
for trade in trades:
    if trade['action'] == 1:  # 买入
        plt.scatter(trade['price'], trade['price'], color='green', marker='^', s=100)
    else:  # 卖出
        plt.scatter(trade['price'], trade['price'], color='red', marker='v', s=100)
plt.title('Backtest Result', fontsize=15)
plt.xlabel('Time', fontsize=12)
plt.ylabel('Price', fontsize=12)
plt.legend()

# 绘制累积收益
plt.subplot(2,1,2)
plt.plot(portfolio_values, label='Portfolio Value', color='green')
plt.axhline(y=10000, color='r', linestyle='--', label='Initial Balance')
plt.title('Portfolio Value Over Time', fontsize=15)
plt.xlabel('Time', fontsize=12)
plt.ylabel('Value', fontsize=12)
plt.legend()

plt.tight_layout()
plt.savefig('backtest_results.png')
plt.close()

print("\n回测结果图表已保存为 'backtest_results.png'")

# 添加特征选择
selector = SelectKBest(f_classif, k=20)
selected_features = selector.fit_transform(X, y)

