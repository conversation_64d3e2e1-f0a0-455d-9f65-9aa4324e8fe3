"""
Original file is located at
    https://colab.research.google.com/drive/1sDspYg3mg0cLIicmFsH9EfNKp78gRuD1

#!pip install tensorflow-gpu==1.15.0 tensorflow==1.15.0 stable-baselines gym-anytrading gym

#Optimize
#- Limitation
#- Fixed Window Size VS Increasing Window Size
#   - Meta-Labeling

'''

关于如何优化模型以提高预测准确性，我有以下建议：

1. **特征工程优化**：
   - 添加技术指标：如RSI、MACD、布林带等
   - 添加市场情绪指标：如社交媒体情绪分析
   - 添加基本面数据：如公司财务数据、行业数据等
   - 添加市场宏观指标：如利率、通货膨胀率等

2. **模型架构优化**：
   - 使用更复杂的网络结构：如LSTM或Transformer替代简单的MLP
   - 实现多时间尺度分析：同时考虑短期和长期趋势
   - 添加注意力机制：关注重要的市场特征

3. **训练策略优化**：
   - 增加训练数据量：使用更长时间跨度的历史数据
   - 实现交叉验证：使用多个时间窗口进行验证
   - 优化奖励函数：考虑风险调整后的收益
   - 实现早停机制：防止过拟合

4. **风险控制优化**：
   - 添加止损机制
   - 实现仓位管理
   - 考虑交易成本
   - 添加风险度量指标

5. **集成学习优化**：
   - 使用多个模型集成
   - 实现模型投票机制
   - 使用不同时间尺度的模型组合

6. **超参数优化**：
   - 使用网格搜索或贝叶斯优化
   - 优化学习率
   - 调整batch size
   - 优化网络层数和神经元数量

7. **数据预处理优化**：
   - 实现更好的数据标准化方法
   - 处理异常值和缺失值
   - 添加数据增强技术

8. **实时更新机制**：
   - 实现模型在线学习
   - 定期重新训练
   - 动态调整预测策略

这些优化建议可以根据具体需求选择性实施，建议从最重要的几个方面开始，逐步改进模型性能。
"""

# Gym library: https://github.com/AminHP/gym-anytrading
# A2C library wiki https://github.com/Stable-Baselines-Team/stable-baselines
# Original paper: https://arxiv.org/abs/1602.01783

# 导入必要的库
import gymnasium as gym  # 导入强化学习环境库
import gym_anytrading  # 导入交易环境库，用于股票交易模拟
import talib  # 用于计算技术指标
from scipy import stats

# 导入A2C算法相关库
from stable_baselines3 import A2C  # 导入A2C(Advantage Actor-Critic)算法

# 导入数据处理和可视化相关库
import numpy as np  # 导入numpy用于数值计算
import pandas as pd  # 导入pandas用于数据处理
import pandas_datareader as web  # 导入pandas_datareader用于获取股票数据
from matplotlib import pyplot as plt  # 导入matplotlib用于数据可视化

# 读取本地Excel数据
df = pd.read_excel('/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_day.xlsx', engine='openpyxl')  # 读取特斯拉股票数据

# 数据预处理
df.columns = [col.capitalize() for col in df.columns]  # 将列名首字母大写
df.rename(columns={'Opne': 'Open'}, inplace=True)  # 修正'Open'列的拼写错误
df['Datetime'] = pd.to_datetime(df['Datetime'])  # 将日期列转换为datetime格式
df.set_index('Datetime', inplace=True)  # 将日期列设置为索引
df = df[['Open', 'High', 'Low', 'Close', 'Volume']]  # 选择需要的列并确保顺序

# 在数据预处理后添加技术指标计算
def add_technical_indicators(df):
    print("\n=== 开始添加技术指标 ===")
    
    # 1. 趋势类指标
    print("1. 添加趋势类指标...")
    # 移动平均线
    df['SMA_5'] = talib.SMA(df['Close'], timeperiod=5)
    df['SMA_10'] = talib.SMA(df['Close'], timeperiod=10)
    df['SMA_20'] = talib.SMA(df['Close'], timeperiod=20)
    df['SMA_50'] = talib.SMA(df['Close'], timeperiod=50)
    df['SMA_200'] = talib.SMA(df['Close'], timeperiod=200)
    
    # EMA
    df['EMA_5'] = talib.EMA(df['Close'], timeperiod=5)
    df['EMA_10'] = talib.EMA(df['Close'], timeperiod=10)
    df['EMA_20'] = talib.EMA(df['Close'], timeperiod=20)
    
    # MACD
    macd, macd_signal, macd_hist = talib.MACD(df['Close'])
    df['MACD'] = macd
    df['MACD_Signal'] = macd_signal
    df['MACD_Hist'] = macd_hist
    
    # 2. 动量类指标
    print("2. 添加动量类指标...")
    # RSI
    df['RSI_6'] = talib.RSI(df['Close'], timeperiod=6)
    df['RSI_12'] = talib.RSI(df['Close'], timeperiod=12)
    df['RSI_24'] = talib.RSI(df['Close'], timeperiod=24)
    
    # 随机指标
    df['STOCH_K'], df['STOCH_D'] = talib.STOCH(df['High'], df['Low'], df['Close'])
    
    # CCI
    df['CCI'] = talib.CCI(df['High'], df['Low'], df['Close'])
    
    # 3. 波动率指标
    print("3. 添加波动率指标...")
    # 布林带
    df['BB_Upper'], df['BB_Middle'], df['BB_Lower'] = talib.BBANDS(df['Close'])
    
    # ATR
    df['ATR'] = talib.ATR(df['High'], df['Low'], df['Close'])
    
    # 4. 成交量指标
    print("4. 添加成交量指标...")
    # OBV
    df['OBV'] = talib.OBV(df['Close'], df['Volume'])
    
    # 成交量移动平均
    df['Volume_SMA_5'] = talib.SMA(df['Volume'], timeperiod=5)
    df['Volume_SMA_20'] = talib.SMA(df['Volume'], timeperiod=20)
    
    # 5. 价格形态指标
    print("5. 添加价格形态指标...")
    # 十字星
    df['DOJI'] = talib.CDLDOJI(df['Open'], df['High'], df['Low'], df['Close'])
    # 锤子线
    df['HAMMER'] = talib.CDLHAMMER(df['Open'], df['High'], df['Low'], df['Close'])
    # 吞没形态
    df['ENGULFING'] = talib.CDLENGULFING(df['Open'], df['High'], df['Low'], df['Close'])
    
    # 6. 自定义技术指标
    print("6. 添加自定义技术指标...")
    # 价格动量
    df['Price_Momentum'] = df['Close'].pct_change(periods=5)
    
    # 波动率
    df['Volatility'] = df['Close'].rolling(window=20).std()
    
    # 价格范围
    df['Price_Range'] = (df['High'] - df['Low']) / df['Close']
    
    # 7. 统计指标
    print("7. 添加统计指标...")
    # 价格z-score
    df['Price_ZScore'] = stats.zscore(df['Close'])
    
    # 成交量z-score
    df['Volume_ZScore'] = stats.zscore(df['Volume'])
    
    # 8. 趋势强度指标
    print("8. 添加趋势强度指标...")
    # ADX
    df['ADX'] = talib.ADX(df['High'], df['Low'], df['Close'])
    
    # 方向移动指标
    df['PLUS_DI'] = talib.PLUS_DI(df['High'], df['Low'], df['Close'])
    df['MINUS_DI'] = talib.MINUS_DI(df['High'], df['Low'], df['Close'])
    
    print("\n=== 特征工程完成 ===")
    print(f"总共添加了 {len(df.columns) - 5} 个技术指标")  # 减去原始的5个列
    print("\n添加的指标类别：")
    print("1. 趋势类指标 (SMA, EMA, MACD)")
    print("2. 动量类指标 (RSI, STOCH, CCI)")
    print("3. 波动率指标 (BB, ATR)")
    print("4. 成交量指标 (OBV, Volume SMA)")
    print("5. 价格形态指标 (DOJI, HAMMER, ENGULFING)")
    print("6. 自定义技术指标 (Price Momentum, Volatility, Price Range)")
    print("7. 统计指标 (Z-Score)")
    print("8. 趋势强度指标 (ADX, DI)")
    
    return df

# 在数据预处理后调用
df = add_technical_indicators(df)

# 处理NaN值
df = df.fillna(method='ffill')  # 使用前向填充处理缺失值
df = df.fillna(method='bfill')  # 使用后向填充处理剩余的缺失值

# 打印数据信息
print("\n=== 数据信息 ===")
print(f"数据形状: {df.shape}")
print("\n列名列表:")
for col in df.columns:
    print(f"- {col}")

# 划分训练和测试数据集
train_df = df.loc['2020-01-02':'2024-01-01']  # 训练集：2020-2024年数据
test_df = df.loc['2024-01-02':'2025-04-28']  # 测试集：2024-2025年数据

# 可视化股票收盘价历史数据
# plt.figure(figsize=(16, 8))  # 设置图形大小
# plt.title('Close Price History', fontsize = 25)  # 设置标题
# plt.plot(df['Close'])  # 绘制收盘价走势图
# plt.xlabel('Date', fontsize = 18)  # 设置x轴标签
# plt.ylabel('Close Price USD($)', fontsize = 18)  # 设置y轴标签
# plt.show()  # 显示图形

# 创建训练环境
env = gym.make('stocks-v0', df=train_df, frame_bound=(15, len(train_df)), window_size=15)  # 创建股票交易环境

# 训练模型
model = A2C('MlpPolicy', env, verbose=1)  # 创建A2C模型
model.learn(total_timesteps=60000)  # 训练模型，设置总步数为60000

# 回测/验证
eval_env = gym.make('stocks-v0', df=test_df, frame_bound=(15, len(test_df)), window_size=15)  # 创建测试环境
obs, _ = eval_env.reset()  # 重置环境
done = False
while not done:  # 开始回测循环
    action, _states = model.predict(obs)  # 预测动作
    obs, rewards, terminated, truncated, info = eval_env.step(action)  # 执行动作
    done = terminated or truncated  # 检查是否结束
    # eval_env.render()  # 渲染环境

# 可视化回测结果
plt.figure(figsize=(15,6))  # 设置图形大小
eval_env.unwrapped.render_all()  # 渲染所有交易
plt.title('Backtest Result', fontsize=25)  # 设置标题
plt.xlabel('Bound', fontsize=18)  # 设置x轴标签
plt.ylabel('Close Price USD($)', fontsize=18)  # 设置y轴标签
plt.show()  # 显示图形

