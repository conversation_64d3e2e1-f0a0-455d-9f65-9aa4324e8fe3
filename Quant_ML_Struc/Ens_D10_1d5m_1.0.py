# Ensemble 模型策略 - 原始版本 1.0
# 本notebook将整合之前实现的各种模型，构建一个基于ensemble模型的策略。主要步骤包括：

# 1. 数据获取与预处理
# 2. 特征工程（技术指标构建）
# 3. 数据集划分（训练集、验证集、测试集）
# 4. 模型集成：
#    4.1 线性回归（Day1）
#    4.2 随机森林（Day2）
#    4.3 XGBoost（Day3）
#    4.4 MLP（Day4）
# 5. 模型权重优化
# 6. 策略回测与评估

# ——————————————————————————————————————————————————————————————————————————————

# 0. 导入依赖包
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import sys

from dotenv import load_dotenv, find_dotenv

# Find the .env file in the parent directory
dotenv_path = find_dotenv("../../.env")
# Load it explicitly
load_dotenv(dotenv_path)

# Add the parent directory to the sys.path list
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))

from data_processing import load_data_year, flatten_yf_columns, standardize_columns
from plotting import plot_results
from strategy.buy_and_hold import BuyAndHoldStrategy
from back_test import run_backtest
import backtrader as bt

# 设置显示选项
pd.set_option('display.float_format', lambda x: '%.4f' % x)
# 绘图风格（可选）
plt.style.use('seaborn-v0_8-bright')
# 设置中文显示
plt.rcParams['font.sans-serif'] = ['PingFang HK']
plt.rcParams['axes.unicode_minus'] = False

import random

# 固定全局随机种子
np.random.seed(42)
random.seed(42)

# ——————————————————————————————————————————————————————————————————————————————

# 1. 数据获取与预处理（原始版本 - 包含数据泄露）
print("=== 加载预处理后的数据（winsorized版本）===")
print("注意：此版本包含数据泄露问题，仅用于对比学习")

# 直接读取最终的winsorized数据（包含未来信息泄露）
data_path = './cache/TSLA_5min_interday_merged_factors_winsorized_v2.xlsx'
df = pd.read_excel(data_path)

print(f"数据形状: {df.shape}")
print("数据预览:")
print(df.head())

# 检查数据
print(f"\n特征（列）数: {df.shape[1]}")
print(f"样本（行）数: {df.shape[0]}")

# ——————————————————————————————————————————————————————————————————————————————

# 2. 特征选择和目标变量设置
exclude_cols = ['datetime', 'date', 'future_ret_1d']
features = [col for col in df.columns if col not in exclude_cols]

print(f"选择的特征数量: {len(features)}")
print("特征列表:")
for i, feature in enumerate(features):
    print(f"{i+1:2d}. {feature}")

# 提取特征和目标变量
X = df[features].values
y = df['future_ret_1d'].values

print(f"\n特征矩阵形状: {X.shape}")
print(f"目标变量形状: {y.shape}")

# 移除包含NaN的行
nan_mask = ~(np.isnan(X).any(axis=1) | np.isnan(y))
X = X[nan_mask]
y = y[nan_mask]

print(f"清理后特征矩阵形状: {X.shape}")
print(f"清理后目标变量形状: {y.shape}")

# ——————————————————————————————————————————————————————————————————————————————

# 3. 数据集划分（随机划分 - 包含数据泄露）
from sklearn.model_selection import train_test_split

print("\n=== 数据集划分（随机划分 - 存在数据泄露）===")
print("警告：使用随机划分会导致未来信息泄露到训练集")

# 随机划分数据集（60%训练，20%验证，20%测试）
X_train_val, X_test, y_train_val, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42
)

X_train, X_val, y_train, y_val = train_test_split(
    X_train_val, y_train_val, test_size=0.25, random_state=42  # 0.25 * 0.8 = 0.2 总体
)

print(f"训练集形状: X_train: {X_train.shape}, y_train: {y_train.shape}")
print(f"验证集形状: X_val: {X_val.shape}, y_val: {y_val.shape}")
print(f"测试集形状: X_test: {X_test.shape}, y_test: {y_test.shape}")

# ——————————————————————————————————————————————————————————————————————————————

# 4. 模型训练与超参数优化
import copy
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.ensemble import RandomForestRegressor
from sklearn.neural_network import MLPRegressor
from xgboost import XGBRegressor
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import ParameterGrid
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler
import optuna

print("\n=== 开始模型训练（原始版本）===")

# 4.1 训练线性模型
print("\n--- 训练线性回归模型 ---")

# 建立 Pipeline（先缩放，再线性回归）
pipeline_lr = Pipeline([
    ('scaler', StandardScaler()),
    ('lr', LinearRegression())
])

# 定义线性模型的超参数搜索范围
param_grid_lr = {
    'lr__fit_intercept': [True, False]
}

# 遍历所有参数组合，寻找最佳线性模型（在验证集上评估）
best_score_lr = float('-inf')
best_params_lr = None
best_pipeline_lr = None

for params in ParameterGrid(param_grid_lr):
    pipeline_lr.set_params(**params)
    pipeline_lr.fit(X_train, y_train)

    # 在验证集上进行预测和评估
    valid_pred_lr = pipeline_lr.predict(X_val)
    valid_r2_lr = r2_score(y_val, valid_pred_lr)

    if valid_r2_lr > best_score_lr:
        best_score_lr = valid_r2_lr
        best_params_lr = params
        # 复制当前 pipeline，保存最佳模型
        best_pipeline_lr = copy.deepcopy(pipeline_lr)

print("最佳参数：", best_params_lr)
print("最佳验证集R²：", best_score_lr)

# 使用最佳模型在训练集和测试集上评估
y_pred_train_lr = best_pipeline_lr.predict(X_train)
y_pred_test_lr = best_pipeline_lr.predict(X_test)

train_mse_lr = mean_squared_error(y_train, y_pred_train_lr)
test_mse_lr = mean_squared_error(y_test, y_pred_test_lr)
train_r2_lr = r2_score(y_train, y_pred_train_lr)
test_r2_lr = r2_score(y_test, y_pred_test_lr)

print("==== 线性模型 - 训练集 ====")
print("MSE:", train_mse_lr)
print("R2: ", train_r2_lr)

print("==== 线性模型 - 测试集 ====")
print("MSE:", test_mse_lr)
print("R2: ", test_r2_lr)

# 4.2 训练随机森林
print("\n--- 训练随机森林模型 ---")

def objective_rf(trial):
    params = {
        'max_depth': trial.suggest_int('max_depth', 3, 10),
        'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
        'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', 0.3, 0.5, 1.0]),
        'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
        'min_samples_split': trial.suggest_int('min_samples_split', 2, 10),
        'random_state': 42
    }
    
    # 使用标准化的数据
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    
    model = RandomForestRegressor(**params)
    model.fit(X_train_scaled, y_train)
    preds = model.predict(X_val_scaled)
    return -mean_squared_error(y_val, preds)

study_rf = optuna.create_study(direction='minimize')
study_rf.optimize(objective_rf, n_trials=30)
best_params_rf = study_rf.best_params

# 创建最佳随机森林pipeline
best_pipeline_rf = Pipeline([
    ('scaler', StandardScaler()),
    ('rf', RandomForestRegressor(**best_params_rf))
])
best_pipeline_rf.fit(X_train, y_train)

# 使用最佳模型在训练集和测试集上评估
y_pred_train_rf = best_pipeline_rf.predict(X_train)
y_pred_test_rf = best_pipeline_rf.predict(X_test)

train_mse_rf = mean_squared_error(y_train, y_pred_train_rf)
test_mse_rf = mean_squared_error(y_test, y_pred_test_rf)
train_r2_rf = r2_score(y_train, y_pred_train_rf)
test_r2_rf = r2_score(y_test, y_pred_test_rf)

print("==== 随机森林 - 训练集 ====")
print("MSE:", train_mse_rf)
print("R2 :", train_r2_rf)

print("==== 随机森林 - 测试集 ====")
print("MSE:", test_mse_rf)
print("R2 :", test_r2_rf)

# 查看特征重要性
rf_importances = best_pipeline_rf.named_steps['rf'].feature_importances_
print("\nTop 10 Feature Importances (RandomForest):")
rf_imp_dict = {features[i]: rf_importances[i] for i in range(len(features))}
sorted_rf_imp = sorted(rf_imp_dict.items(), key=lambda x: x[1], reverse=True)
for feat, imp in sorted_rf_imp[:10]:
    print(f"{feat} -> {imp:.4f}")

# 4.3 训练XGBoost
print("\n--- 训练XGBoost模型 ---")

def objective_xgb(trial):
    params = {
        'learning_rate': trial.suggest_float('learning_rate', 0.001, 0.1, log=True),
        'max_depth': trial.suggest_int('max_depth', 3, 8),
        'n_estimators': trial.suggest_int('n_estimators', 100, 500),
        'subsample': trial.suggest_float('subsample', 0.6, 1.0),
        'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
        'reg_alpha': trial.suggest_float('reg_alpha', 0, 1.0),
        'reg_lambda': trial.suggest_float('reg_lambda', 0.1, 10.0),
        'random_state': 42,
        'verbosity': 0
    }
    
    # 使用标准化的数据
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    
    model = XGBRegressor(**params)
    model.fit(X_train_scaled, y_train)
    preds = model.predict(X_val_scaled)
    return -mean_squared_error(y_val, preds)

study_xgb = optuna.create_study(direction='minimize')
study_xgb.optimize(objective_xgb, n_trials=30)
best_params_xgb = study_xgb.best_params

# 创建最佳XGBoost pipeline
best_pipeline_xgb = Pipeline([
    ('scaler', StandardScaler()),
    ('xgb', XGBRegressor(**best_params_xgb))
])
best_pipeline_xgb.fit(X_train, y_train)

# 使用最佳模型在训练集和测试集上评估
y_pred_train_xgb = best_pipeline_xgb.predict(X_train)
y_pred_test_xgb = best_pipeline_xgb.predict(X_test)

train_mse_xgb = mean_squared_error(y_train, y_pred_train_xgb)
test_mse_xgb = mean_squared_error(y_test, y_pred_test_xgb)
train_r2_xgb = r2_score(y_train, y_pred_train_xgb)
test_r2_xgb = r2_score(y_test, y_pred_test_xgb)

print("==== XGBoost - 训练集 ====")
print("MSE:", train_mse_xgb)
print("R2: ", train_r2_xgb)

print("==== XGBoost - 测试集 ====")
print("MSE:", test_mse_xgb)
print("R2: ", test_r2_xgb)

# 查看特征重要性
xgb_importances = best_pipeline_xgb.named_steps['xgb'].feature_importances_
print("\nTop 10 Feature Importances (XGBoost):")
xgb_imp_dict = {features[i]: xgb_importances[i] for i in range(len(features))}
sorted_xgb_imp = sorted(xgb_imp_dict.items(), key=lambda x: x[1], reverse=True)
for feat, imp in sorted_xgb_imp[:10]:
    print(f"{feat} -> {imp:.4f}")

# 4.4 训练MLP
print("\n--- 训练MLP模型 ---")

def objective_mlp(trial):
    params = {
        'hidden_layer_sizes': trial.suggest_categorical('hidden_layer_sizes', [(64, 32), (128, 64), (64, 64), (128, 128), (256, 128)]),
        'activation': trial.suggest_categorical('activation', ['relu', 'tanh']),
        'alpha': trial.suggest_float('alpha', 1e-5, 1e-2, log=True),
        'learning_rate_init': trial.suggest_float('learning_rate_init', 1e-4, 1e-2, log=True),
        'max_iter': 1000,
        'early_stopping': True,
        'n_iter_no_change': 20,
        'batch_size': trial.suggest_categorical('batch_size', [32, 64, 128, 'auto']),
        'random_state': 42
    }
    
    # 使用标准化的数据
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    
    model = MLPRegressor(**params)
    model.fit(X_train_scaled, y_train)
    preds = model.predict(X_val_scaled)
    return -mean_squared_error(y_val, preds)

study_mlp = optuna.create_study(direction='minimize')
study_mlp.optimize(objective_mlp, n_trials=30)
best_params_mlp = study_mlp.best_params

# 创建最佳MLP pipeline
best_pipeline_mlp = Pipeline([
    ('scaler', StandardScaler()),
    ('mlp', MLPRegressor(**best_params_mlp))
])
best_pipeline_mlp.fit(X_train, y_train)

# 使用最优模型在训练集和测试集上评估
y_pred_train_mlp = best_pipeline_mlp.predict(X_train)
y_pred_test_mlp = best_pipeline_mlp.predict(X_test)

train_mse_mlp = mean_squared_error(y_train, y_pred_train_mlp)
test_mse_mlp = mean_squared_error(y_test, y_pred_test_mlp)
train_r2_mlp = r2_score(y_train, y_pred_train_mlp)
test_r2_mlp = r2_score(y_test, y_pred_test_mlp)

print("==== MLP - 训练集 ====")
print("MSE:", train_mse_mlp)
print("R2: ", train_r2_mlp)

print("==== MLP - 测试集 ====")
print("MSE:", test_mse_mlp)
print("R2: ", test_r2_mlp)

# ——————————————————————————————————————————————————————————————————————————————

# 5. 模型集成与权重优化（用凸优化）
print("\n=== 模型集成与权重优化 ===")

import cvxpy as cp

def optimize_weights_constrained(
        models,
        X_val,
        y_val,
        sum_to_1=True,
        nonnegative=True,
        alpha_l1=0.0,
        alpha_l2=0.0,
        verbose=True
):
    """
    用凸优化方式在验证集上寻找一组最优权重，使得加权后的预测最小化 MSE
    """
    # 1) 先得到在验证集上的预测矩阵 predictions: shape (N, M)
    predictions = np.column_stack([model.predict(X_val) for model in models])
    N, M = predictions.shape

    # 2) 定义优化变量 w: 大小 M
    if nonnegative:
        w = cp.Variable(M, nonneg=True)
    else:
        w = cp.Variable(M)

    # 3) 定义约束列表 constraints
    constraints = []
    if sum_to_1:
        constraints.append(cp.sum(w) == 1)

    # 4) 定义目标函数（最小化 MSE + 正则项）
    residual = y_val - predictions @ w
    obj_mse = cp.sum_squares(residual)

    # 若要加 L1 正则：alpha_l1 * ||w||_1
    # 若要加 L2 正则：alpha_l2 * ||w||_2^2
    obj_reg = 0
    if alpha_l1 > 0:
        obj_reg += alpha_l1 * cp.norm1(w)
    if alpha_l2 > 0:
        obj_reg += alpha_l2 * cp.norm2(w) ** 2

    # 最终目标：Minimize(MSE + 正则)
    objective = cp.Minimize(obj_mse + obj_reg)

    # 5) 构建并求解凸优化问题
    problem = cp.Problem(objective, constraints)
    result = problem.solve(verbose=verbose)

    # 6) 拿到最优权重 w_opt
    w_opt = w.value
    # 计算该组合在验证集上的 r2_score
    y_val_pred = predictions @ w_opt
    score_r2 = r2_score(y_val, y_val_pred)

    if verbose:
        print(f"Optimal objective (MSE + reg) = {problem.value:.6f}")
        print("Optimized weights:", w_opt)
        print(f"sum of weights = {w_opt.sum():.4f}")
        print(f"R2 on validation set = {score_r2:.4f}")

    return w_opt, score_r2

# 使用示例
w_constrained, r2_constrained = optimize_weights_constrained(
    models=[best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb, best_pipeline_mlp],
    X_val=X_val,
    y_val=y_val,
    sum_to_1=True,
    nonnegative=True,
    alpha_l1=0.0,
    alpha_l2=1e-3,
    verbose=True
)

print("得到的约束权重 =", [f"{num:0.2f}" for num in w_constrained])
print("验证集 R² =", r2_constrained)

# 得到测试集上各模型的预测矩阵
predictions_test = np.column_stack([model.predict(X_test) for model in
                                    [best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb,
                                     best_pipeline_mlp]])

# 利用之前优化得到的权重 w_constrained 对测试集预测进行加权组合
y_test_pred = predictions_test @ w_constrained

# 计算测试集上的 R² 分数
r2_test = r2_score(y_test, y_test_pred)
print("测试集 R² =", r2_test)

# ——————————————————————————————————————————————————————————————————————————————

# 6. 结果可视化
print("\n=== 结果可视化 ===")

# 创建结果目录
results_dir = '/Users/<USER>/MLQuant/Quant_ML_Struc/cache/Results'
os.makedirs(results_dir, exist_ok=True)

# 绘制模型性能对比
plt.figure(figsize=(15, 10))

# 1. 测试集R²对比
plt.subplot(2, 3, 1)
models_names = ['Linear', 'RandomForest', 'XGBoost', 'MLP', 'Ensemble']
r2_scores = [test_r2_lr, test_r2_rf, test_r2_xgb, test_r2_mlp, r2_test]
colors = ['blue', 'green', 'orange', 'red', 'purple']
bars = plt.bar(models_names, r2_scores, color=colors, alpha=0.7)
plt.title('测试集 R² 分数对比')
plt.ylabel('R² Score')
plt.xticks(rotation=45)
for i, v in enumerate(r2_scores):
    plt.text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')

# 2. 集成权重可视化
plt.subplot(2, 3, 2)
model_names = ['Linear', 'RF', 'XGBoost', 'MLP']
plt.pie(w_constrained, labels=model_names, autopct='%1.1f%%', startangle=90)
plt.title('集成模型权重分布')

# 3. 预测值 vs 真实值散点图（测试集）
plt.subplot(2, 3, 3)
plt.scatter(y_test, y_test_pred, alpha=0.5)
plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
plt.xlabel('真实值')
plt.ylabel('预测值')
plt.title(f'集成模型预测效果 (R²={r2_test:.3f})')

# 4. 残差分布
plt.subplot(2, 3, 4)
residuals = y_test - y_test_pred
plt.hist(residuals, bins=30, alpha=0.7, color='skyblue')
plt.xlabel('残差')
plt.ylabel('频次')
plt.title('残差分布')

# 5. 各模型预测对比（前100个样本）
plt.subplot(2, 3, 5)
sample_size = min(100, len(y_test))
x_axis = range(sample_size)
plt.plot(x_axis, y_test[:sample_size], label='True', linewidth=2, color='black')
plt.plot(x_axis, best_pipeline_lr.predict(X_test)[:sample_size], label='Linear', alpha=0.7)
plt.plot(x_axis, best_pipeline_rf.predict(X_test)[:sample_size], label='RF', alpha=0.7)
plt.plot(x_axis, best_pipeline_xgb.predict(X_test)[:sample_size], label='XGBoost', alpha=0.7)
plt.plot(x_axis, best_pipeline_mlp.predict(X_test)[:sample_size], label='MLP', alpha=0.7)
plt.plot(x_axis, y_test_pred[:sample_size], label='Ensemble', linewidth=2, color='red')
plt.xlabel('样本索引')
plt.ylabel('收益率')
plt.title('各模型预测对比 (前100个样本)')
plt.legend()

# 6. 特征重要性对比（RF vs XGBoost）
plt.subplot(2, 3, 6)
top_features = 10
rf_top = dict(sorted_rf_imp[:top_features])
xgb_top = dict(sorted_xgb_imp[:top_features])

x_pos = np.arange(len(rf_top))
width = 0.35

plt.bar(x_pos - width/2, list(rf_top.values()), width, label='RandomForest', alpha=0.7)
plt.bar(x_pos + width/2, [xgb_top.get(feat, 0) for feat in rf_top.keys()], width, label='XGBoost', alpha=0.7)
plt.xlabel('特征')
plt.ylabel('重要性')
plt.title(f'Top {top_features} 特征重要性对比')
plt.xticks(x_pos, list(rf_top.keys()), rotation=45, ha='right')
plt.legend()

plt.tight_layout()
plt.savefig(os.path.join(results_dir, 'ensemble_model_performance_v1.0.png'), dpi=300, bbox_inches='tight')
plt.show()

# ——————————————————————————————————————————————————————————————————————————————

# 7. 保存模型
print("\n=== 保存模型 ===")

import joblib

# 创建models目录
models_dir = '/Users/<USER>/MLQuant/Quant_ML_Struc/cache/models'
os.makedirs(models_dir, exist_ok=True)

# 构建集成模型组件
ensemble_model = {
    'lr': best_pipeline_lr,
    'rf': best_pipeline_rf,
    'xgb': best_pipeline_xgb,
    'mlp': best_pipeline_mlp,
    'weights': w_constrained,
    'features': features,
    'scaler': StandardScaler().fit(X_train)  # 保存标准化器
}

# 保存到models目录
ensemble_model_path = os.path.join(models_dir, 'ensemble_model_v1.0.pkl')
features_path = os.path.join(models_dir, 'features_v1.0.pkl')

joblib.dump(ensemble_model, ensemble_model_path)
joblib.dump(features, features_path)

print(f"集成模型已保存到: {ensemble_model_path}")
print(f"特征列表已保存到: {features_path}")

# ——————————————————————————————————————————————————————————————————————————————

# 8. 模型性能总结
print("\n" + "="*60)
print("模型性能总结 - 原始版本 1.0")
print("="*60)
print("注意：此版本包含数据泄露问题，性能可能被高估")
print("="*60)

performance_summary = {
    'Model': ['Linear Regression', 'Random Forest', 'XGBoost', 'MLP', 'Ensemble'],
    'Train R²': [train_r2_lr, train_r2_rf, train_r2_xgb, train_r2_mlp, '-'],
    'Test R²': [test_r2_lr, test_r2_rf, test_r2_xgb, test_r2_mlp, r2_test],
    'Train MSE': [train_mse_lr, train_mse_rf, train_mse_xgb, train_mse_mlp, '-'],
    'Test MSE': [test_mse_lr, test_mse_rf, test_mse_xgb, test_mse_mlp, mean_squared_error(y_test, y_test_pred)]
}

summary_df = pd.DataFrame(performance_summary)
print(summary_df.to_string(index=False))

print(f"\n集成模型权重:")
for i, (model_name, weight) in enumerate(zip(['Linear', 'RF', 'XGBoost', 'MLP'], w_constrained)):
    print(f"{model_name}: {weight:.3f}")

print(f"\n最佳集成模型测试集 R²: {r2_test:.4f}")
print(f"最佳集成模型测试集 MSE: {mean_squared_error(y_test, y_test_pred):.6f}")

print("\n" + "="*60)
print("警告：此版本使用随机数据分割和winsorized数据")
print("存在严重的数据泄露问题，不适用于实际交易")
print("请使用修复版本进行实际应用")
print("="*60) 