"""
🎯 自定义Alpha因子生成示例
📈 展示如何使用模块化因子生成器创建自定义因子

使用场景:
1. 快速修改特定类型的因子
2. 添加新的因子计算逻辑
3. 测试不同参数组合的效果

作者: Vincent
版本: 1.0
"""

import sys
import os
sys.path.append('/Users/<USER>/MLQuant/Quant_ML_Struc/2_Feature_Engineering')

import pandas as pd
import numpy as np
from ModularAlphaFactors import ModularAlphaFactors
from AutoAlphaFactorsMining_Clean import TSLADataLoader, AlphaFactorDetector, StatisticalIndicatorExplainer
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class CustomAlphaFactors(ModularAlphaFactors):
    """
    自定义Alpha因子生成器
    继承自ModularAlphaFactors，可以重写任何方法
    """
    
    def __init__(self, data: pd.DataFrame):
        super().__init__(data)
        print("🎯 初始化自定义Alpha因子生成器")
    
    def momentum_factors(self):
        """
        🚀 自定义动量因子
        示例：只生成短期动量因子，并添加新的计算方法
        """
        logging.info("生成自定义动量因子...")
        
        # === 修改1: 只使用短期动量周期 ===
        momentum_periods = [3, 5, 7]  # 原来是 [3, 5, 7, 10, 15, 20, 30]
        for period in momentum_periods:
            factor_name = f'momentum_{period}d'
            self.factors[factor_name] = self.close / self.close.shift(period) - 1
            self.factor_descriptions[factor_name] = f'{period}日价格动量'
        
        # === 修改2: 添加对数收益率动量 ===
        log_momentum_periods = [3, 5, 7]
        for period in log_momentum_periods:
            factor_name = f'log_momentum_{period}d'
            self.factors[factor_name] = np.log(self.close / self.close.shift(period))
            self.factor_descriptions[factor_name] = f'{period}日对数动量'
        
        # === 修改3: 添加动量强度因子 ===
        for period in [5, 10]:
            factor_name = f'momentum_strength_{period}d'
            momentum = self.close / self.close.shift(period) - 1
            momentum_std = momentum.rolling(period).std()
            self.factors[factor_name] = momentum / momentum_std
            self.factor_descriptions[factor_name] = f'{period}日动量强度'
        
        # === 修改4: 添加动量持续性 ===
        for period in [5, 10]:
            factor_name = f'momentum_persistence_{period}d'
            momentum = self.close / self.close.shift(period) - 1
            momentum_sign = np.sign(momentum)
            persistence = momentum_sign.rolling(period).sum() / period
            self.factors[factor_name] = persistence
            self.factor_descriptions[factor_name] = f'{period}日动量持续性'
    
    def volume_factors(self):
        """
        📊 自定义成交量因子
        示例：添加成交量异常检测和成交量动量
        """
        logging.info("生成自定义成交量因子...")
        
        # === 保留原有的基础成交量因子 ===
        super().volume_factors()
        
        # === 添加1: 成交量异常检测 ===
        volume_anomaly_periods = [10, 20]
        for period in volume_anomaly_periods:
            factor_name = f'volume_anomaly_{period}d'
            vol_mean = self.volume.rolling(period).mean()
            vol_std = self.volume.rolling(period).std()
            z_score = (self.volume - vol_mean) / vol_std
            self.factors[factor_name] = z_score
            self.factor_descriptions[factor_name] = f'{period}日成交量异常度'
        
        # === 添加2: 成交量动量 ===
        volume_momentum_periods = [5, 10, 20]
        for period in volume_momentum_periods:
            factor_name = f'volume_momentum_{period}d'
            self.factors[factor_name] = self.volume / self.volume.shift(period) - 1
            self.factor_descriptions[factor_name] = f'{period}日成交量动量'
        
        # === 添加3: 价格成交量效率 ===
        pv_efficiency_periods = [5, 10]
        for period in pv_efficiency_periods:
            factor_name = f'price_volume_efficiency_{period}d'
            price_change = abs(self.close.pct_change(period))
            volume_change = abs(self.volume.pct_change(period))
            efficiency = price_change / (volume_change + 1e-8)  # 避免除零
            self.factors[factor_name] = efficiency
            self.factor_descriptions[factor_name] = f'{period}日价量效率'
    
    def custom_technical_factors(self):
        """
        🔧 全新的技术因子类别
        示例：添加完全自定义的技术指标
        """
        logging.info("生成自定义技术因子...")
        
        # === 自定义1: 价格加速度 ===
        acceleration_periods = [5, 10]
        for period in acceleration_periods:
            factor_name = f'price_acceleration_{period}d'
            velocity = self.close.diff(period)
            acceleration = velocity.diff(period)
            self.factors[factor_name] = acceleration / self.close
            self.factor_descriptions[factor_name] = f'{period}日价格加速度'
        
        # === 自定义2: 波动率偏斜度 ===
        skew_periods = [10, 20]
        for period in skew_periods:
            factor_name = f'return_skewness_{period}d'
            returns = self.close.pct_change()
            skewness = returns.rolling(period).skew()
            self.factors[factor_name] = skewness
            self.factor_descriptions[factor_name] = f'{period}日收益率偏斜度'
        
        # === 自定义3: 价格密度 ===
        density_periods = [10, 20]
        for period in density_periods:
            factor_name = f'price_density_{period}d'
            price_range = self.high.rolling(period).max() - self.low.rolling(period).min()
            trading_days = period
            density = trading_days / (price_range / self.close + 1e-8)
            self.factors[factor_name] = density
            self.factor_descriptions[factor_name] = f'{period}日价格密度'
        
        # === 自定义4: 市场微观结构因子 ===
        # 开盘缺口持续性
        gap_persistence_periods = [5, 10]
        for period in gap_persistence_periods:
            factor_name = f'gap_persistence_{period}d'
            gap = (self.open - self.close.shift(1)) / self.close.shift(1)
            intraday_move = (self.close - self.open) / self.open
            persistence = (gap * intraday_move).rolling(period).mean()
            self.factors[factor_name] = persistence
            self.factor_descriptions[factor_name] = f'{period}日缺口持续性'
    
    def generate_all_factors(self):
        """重写生成所有因子的方法，包含自定义因子"""
        logging.info("开始生成所有自定义Alpha因子...")
        
        # 生成基础因子（使用自定义的方法）
        self.momentum_factors()      # 使用自定义的动量因子
        self.volume_factors()        # 使用自定义的成交量因子
        self.volatility_factors()    # 使用父类的波动率因子
        self.price_structure_factors()  # 使用父类的价格结构因子
        self.mean_reversion_factors()   # 使用父类的均值回复因子
        self.complex_factors()          # 使用父类的复杂因子
        
        # 添加全新的自定义因子
        self.custom_technical_factors()
        
        logging.info(f"成功生成 {len(self.factors.columns)} 个自定义Alpha因子")
        return self.factors, self.factor_descriptions


def main():
    """主函数：演示自定义因子生成和检测"""
    print("🎯 自定义Alpha因子生成示例")
    print("="*60)
    
    try:
        # 1. 加载数据
        print("📊 步骤1: 加载TSLA数据...")
        loader = TSLADataLoader()
        data = loader.load_daily_data()
        
        # 2. 使用自定义因子生成器
        print("\n🔧 步骤2: 生成自定义Alpha因子...")
        custom_generator = CustomAlphaFactors(data)
        custom_factors, custom_descriptions = custom_generator.generate_all_factors()
        
        # 合并原始数据和自定义因子
        data_with_custom_factors = pd.concat([data, custom_factors], axis=1)
        
        print(f"✅ 自定义因子生成完成!")
        print(f"   - 原始特征: {len(data.columns)} 个")
        print(f"   - 生成因子: {len(custom_factors.columns)} 个")
        print(f"   - 总特征数: {len(data_with_custom_factors.columns)} 个")
        
        # 3. 显示统计指标解释
        StatisticalIndicatorExplainer.print_statistical_explanation()
        
        # 4. 检测自定义因子
        print("\n🔍 步骤3: 检测自定义Alpha因子...")
        detector = AlphaFactorDetector(data_with_custom_factors)
        detection_results = detector.detect_alpha_factors(min_periods=50)
        
        # 5. 显示结果
        if not detection_results.empty:
            print(f"\n🏆 前10个最佳自定义Alpha因子:")
            print("-" * 100)
            print(f"{'排名':<4} {'因子名称':<30} {'R²':<8} {'t值':<8} {'p值':<10} {'显著性':<6}")
            print("-" * 100)
            
            for i, (_, factor) in enumerate(detection_results.head(10).iterrows(), 1):
                print(f"{i:<4} {factor['factor_name'][:29]:<30} "
                      f"{factor['r_squared']:<8.4f} "
                      f"{factor['t_value']:<8.2f} "
                      f"{factor['p_value']:<10.6f} "
                      f"{factor['significance_level']:<6}")
            
            # 统计摘要
            total_factors = len(detection_results)
            significant_factors = len(detection_results[detection_results['p_value'] < 0.05])
            
            print(f"\n📊 自定义因子统计摘要:")
            print(f"   - 总因子数量: {total_factors}")
            print(f"   - 显著因子数量: {significant_factors} ({significant_factors/total_factors*100:.1f}%)")
            print(f"   - 平均R²: {detection_results['r_squared'].mean():.4f}")
        
        print("\n🎉 自定义Alpha因子分析完成!")
        print("💡 提示：您可以继续修改CustomAlphaFactors类来测试不同的因子组合")
        
    except Exception as e:
        logging.error(f"程序执行失败: {e}")
        print(f"❌ 错误: {e}")
        raise


if __name__ == "__main__":
    main()
