# 1. 导入依赖包

# 导入yfinance库，用于获取金融数据
import yfinance as yf
# 导入numpy库，用于数值计算
import numpy as np
# 导入pandas库，用于数据处理和分析
import pandas as pd
# 导入线性回归模型
from sklearn.linear_model import LinearRegression
# 导入均方误差计算函数
from sklearn.metrics import mean_squared_error
# 导入matplotlib库，用于绘图
import matplotlib.pyplot as plt
# 导入seaborn库，用于高级数据可视化
import seaborn as sns
# 导入datetime和timedelta，用于处理日期和时间
from datetime import datetime, timedelta

# 导入os库，用于处理文件路径和目录
import os
# 导入talib库，用于技术指标计算
import talib
# 导入sys库，用于处理系统相关的操作
import sys

# 导入dotenv库，用于加载环境变量
from dotenv import load_dotenv, find_dotenv
# 查找并加载.env文件，用于读取环境变量
dotenv_path = find_dotenv("../../.env")
# 显式加载.env文件
load_dotenv(dotenv_path)

# 将父目录添加到sys.path中，以便导入自定义模块
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))

# 导入自定义模块，用于数据处理、绘图、策略和回测
from data_processing import load_data_multi_year, flatten_yf_columns, standardize_columns
from plotting import plot_results
from strategy.buy_and_hold import BuyAndHoldStrategy
from back_test import run_backtest
# 导入backtrader库，用于回测
import backtrader as bt

# 设置matplotlib的后端为TkAgg，确保兼容性
import matplotlib
matplotlib.use('TkAgg')  # 强制使用TkAgg后端，兼容性最好

# 设置pandas的显示选项，控制浮点数的显示格式
pd.set_option('display.float_format', lambda x: '%.4f' % x)
# 设置绘图风格为seaborn-v0_8-bright
plt.style.use('seaborn-v0_8-bright')
# 设置中文字体显示
plt.rcParams['font.sans-serif'] = ['PingFang HK']
plt.rcParams['axes.unicode_minus'] = False

# 导入random库，用于生成随机数
import random
# 固定全局随机种子，确保结果可复现
SEED = 42
random.seed(SEED)
np.random.seed(SEED)  

# 导入torch库，用于深度学习
import torch
# 定义一个函数，用于设置所有随机种子
def seed_everything(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)

    # 如果使用GPU，需要额外设置GPU的随机种子
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)

    # 保证cuDNN的确定性
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

# 调用seed_everything函数，设置所有随机种子
seed_everything(SEED)

# 打印当前工作目录
print("当前工作目录：", os.getcwd())


# 2. 数据准备与特征工程
# 读取5分钟级别的TSLA数据
df_5min = pd.read_excel("/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_5min_merged_raw.xlsx")

# 获取当前脚本所在的目录
script_dir = os.path.dirname(os.path.abspath(__file__))
# 定义缓存目录
cache_dir = os.path.join(script_dir, "cache")
# 读取日级别的TSLA数据
df_day = pd.read_excel("Quant_ML_Struc/cache/TSLA_day.xlsx")

# 打印日级别数据的列名
print(df_day.columns)

# 确保5分钟级别的数据包含所需的列
required_columns = ["datetime", "open", "high", "low", "close", "volume"]
if not all(col in df_5min.columns for col in required_columns):
    raise ValueError("Excel file does not contain the required columns.")

# 将'timestamp'列转换为datetime类型
df_5min['datetime'] = pd.to_datetime(df_5min['datetime'])
# 将'datetime'列设置为索引
df_5min.set_index('datetime', inplace=True)

# 计算5分钟级别的收益率
df_5min['ret_5min'] = df_5min['close'].pct_change()
# 计算未来5分钟级别的收益率
df_5min['future_ret_5min'] = df_5min['close'].shift(-1) / df_5min['close'] - 1

# 打印缓存数据的前几行
print("\n缓存数据预览: ")
print(df_5min.head())

# 3. 特征生成
# 定义一个空列表，用于存储生成的特征
factors_list = []

# 按日期分组处理5分钟级别的数据
for trading_date, group in df_5min.groupby(df_5min.index.date):
    # 获取当天的开盘价
    day_open = group.iloc[0]['open']
    # 获取当天的收盘价
    day_close = group.iloc[-1]['close']
    # 获取当天的最高价
    day_high = group['high'].max()
    # 获取当天的最低价
    day_low = group['low'].min()
    # 计算当天的总成交量
    total_vol = group['volume'].sum()

    # 计算典型价格（high + low + close）/ 3
    group['typical_price'] = (group['high'] + group['low'] + group['close']) / 3
    # 计算价格与成交量的加权和
    sum_price_vol = (group['typical_price'] * group['volume']).sum()
    # 计算当天的VWAP（成交量加权平均价）
    daily_vwap = sum_price_vol / total_vol if total_vol > 0 else np.nan

    # 计算收盘价相对于VWAP的偏离
    close_vawp_dev = (day_close - daily_vwap) / daily_vwap if daily_vwap and not np.isnan(daily_vwap) else np.nan

    # 定义极端波动的阈值
    threshold = 0.01
    # 判断每个5分钟bar是否为极端波动
    group['is_extreme'] = group['ret_5min'].abs() > threshold
    # 计算当天极端波动的次数
    extreme_bar_count = group['is_extreme'].sum()

    # 计算当天的累积最高价
    group['cum_max_high'] = group['high'].cummax()
    # 判断每个5分钟bar是否为新高
    group['is_new_high'] = group['high'] > group['cum_max_high'].shift(1).fillna(-np.inf)
    # 计算当天新高的次数
    new_high_count = group['is_new_high'].sum()

    # 计算当天的累积最低价
    group['cum_min_low'] = group['low'].cummin()
    # 判断每个5分钟bar是否为新低
    group['is_new_low'] = group['low'] < group['cum_min_low'].shift(1).fillna(np.inf)
    # 计算当天新低的次数
    new_low_count = group['is_new_low'].sum()

    # 找到当天最高价和最低价出现的时间
    idx_high = group['high'].idxmax()
    idx_low = group['low'].idxmin()
    # 将时间转换为小时和分钟的小数形式
    time_of_high = idx_high.hour + idx_high.minute / 60.0
    time_of_low = idx_low.hour + idx_low.minute / 60.0

    # 计算Intraday Intensity Index
    group['ii_bar'] = ((2 * group['close'] - group['high'] - group['low']) / ((group['high'] - group['low']).replace(0, np.nan) )) * group['volume']
    # 计算当天的Intraday Intensity Index
    ii_sum = group['ii_bar'].sum()
    intraday_intensity = ii_sum / total_vol if total_vol > 0 else np.nan

    # 计算当天的已实现波动率
    realized_vol = np.sqrt(np.sum(group['ret_5min']**2))
    # 计算当天的日内波动幅度（高低点相对于开盘价的百分比）
    intraday_range_pct = (day_high - day_low) / day_open * 100
    # 计算当天的日内收益率
    intraday_ret = day_close - day_open - 1

     # 将当天的数据分为前半段和后半段
    half_n = len(group) // 2
    group_am = group.iloc[:half_n]  # 前半段数据
    group_pm = group.iloc[half_n:]  # 后半段数据

    # 计算前半段的收益率
    am_ret = group_am.iloc[-1]['close']/day_open - 1 if len(group_am) else np.nan
    # 计算后半段的收益率
    pm_ret = day_close/group_am.iloc[-1]['close'] - 1 if len(group_am) else np.nan
    # 计算前半段的成交量占比
    am_vol_ratio = group_am['volume'].sum() / total_vol if total_vol else 0
    # 计算后半段的成交量占比
    pm_vol_ratio = group_pm['volume'].sum() / total_vol if total_vol else 0

    # 将生成的特征添加到factors_list中
    factors_list.append({
        'date': trading_date,  # 交易日期
        'daily_vwap': daily_vwap,  # 当天的VWAP
        'close_vwap_dev': close_vawp_dev,  # 收盘价相对于VWAP的偏离
        'extreme_bar_count': extreme_bar_count,  # 极端波动的次数
        'new_high_count': new_high_count,  # 新高的次数
        'new_low_count': new_low_count,  # 新低的次数
        'time_of_high': time_of_high,  # 最高价出现的时间
        'time_of_low': time_of_low,  # 最低价出现的时间
        'intraday_intensity': intraday_intensity,  # 当天的Intraday Intensity Index
        'realized_vol': realized_vol,  # 当天的已实现波动率
        'intraday_range_pct': intraday_range_pct,  # 当天的日内波动幅度
        'intraday_ret': intraday_ret,  # 当天的日内收益率
        'am_ret': am_ret,  # 前半段的收益率
        'pm_ret': pm_ret,  # 后半段的收益率
        'am_vol_ratio': am_vol_ratio,  # 前半段的成交量占比
        'pm_vol_ratio': pm_vol_ratio  # 后半段的成交量占比
    })

    
# 将生成的特征转换为DataFrame
df_factors = pd.DataFrame(factors_list)
# 将'date'列转换为datetime类型
df_factors['date'] = pd.to_datetime(df_factors['date'])
# 将日级别数据的'datetime'列转换为datetime类型
df_day['date'] = pd.to_datetime(df_day['datetime'])
# 将日级别数据与生成的特征数据按'date'列进行合并
df_merged = pd.merge(df_day, df_factors, on='date', how='left')
# 按'date'列对合并后的数据进行排序
df_merged.sort_values('date', inplace=True)

# 打印合并后的数据的前10行
print(df_merged.head(10))

# 定义输出目录
output_dir = "/MLQuant/Quant_ML_Struc/cache"
# 如果输出目录不存在，则创建
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 将合并后的数据保存为Excel文件
df_merged.to_excel(os.path.join(output_dir, "TSLA_merged_factors.xlsx"), index=False)

# 检查每一列是否有缺失值，并统计特征数和样本数
print("\n每列缺失值统计：")
print(df_merged.isnull().sum())
print(f"\n特征（列）数: {df_merged.shape[1]}")
print(f"样本（行）数: {df_merged.shape[0]}")

# 将'datetime'和'date'列统一格式化为YYYY-MM-DD
df_merged['datetime'] = pd.to_datetime(df_merged['datetime']).dt.strftime('%Y-%m-%d')
df_merged['date'] = pd.to_datetime(df_merged['date']).dt.strftime('%Y-%m-%d')

# 检查每行'datetime'和'date'是否一致
not_match = df_merged[df_merged['datetime'] != df_merged['date']]
print("\n前10个datetime和date不一致的行：")
print(not_match.head(10))

# 如果需要保存格式化后的数据，可以再次保存
df_merged.to_excel(os.path.join(output_dir, "TSLA_merged_factors.xlsx"), index=False)


#6. 加入日间因子
# 导入pandas库，用于数据处理
import pandas as pd
# 导入talib库，用于技术指标计算
import talib

# 读取之前保存的融合表
df_merged = pd.read_excel(os.path.join(output_dir, "TSLA_merged_factors.xlsx"))
# 将'date'列转换为datetime类型
df_merged['date'] = pd.to_datetime(df_merged['date'])

# 复制一份数据用于生成新因子
df = df_merged.copy()

# 生成新因子
# 将'Volume'列中的逗号去除并转换为浮点数
df['Volume'] = df['Volume'].astype(str).str.replace(',', '').astype(float)
# 计算5日动量
df['momentum_5_lower'] = df['Close'] / df['Close'].shift(5) - 1
# 计算成交量比率
df['vol_ratio_lower'] = (df['Volume'].rolling(5).mean()) / (df['Volume'].rolling(10).mean()) - 1
# 计算14日RSI
df['RSI_14_lower'] = talib.RSI(df['Close'], timeperiod=14)
# 计算布林带的上轨、中轨和下轨
upper, middle, lower = talib.BBANDS(df['Close'], timeperiod=20, nbdevup=2, nbdevdn=2, matype=0)
df['BB_upper'] = upper
df['BB_middle'] = middle
df['BB_lower'] = lower
# 计算3日动量
df['momentum_3_lower'] = df['Close'] / df['Close'].shift(3) - 1
# 计算10日动量
df['momentum_10_lower'] = df['Close'] / df['Close'].shift(10) - 1
# 计算1日反转
df['reversal_1'] = - (df['Close'].pct_change(1))
# 计算3日反转
df['reversal_3'] = - (df['Close'] / df['Close'].shift(3) - 1)

# 只保留新因子和'date'列
factor_cols = [
    'date', 'momentum_3_lower', 'momentum_5_lower', 'momentum_10_lower',
    'reversal_1', 'reversal_3', 'vol_ratio_lower', 'RSI_14_lower', 'BB_upper', 'BB_middle', 'BB_lower'
]
df_factors = df[factor_cols]

# 将新因子合并到原始表中，按'date'列进行左连接
df_final = pd.merge(df_merged, df_factors, on='date', how='left', suffixes=('', '_new'))

# 保存新表格
# 计算未来1日收益率
df_final['future_ret_1d'] = df_final['Close'].shift(-1) / df_final['Close'] - 1
# 将最终数据保存为Excel文件
df_final.to_excel(os.path.join(output_dir, "TSLA_5min_interday_merged_factors.xlsx"), index=False)
print("融合完成，已保存到 cache/TSLA_5min_interday_merged_factors.xlsx")

# 7. 数据清洗与保存
# 读取融合后的新表格
df = pd.read_excel(os.path.join(output_dir, "TSLA_5min_interday_merged_factors.xlsx"))

# 确保所有关键列为float类型
for col in df.columns:
    if col not in ['datetime', 'date']:
        # 将列转换为数值类型，无法转换的设置为NaN
        df[col] = pd.to_numeric(df[col], errors='coerce')
        # 将无穷大值替换为NaN
        df[col] = df[col].replace([np.inf, -np.inf], np.nan)
        # 将NaN值替换为0
        df[col] = df[col].fillna(0)

# 删除所有含有缺失值的行
df = df.dropna()

# 保存清洗后的数据为Excel文件
df.to_excel(os.path.join(output_dir, "TSLA_5min_interday_merged_factors_cleaned.xlsx"), index=False)

# 打印提示信息，表示已保存清洗后的数据
print("已保存清洗后的数据到 cache/TSLA_5min_interday_merged_factors_cleaned.xlsx")

# 导入pandas库，用于数据处理
import pandas as pd
# 导入re库，用于正则表达式操作
import re

# 读取数据，全部为字符串类型
df = pd.read_excel("/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_5min_interday_merged_factors.xlsx", dtype=str)

exclude_cols = ['datetime', 'date']  # 非数值列

for col in df.columns:
    if col not in exclude_cols:
        # 去除千分位逗号和其它非数字字符
        df[col] = df[col].astype(str).apply(lambda x: re.sub(r'[^\d\.\-eE]', '', x))
        # 空字符串和NA转为NaN
        df[col] = df[col].replace(['', pd.NA], pd.NA)
        # 转为float
        df[col] = pd.to_numeric(df[col], errors='coerce')

# 检查所有列的类型
print(df.dtypes)

# 删除所有含有缺失值的行
df = df.dropna()

# 保存为cleaned文件
df.to_excel("/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_5min_interday_merged_factors_cleaned.xlsx", index=False)
print("已保存清洗且去除缺失值后的数据到 cache/TSLA_5min_interday_merged_factors_cleaned.xlsx")


# 9. 描述性统计与正态性检验

# 导入必要的库
import pandas as pd  # 数据处理
import numpy as np   # 数值计算
import matplotlib.pyplot as plt  # 绘图
import seaborn as sns  # 高级可视化
from scipy import stats  # 统计检验
import math  # 数学工具

# 1. 读取清洗后的特征文件
file_path = "/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_5min_interday_merged_factors_cleaned.xlsx"  # 文件路径
df = pd.read_excel(file_path)  # 读取Excel文件为DataFrame

# 2. 缺失值统计
print("# 缺失值统计：")
missing_values = df.isnull().sum()  # 统计每一列的缺失值数量
print(missing_values)  # 输出缺失值统计

# 3. 描述性统计（count, mean, min, 25%, 50%, 75%, max）
print("# 描述性统计：")
desc_stats = df.describe().T  # .T转置方便查看
print(desc_stats)  # 输出每一列的统计信息

# 4. 选择数值型列（排除非数值列，如日期等）
numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()  # 只保留数值型列

# 5. 正态性检验函数（Shapiro-Wilk和D'Agostino K²）
def normality_tests(data, col):
    # 去除缺失值
    clean_data = data[col].dropna()  # 去除缺失值
    # Shapiro-Wilk检验（样本量≤5000时使用）
    if len(clean_data) <= 5000:
        stat_sw, p_sw = stats.shapiro(clean_data)
        sw_result = "符合正态分布" if p_sw > 0.05 else "不符合正态分布"
    else:
        stat_sw, p_sw, sw_result = None, None, "样本量>5000，跳过Shapiro-Wilk"
    # D'Agostino K²检验
    stat_da, p_da = stats.normaltest(clean_data)
    da_result = "符合正态分布" if p_da > 0.05 else "不符合正态分布"
    # 返回结果
    return {
        'Column': col,  # 列名
        'Shapiro-Wilk': f"p值={p_sw:.4f} ({sw_result})" if p_sw is not None else sw_result,  # Shapiro-Wilk检验结果
        'D\'Agostino K²': f"p值={p_da:.4f} ({da_result})",  # D'Agostino K²检验结果
        '偏度': f"{stats.skew(clean_data):.4f}",  # 偏度
        '峰度': f"{stats.kurtosis(clean_data):.4f}"  # 峰度
    }

# 6. 对所有数值列做正态性检验
normality_results = [normality_tests(df, col) for col in numeric_cols]  # 列表推导
normality_df = pd.DataFrame(normality_results)  # 转为DataFrame
print("# 正态性检验结果：")
print(normality_df.to_string(index=False))  # 输出所有列的正态性检验结果

# 7. 绘制每一列的分布直方图和密度图
print("# 正在绘制所有数值特征的分布直方图和密度图...")
n_cols = 4  # 每行显示4个图
n_rows = math.ceil(len(numeric_cols) / n_cols)  # 计算需要多少行
plt.figure(figsize=(5 * n_cols, 4 * n_rows))  # 设置画布大小
for i, col in enumerate(numeric_cols):
    plt.subplot(n_rows, n_cols, i + 1)  # 子图位置
    sns.histplot(df[col].dropna(), kde=True, bins=30, color='skyblue')  # 直方图+密度曲线
    plt.title(col, fontsize=10)  # 图标题
    plt.xlabel("")  # 不显示x轴标签
    plt.ylabel("")  # 不显示y轴标签
plt.tight_layout()  # 自动调整子图间距
plt.suptitle("各特征分布直方图与密度曲线", fontsize=16, y=1.02)  # 总标题
plt.show()  # 显示图形

import pandas as pd
from sklearn.preprocessing import StandardScaler, PowerTransformer
import joblib

# 读取数据
file_path = "/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_5min_interday_merged_factors_cleaned.xlsx"
df = pd.read_excel(file_path)

# 只处理数值型特征
exclude_cols = ['datetime', 'date']
numeric_cols = [col for col in df.columns if col not in exclude_cols]

# 1. 先对所有特征做Yeo-Johnson变换（适合有负数的特征，能让分布更接近正态）
pt = PowerTransformer(method='yeo-johnson', standardize=False)
df[numeric_cols] = pt.fit_transform(df[numeric_cols])

# 2. 再做标准化（Z-score），让每列均值为0，方差为1
scaler = StandardScaler()
df[numeric_cols] = scaler.fit_transform(df[numeric_cols])

# 3. 保存变换器，方便后续推理/新数据使用
joblib.dump(pt, "/Users/<USER>/MLQuant/Quant_ML_Struc/cache/yeojohnson_transformer.save")
joblib.dump(scaler, "/Users/<USER>/MLQuant/Quant_ML_Struc/cache/standard_scaler.save")

# 4. 保存新excel
output_path = "/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_5min_interday_merged_factors_normalized.xlsx"
df.to_excel(output_path, index=False)

# 5. 展示新excel的前几行
print(df.head())

# 10. 对归一化后的特征文件做描述性统计、极值/异常值统计、正态性检验和分布可视化

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import math

# 1. 读取归一化后的特征文件
norm_file_path = "/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_5min_interday_merged_factors_normalized.xlsx"
df_norm = pd.read_excel(norm_file_path)

# 2. 只保留数值型特征列
exclude_cols = ['datetime', 'date']
numeric_cols = [col for col in df_norm.columns if col not in exclude_cols]

# 3. 描述性统计
print("# 归一化后特征的描述性统计：")
desc_stats_norm = df_norm[numeric_cols].describe().T
print(desc_stats_norm)

# 4. 极值和异常值统计
# 使用3σ原则：均值±3*std之外为异常值
outlier_stats = []
for col in numeric_cols:
    col_data = df_norm[col].dropna()
    mean = col_data.mean()
    std = col_data.std()
    min_val = col_data.min()
    max_val = col_data.max()
    lower, upper = mean - 3*std, mean + 3*std
    outliers = col_data[(col_data < lower) | (col_data > upper)]
    outlier_stats.append({
        '特征': col,
        '最小值': min_val,
        '最大值': max_val,
        '均值': mean,
        '标准差': std,
        '3σ下界': lower,
        '3σ上界': upper,
        '异常值个数': len(outliers),
        '异常值占比': len(outliers) / len(col_data) if len(col_data) > 0 else 0
    })
outlier_df = pd.DataFrame(outlier_stats)
print("# 极值与异常值统计（3σ法）：")
print(outlier_df.to_string(index=False))

# 5. 正态性检验（Shapiro-Wilk和D'Agostino K²）
def normality_tests(data, col):
    clean_data = data[col].dropna()
    # Shapiro-Wilk检验（样本量≤5000时使用）
    if len(clean_data) <= 5000:
        stat_sw, p_sw = stats.shapiro(clean_data)
        sw_result = "正态" if p_sw > 0.05 else "非正态"
    else:
        stat_sw, p_sw, sw_result = None, None, "样本量>5000，跳过Shapiro-Wilk"
    # D'Agostino K²检验
    stat_da, p_da = stats.normaltest(clean_data)
    da_result = "正态" if p_da > 0.05 else "非正态"
    # 返回结果
    return {
        '特征': col,
        'Shapiro-Wilk': f"p={p_sw:.4f} ({sw_result})" if p_sw is not None else sw_result,
        "D'Agostino K²": f"p={p_da:.4f} ({da_result})",
        '偏度': f"{stats.skew(clean_data):.4f}",
        '峰度': f"{stats.kurtosis(clean_data):.4f}"
    }

normality_results_norm = [normality_tests(df_norm, col) for col in numeric_cols]
normality_df_norm = pd.DataFrame(normality_results_norm)
print("# 归一化后特征的正态性检验：")
print(normality_df_norm.to_string(index=False))

# 6. 绘制每一列的分布直方图和密度图
print("# 正在绘制归一化后所有数值特征的分布直方图和密度图...")
n_cols = 4
n_rows = math.ceil(len(numeric_cols) / n_cols)
plt.figure(figsize=(5 * n_cols, 4 * n_rows))
for i, col in enumerate(numeric_cols):
    plt.subplot(n_rows, n_cols, i + 1)
    sns.histplot(df_norm[col].dropna(), kde=True, bins=30, color='skyblue')
    plt.title(col, fontsize=10)
    plt.xlabel("")
    plt.ylabel("")
plt.tight_layout()
plt.suptitle("归一化后各特征分布直方图与密度曲线", fontsize=16, y=1.02)
plt.show()

# 11. 对归一化后的特征文件做winsorize处理，进一步修饰极端值

import pandas as pd
import numpy as np
from scipy.stats import mstats

# 读取归一化后的特征文件
norm_file_path = "/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_5min_interday_merged_factors_normalized.xlsx"
df_norm = pd.read_excel(norm_file_path)

# 只处理数值型特征
exclude_cols = ['datetime', 'date']
numeric_cols = [col for col in df_norm.columns if col not in exclude_cols]

# winsorize参数：上下各1%（可根据实际情况调整为0.5%~2%）
winsor_limits = (0.01, 0.01)

# 对每个数值特征做winsorize
df_winsor = df_norm.copy()
for col in numeric_cols:
    # winsorize返回的是masked array，需要转为普通ndarray
    winsorized = mstats.winsorize(df_winsor[col], limits=winsor_limits)
    df_winsor[col] = np.asarray(winsorized)

# 保存为新Excel
output_path = "/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_5min_interday_merged_factors_winsorized.xlsx"
df_winsor.to_excel(output_path, index=False)
print(f"已完成winsorize处理，保存到 {output_path}")

param_grid_mlp = {
    'mlp__hidden_layer_sizes': [(100,), (100, 50)],  # 更深的网络
    'mlp__activation': ['relu', 'tanh'],
    'mlp__alpha': [0.001, 0.01, 0.1],  # 更强的正则化
    'mlp__learning_rate_init': [0.0001, 0.001],  # 更小的学习率
    'mlp__max_iter': [1000]  # 更多迭代
}

class MLEnsembleStrategy(bt.Strategy):
    params = (
        ('models', None),  # 模型字典
        ('weights', None),  # 权重数组
        ('lookback', 5),  # 回看窗口
    )

    def __init__(self, models, weights):
        self.models = models  # 存储模型
        self.weights = weights  # 存储权重
        self.data_pred = None  # 初始化预测值
        self.order = None  # 初始化订单

# 1. 确保参数已定义
best_models = {
    'lr': best_pipeline_lr,
    'rf': best_pipeline_rf,
    'xgb': best_pipeline_xgb,
    'mlp': best_pipeline_mlp
}
optimal_weights = [0.3, 0.3, 0.2, 0.2]  # 示例权重

# 2. 调用回测
ensemble_result, ensemble_cerebro = run_backtest(
    ticker=ticker,
    df=backtest_data,
    start_date=start_date,
    end_date=end_date,
    strategy=MLEnsembleStrategy,
    strategy_params={'models': best_models, 'weights': optimal_weights},
    initial_cash=100000,
    print_log=True,
    timeframe=bt.TimeFrame.Minutes,
    compression=5
)

# 对 future_ret_1d 进行 winsorize 处理
from scipy.stats import mstats
df['future_ret_1d'] = mstats.winsorize(df['future_ret_1d'], limits=[0.05, 0.05])

df['rolling_ret_5'] = df['future_ret_1d'].rolling(5).mean()

print("Models:", best_models.keys() if best_models else "未定义")
print("Weights:", optimal_weights if optimal_weights is not None else "未计算")