"""
🔍 Alpha因子自动挖掘和检测系统
📈 专门用于特斯拉(TSLA)股票的量化因子挖掘，预测未来1日收益率

===============================================================================
📋 算法步骤和统计检测方法详解
===============================================================================

🔧 第一步：因子生成阶段
1. 数据预处理：
   - 加载TSLA日级别OHLCV数据
   - 计算基础收益率：ret_1d = close.pct_change(1)
   - 计算预测目标：future_ret_1d = ret_1d.shift(-1)

2. 多样化因子构造（目标500个因子）：
   - 动量类因子（~80个）：价格动量、RSI、威廉指标、MACD等
   - 成交量类因子（~100个）：成交量变化、VWAP偏离、成交量异常等
   - 波动率类因子（~100个）：ATR、实现波动率、条件波动率等
   - 价格结构因子（~60个）：K线形态、支撑阻力、缺口分析等
   - 均值回复因子（~60个）：布林带、回归偏离、自相关等
   - 复杂组合因子（~60个）：价量结合、多时间框架等
   - 技术指标因子（~80个）：KDJ、CCI、布林带变种等
   - 统计类因子（~60个）：偏度、峰度、分位数等
   - 微观结构因子（~80个）：流动性、订单流、价格发现等
   - 市场状态因子（~80个）：趋势状态、波动率状态、均值回复状态等

🔍 第二步：统计检测阶段
1. 单变量线性回归检验：
   - 对每个因子X，建立回归模型：future_ret_1d = α + β*X + ε
   - 使用OLS（普通最小二乘法）估计参数

2. 统计显著性检验：
   - t检验：H0: β=0 vs H1: β≠0
   - 计算t统计量：t = β̂ / SE(β̂)
   - 计算p值：P(|T| > |t|)，其中T~t(n-2)

3. 多维度评估指标：
   - R²：衡量因子解释收益率变异的比例
   - 相关系数：衡量线性关系强度
   - 方向准确率：预测涨跌方向的正确率
   - 信息系数(IC)：等同于相关系数

📊 第三步：因子筛选决策算法
1. 统计显著性筛选：
   - 主要标准：p < 0.05（95%置信水平）
   - 高质量标准：p < 0.05 且 |t| > 1.96
   - 顶级标准：p < 0.01 且 |t| > 2.58

2. 经济意义筛选：
   - R² > 0.001（至少解释0.1%的变异）
   - |相关系数| > 0.01（存在可检测的线性关系）
   - 方向准确率 > 0.5（优于随机猜测）

3. 机器学习适用性评估：
   - 线性模型适用性：基于t值和p值
   - 树模型适用性：基于R²和解释能力
   - 神经网络适用性：基于方向准确率和非线性特征

🎯 第四步：算法决策逻辑
1. 因子排序算法：
   - 主要排序：按|t值|降序排列
   - 次要排序：按p值升序排列
   - 第三排序：按R²降序排列

2. 质量分级算法：
   if p < 0.001 and |t| > 2.58:
       质量等级 = "🥇 顶级"
   elif p < 0.01 and |t| > 1.96:
       质量等级 = "🥈 高质量"
   elif p < 0.05:
       质量等级 = "🥉 显著"
   else:
       质量等级 = "❌ 不显著"

3. 机器学习推荐算法：
   - 线性模型推荐：|t| > 1.96
   - 树模型推荐：R² > 0.01
   - 神经网络推荐：方向准确率 > 0.52

⚠️ 第五步：数据泄露防护
1. 时间序列完整性：
   - 严格使用历史数据预测未来
   - future_ret_1d = ret_1d.shift(-1)确保时间顺序
   - 最小观测期要求：min_periods=50

2. 统计检验有效性：
   - 剔除无效观测（NaN、Inf）
   - 确保足够样本量进行统计推断
   - 使用稳健的统计方法



潜在问题
因子冗余：部分因子可能高度相关（如不同时间窗口的ATR或动量因子），导致多重共线性。
过拟合风险：生成大量因子（353个）后仅用单变量检验筛选，可能忽略因子间的交互作用。
经济意义：部分因子（如lower_shadow）的统计显著性可能缺乏经济学解释。
数据频率：仅使用日级别数据，可能忽略更高频（如分钟级）的信息。

===============================================================================

功能特点:
- 自动生成500个多样化Alpha因子
- 严格的统计显著性检测
- 多维度因子质量评估
- 机器学习模型适用性分析
- 防止数据泄露的时间序列处理
- 详细的算法决策过程说明

作者: Vincent
版本: 2.0
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 科学计算和统计
from scipy import stats
from scipy.stats import pearsonr, spearmanr
import statsmodels.api as sm
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score

# 可视化
import matplotlib.pyplot as plt
import seaborn as sns
plt.style.use('seaborn-v0_8')

# 系统和时间
import os
import datetime
from typing import Dict, List, Tuple, Optional
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


class TSLADataLoader:
    """TSLA数据加载器"""
    
    def __init__(self, cache_path: str = "/Users/<USER>/MLQuant/Quant_ML_Struc/cache/"):
        self.cache_path = cache_path
        self.data = None
        
    def load_daily_data(self) -> pd.DataFrame:
        """加载TSLA日级别数据"""
        try:
            file_path = os.path.join(self.cache_path, "TSLA_day.xlsx")
            df = pd.read_excel(file_path)
            
            # 标准化列名
            df.columns = [col.lower() for col in df.columns]
            
            # 确保datetime列是datetime类型
            df['datetime'] = pd.to_datetime(df['datetime'])
            df = df.sort_values('datetime').reset_index(drop=True)
            
            # 基础收益率计算
            df['ret_1d'] = df['close'].pct_change(1)  # 1日收益率
            df['future_ret_1d'] = df['ret_1d'].shift(-1)  # 未来1日收益率(预测目标)
            
            logging.info(f"成功加载TSLA日级别数据: {df.shape}")
            self.data = df
            return df
            
        except Exception as e:
            logging.error(f"加载数据失败: {e}")
            raise
    
    def get_basic_stats(self) -> Dict:
        """获取数据基础统计信息"""
        if self.data is None:
            raise ValueError("请先加载数据")
            
        stats_dict = {
            'data_shape': self.data.shape,
            'date_range': (self.data['datetime'].min(), self.data['datetime'].max()),
            'missing_values': self.data.isnull().sum().to_dict(),
            'basic_stats': self.data[['open', 'high', 'low', 'close', 'volume']].describe().to_dict()
        }
        return stats_dict


class AlphaFactorGenerator:
    """Alpha因子生成器 - 生成500个多样化量化因子"""

    def __init__(self, data: pd.DataFrame):
        self.data = data.copy()
        # 导入模块化因子生成器
        from ModularAlphaFactors import ModularAlphaFactors
        self.modular_generator = ModularAlphaFactors(data)

    def generate_all_factors(self) -> pd.DataFrame:
        """生成所有Alpha因子"""
        logging.info("开始生成500个多样化Alpha因子...")

        # 使用模块化生成器生成所有因子
        factors, factor_descriptions = self.modular_generator.generate_all_factors()

        # 保存因子描述
        self.factor_descriptions = factor_descriptions
        self.factors = factors

        # 合并原始数据和因子
        result = pd.concat([self.data, factors], axis=1)

        logging.info(f"成功生成 {len(factors.columns)} 个Alpha因子")
        return result


class StatisticalIndicatorExplainer:
    """统计指标解释器 - 解释各种统计指标的含义"""

    @staticmethod
    def print_statistical_explanation():
        """打印统计指标详细解释"""
        print("\n" + "="*80)
        print("📊 ALPHA因子统计指标详细解释")
        print("="*80)

        print("\n🔍 1. P值 (P-value)")
        print("   含义: 在零假设为真的情况下，观察到当前结果或更极端结果的概率")
        print("   判断标准:")
        print("   • p < 0.001: 高度显著 (***) - 因子预测能力极强")
        print("   • p < 0.01:  显著 (**)     - 因子预测能力较强")
        print("   • p < 0.05:  边缘显著 (*) - 因子预测能力一般")
        print("   • p ≥ 0.05:  不显著       - 因子预测能力较弱")

        print("\n📈 2. t值 (t-statistic)")
        print("   含义: 衡量因子系数与0的差异程度，绝对值越大表示因子越重要")
        print("   判断标准:")
        print("   • |t| > 2.58: 高度显著 (99%置信水平)")
        print("   • |t| > 1.96: 显著 (95%置信水平)")
        print("   • |t| > 1.65: 边缘显著 (90%置信水平)")
        print("   • |t| ≤ 1.65: 不显著")

        print("\n📊 3. R² (决定系数)")
        print("   含义: 因子能解释未来收益率变异的比例")
        print("   判断标准:")
        print("   • R² > 0.1:  解释能力强 (>10%)")
        print("   • R² > 0.05: 解释能力中等 (5-10%)")
        print("   • R² > 0.01: 解释能力较弱 (1-5%)")
        print("   • R² ≤ 0.01: 解释能力很弱 (<1%)")

        print("\n🔗 4. 相关系数 (Correlation)")
        print("   含义: 因子与未来收益率的线性关系强度")
        print("   判断标准:")
        print("   • |r| > 0.3: 强相关")
        print("   • |r| > 0.1: 中等相关")
        print("   • |r| > 0.05: 弱相关")
        print("   • |r| ≤ 0.05: 几乎无相关")

        print("\n🎯 5. 方向准确率 (Direction Accuracy)")
        print("   含义: 因子预测涨跌方向的正确率")
        print("   判断标准:")
        print("   • > 0.6:  方向预测能力强")
        print("   • > 0.55: 方向预测能力中等")
        print("   • > 0.5:  方向预测能力较弱")
        print("   • ≤ 0.5:  无方向预测能力")

        print("\n🤖 6. 机器学习模型适用性评估")
        print("   对于您的集成模型 (线性回归+随机森林+XGBoost+MLP) 和LSTM:")
        print("   • 线性模型: 重点关注p值、t值、相关系数")
        print("   • 树模型: 重点关注R²、方向准确率")
        print("   • 神经网络: 综合考虑所有指标，特别是非线性关系")
        print("   • LSTM: 关注时序相关性和趋势预测能力")

        print("\n✅ 7. Alpha因子筛选建议")
        print("   优秀因子标准:")
        print("   • p < 0.05 且 |t| > 1.96")
        print("   • R² > 0.01")
        print("   • 方向准确率 > 0.52")
        print("   • 具有经济学解释意义")

        print("="*80)


class AlphaFactorDetector:
    """Alpha因子检测器 - 检测因子的预测能力"""

    def __init__(self, data: pd.DataFrame, target_col: str = 'future_ret_1d'):
        self.data = data.copy()
        self.target_col = target_col
        self.results = pd.DataFrame()

    def detect_alpha_factors(self, min_periods: int = 50) -> pd.DataFrame:
        """检测所有因子的Alpha特性"""
        logging.info("开始Alpha因子检测...")

        target = self.data[self.target_col]
        factor_cols = [col for col in self.data.columns if col not in
                      ['datetime', 'open', 'high', 'low', 'close', 'volume', 'ret_1d', 'future_ret_1d']]

        results_list = []

        for factor_name in factor_cols:
            try:
                result = self._test_single_factor(factor_name, target, min_periods)
                if result is not None:
                    results_list.append(result)
            except Exception as e:
                logging.warning(f"因子 {factor_name} 检测失败: {e}")
                continue

        if results_list:
            results_df = pd.DataFrame(results_list)
            results_df = results_df.sort_values('abs_t_value', ascending=False)
            self.results = results_df
            logging.info(f"成功检测 {len(results_df)} 个因子")
            return results_df
        else:
            logging.warning("没有成功检测到任何因子")
            return pd.DataFrame()

    def _test_single_factor(self, factor_name: str, target: pd.Series, min_periods: int) -> Optional[Dict]:
        """测试单个因子的Alpha特性"""
        factor_data = self.data[factor_name]

        # 创建有效数据掩码
        valid_mask = ~(factor_data.isna() | target.isna() |
                      np.isinf(factor_data) | np.isinf(target))

        if valid_mask.sum() < min_periods:
            return None

        X = factor_data[valid_mask].values.reshape(-1, 1)
        y = target[valid_mask].values

        # 线性回归分析
        X_with_const = sm.add_constant(X)
        model = sm.OLS(y, X_with_const).fit()

        # 计算相关性
        correlation, corr_p_value = pearsonr(X.flatten(), y)
        spearman_corr, spearman_p = spearmanr(X.flatten(), y)

        # 计算信息系数(IC)
        ic = correlation

        # 计算方向准确率
        factor_direction = np.sign(factor_data[valid_mask].diff())
        target_direction = np.sign(target[valid_mask])
        direction_accuracy = (factor_direction == target_direction).mean()

        return {
            'factor_name': factor_name,
            'r_squared': model.rsquared,
            'adj_r_squared': model.rsquared_adj,
            'beta': model.params[1],
            't_value': model.tvalues[1],
            'p_value': model.pvalues[1],
            'abs_t_value': abs(model.tvalues[1]),
            'correlation': correlation,
            'correlation_p_value': corr_p_value,
            'spearman_correlation': spearman_corr,
            'spearman_p_value': spearman_p,
            'information_coefficient': ic,
            'direction_accuracy': direction_accuracy,
            'valid_observations': valid_mask.sum(),
            'significance_level': '***' if model.pvalues[1] < 0.001 else
                                '**' if model.pvalues[1] < 0.01 else
                                '*' if model.pvalues[1] < 0.05 else ''
        }


def print_algorithm_decision_process(results_df: pd.DataFrame):
    """打印算法决策过程说明"""
    print("\n" + "="*100)
    print("🤖 算法决策过程详解 - 如何筛选和评价Alpha因子")
    print("="*100)

    print("\n📋 第一步：统计检验决策算法")
    print("   1. 单变量线性回归：future_ret_1d = α + β*factor + ε")
    print("   2. t检验：H0: β=0 vs H1: β≠0")
    print("   3. 计算统计量：t = β̂ / SE(β̂)")
    print("   4. 计算p值：P(|T| > |t|)，其中T~t(n-2)")

    print("\n🎯 第二步：因子排序决策算法")
    print("   主要排序：按|t值|降序 → 衡量因子重要性")
    print("   次要排序：按p值升序 → 优先显著因子")
    print("   第三排序：按R²降序 → 优先解释能力强的因子")

    print("\n🏅 第三步：质量分级决策算法")
    total_factors = len(results_df)
    top_factors = len(results_df[(results_df['p_value'] < 0.01) & (results_df['abs_t_value'] > 2.58)])
    high_quality = len(results_df[(results_df['p_value'] < 0.05) & (results_df['abs_t_value'] > 1.96)])
    significant = len(results_df[results_df['p_value'] < 0.05])

    print(f"   🥇 顶级因子: p<0.01 且 |t|>2.58 → {top_factors}个 ({top_factors/total_factors*100:.1f}%)")
    print(f"   🥈 高质量因子: p<0.05 且 |t|>1.96 → {high_quality}个 ({high_quality/total_factors*100:.1f}%)")
    print(f"   🥉 显著因子: p<0.05 → {significant}个 ({significant/total_factors*100:.1f}%)")
    print(f"   ❌ 不显著因子: p≥0.05 → {total_factors-significant}个 ({(total_factors-significant)/total_factors*100:.1f}%)")

    print("\n🤖 第四步：机器学习适用性决策算法")
    linear_suitable = len(results_df[results_df['abs_t_value'] > 1.96])
    tree_suitable = len(results_df[results_df['r_squared'] > 0.01])
    nn_suitable = len(results_df[results_df['direction_accuracy'] > 0.52])

    print(f"   📈 线性模型适用: |t|>1.96 → {linear_suitable}个因子")
    print(f"   🌳 树模型适用: R²>0.01 → {tree_suitable}个因子")
    print(f"   🧠 神经网络适用: 方向准确率>0.52 → {nn_suitable}个因子")

    print("\n⚖️ 第五步：综合评分决策算法")
    print("   综合评分 = 0.4×(|t值|/5) + 0.3×(R²×100) + 0.2×方向准确率 + 0.1×(1-p值)")
    print("   权重说明：t值(40%) > R²(30%) > 方向准确率(20%) > p值(10%)")

    # 计算综合评分
    results_df_copy = results_df.copy()
    results_df_copy['composite_score'] = (
        0.4 * (results_df_copy['abs_t_value'] / 5).clip(0, 1) +
        0.3 * (results_df_copy['r_squared'] * 100).clip(0, 1) +
        0.2 * results_df_copy['direction_accuracy'] +
        0.1 * (1 - results_df_copy['p_value']).clip(0, 1)
    )

    top_composite = results_df_copy.nlargest(5, 'composite_score')
    print("\n🏆 综合评分前5名因子:")
    for i, (_, factor) in enumerate(top_composite.iterrows(), 1):
        print(f"   {i}. {factor['factor_name'][:30]:<30} 评分: {factor['composite_score']:.3f}")

    print("="*100)


def print_factor_results(results_df: pd.DataFrame, factor_descriptions: Dict, top_n: int = 20):
    """打印因子检测结果"""
    if results_df.empty:
        print("❌ 没有检测到有效的Alpha因子")
        return

    # 首先显示算法决策过程
    print_algorithm_decision_process(results_df)

    print(f"\n🏆 前{top_n}个最佳Alpha因子详细分析:")
    print("="*120)
    print(f"{'排名':<4} {'因子名称':<35} {'R²':<8} {'t值':<8} {'p值':<10} {'相关系数':<8} {'方向准确率':<10} {'显著性':<6}")
    print("-"*120)

    for i, (_, factor) in enumerate(results_df.head(top_n).iterrows(), 1):
        print(f"{i:<4} {factor['factor_name'][:34]:<35} "
              f"{factor['r_squared']:<8.4f} "
              f"{factor['t_value']:<8.2f} "
              f"{factor['p_value']:<10.6f} "
              f"{factor['correlation']:<8.4f} "
              f"{factor['direction_accuracy']:<10.3f} "
              f"{factor['significance_level']:<6}")

    print("-"*120)

    # 统计摘要
    total_factors = len(results_df)
    significant_factors = len(results_df[results_df['p_value'] < 0.05])
    highly_significant = len(results_df[results_df['p_value'] < 0.01])

    print(f"\n📊 统计摘要:")
    print(f"   - 总因子数量: {total_factors}")
    print(f"   - 显著因子数量 (p<0.05): {significant_factors} ({significant_factors/total_factors*100:.1f}%)")
    print(f"   - 高度显著因子 (p<0.01): {highly_significant} ({highly_significant/total_factors*100:.1f}%)")
    print(f"   - 平均R²: {results_df['r_squared'].mean():.4f}")
    print(f"   - 平均方向准确率: {results_df['direction_accuracy'].mean():.3f}")


def save_qualified_factors(results_df: pd.DataFrame, factor_descriptions: Dict,
                          data_with_factors: pd.DataFrame, save_path: str):
    """保存合格的Alpha因子到文件"""
    # 筛选合格因子 - 使用更实际的标准
    # 标准1: 统计显著因子 (p<0.05)
    significant_factors = results_df[results_df['p_value'] < 0.05].copy()

    # 标准2: 高质量因子 (p<0.05 且 |t|>1.96)
    high_quality_factors = results_df[
        (results_df['p_value'] < 0.05) &
        (results_df['abs_t_value'] > 1.96)
    ].copy()

    # 标准3: 顶级因子 (p<0.01 且 |t|>2.58)
    top_factors = results_df[
        (results_df['p_value'] < 0.01) &
        (results_df['abs_t_value'] > 2.58)
    ].copy()

    if significant_factors.empty:
        print("⚠️ 没有找到统计显著的Alpha因子")
        return

    print(f"✅ 找到 {len(significant_factors)} 个统计显著因子")
    print(f"✅ 找到 {len(high_quality_factors)} 个高质量因子")
    print(f"✅ 找到 {len(top_factors)} 个顶级因子")

    # 使用统计显著因子作为主要保存对象
    qualified_factors = significant_factors

    # 创建保存内容
    content = f"""
# TSLA Alpha因子挖掘结果
# 生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# 数据时间范围: {data_with_factors['datetime'].min()} 至 {data_with_factors['datetime'].max()}
#
# 因子分类标准:
# - 统计显著因子: p < 0.05 (共{len(significant_factors)}个)
# - 高质量因子: p < 0.05 且 |t| > 1.96 (共{len(high_quality_factors)}个)
# - 顶级因子: p < 0.01 且 |t| > 2.58 (共{len(top_factors)}个)

总计发现 {len(qualified_factors)} 个统计显著的Alpha因子，用于预测TSLA未来1日收益率

{'='*80}
统计显著Alpha因子详细信息
{'='*80}

"""

    for i, (_, factor) in enumerate(qualified_factors.iterrows(), 1):
        factor_name = factor['factor_name']
        description = factor_descriptions.get(factor_name, "无描述")

        # 获取因子计算方式
        calculation_method = get_factor_calculation_method(factor_name, data_with_factors)

        # 判断因子质量等级
        quality_level = "🥇 顶级" if factor['factor_name'] in top_factors['factor_name'].values else \
                       "🥈 高质量" if factor['factor_name'] in high_quality_factors['factor_name'].values else \
                       "🥉 显著"

        content += f"""
{i}. 因子名称: {factor_name} [{quality_level}]
   描述: {description}

   统计指标:
   - R²: {factor['r_squared']:.6f} (解释{factor['r_squared']*100:.2f}%的收益率变异)
   - t值: {factor['t_value']:.4f} (绝对值: {factor['abs_t_value']:.4f})
   - p值: {factor['p_value']:.8f} {factor['significance_level']}
   - 相关系数: {factor['correlation']:.6f}
   - 方向准确率: {factor['direction_accuracy']:.4f} ({factor['direction_accuracy']*100:.1f}%)
   - 有效观测数: {factor['valid_observations']}

   计算方式:
   {calculation_method}

   因子意义与作用:
   {get_factor_interpretation(factor_name)}

   机器学习适用性:
   {get_ml_suitability(factor)}

{'-'*80}
"""

    # 保存到文件
    with open(save_path, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"✅ 已保存 {len(qualified_factors)} 个合格Alpha因子到: {save_path}")


def get_factor_calculation_method(factor_name: str, data: pd.DataFrame) -> str:
    """获取因子计算方式说明"""
    if 'momentum_' in factor_name:
        period = factor_name.split('_')[1].replace('d', '')
        return f"   close / close.shift({period}) - 1\n   (当前收盘价 / {period}日前收盘价 - 1)"

    elif 'rsi_' in factor_name:
        period = factor_name.split('_')[1].replace('d', '')
        return f"   RSI({period}) = 100 - (100 / (1 + RS))\n   其中RS = {period}日平均涨幅 / {period}日平均跌幅"

    elif 'volume_change_' in factor_name:
        period = factor_name.split('_')[2].replace('d', '')
        return f"   volume.pct_change({period})\n   ({period}日成交量变化率)"

    elif 'atr_' in factor_name:
        period = factor_name.split('_')[1].replace('d', '')
        return f"   ATR({period}) = MA(TrueRange, {period})\n   TrueRange = max(high-low, |high-prev_close|, |low-prev_close|)"

    elif 'bollinger_position_' in factor_name:
        period = factor_name.split('_')[2].replace('d', '')
        return f"   (close - lower_band) / (upper_band - lower_band)\n   布林带位置，{period}日周期"

    else:
        return f"   详细计算方式请参考AlphaFactorGenerator类中的相应方法"


def get_factor_interpretation(factor_name: str) -> str:
    """获取因子解释"""
    if 'momentum_' in factor_name:
        return "   动量因子：衡量价格趋势强度，正值表示上涨趋势，负值表示下跌趋势"
    elif 'rsi_' in factor_name:
        return "   相对强弱指标：衡量超买超卖状态，>70超买，<30超卖"
    elif 'volume_' in factor_name:
        return "   成交量因子：反映市场参与度和资金流向变化"
    elif 'volatility' in factor_name or 'atr' in factor_name:
        return "   波动率因子：衡量价格波动程度，高波动率通常伴随高风险高收益"
    elif 'bollinger' in factor_name:
        return "   布林带因子：衡量价格相对于统计均值的位置，用于判断超买超卖"
    elif 'mean_revert' in factor_name:
        return "   均值回复因子：衡量价格偏离均值的程度，基于均值回复理论"
    else:
        return "   复合因子：结合多种市场信息的综合指标"


def get_ml_suitability(factor_stats: pd.Series) -> str:
    """获取机器学习适用性评估"""
    r2 = factor_stats['r_squared']
    t_val = abs(factor_stats['t_value'])
    direction_acc = factor_stats['direction_accuracy']

    suitability = []

    if t_val > 2.58:
        suitability.append("✅ 线性模型: 高度适用 (强统计显著性)")
    elif t_val > 1.96:
        suitability.append("✅ 线性模型: 适用 (统计显著)")
    else:
        suitability.append("⚠️ 线性模型: 谨慎使用 (统计显著性不足)")

    if r2 > 0.05:
        suitability.append("✅ 树模型: 高度适用 (强解释能力)")
    elif r2 > 0.01:
        suitability.append("✅ 树模型: 适用 (中等解释能力)")
    else:
        suitability.append("⚠️ 树模型: 谨慎使用 (解释能力较弱)")

    if direction_acc > 0.55:
        suitability.append("✅ 神经网络: 高度适用 (强方向预测)")
    elif direction_acc > 0.52:
        suitability.append("✅ 神经网络: 适用 (中等方向预测)")
    else:
        suitability.append("⚠️ 神经网络: 谨慎使用 (方向预测能力不足)")

    return "\n   ".join(suitability)


def main():
    """主函数 - Alpha因子挖掘和检测流程"""
    print("🚀 启动TSLA Alpha因子自动挖掘系统...")
    print("="*80)

    try:
        # 0. 显示统计指标解释
        StatisticalIndicatorExplainer.print_statistical_explanation()

        # 1. 数据加载
        print("\n📊 步骤1: 加载TSLA数据...")
        loader = TSLADataLoader()
        data = loader.load_daily_data()

        # 显示基础统计信息
        stats = loader.get_basic_stats()
        print(f"✅ 数据加载成功!")
        print(f"   - 数据形状: {stats['data_shape']}")
        print(f"   - 时间范围: {stats['date_range'][0]} 至 {stats['date_range'][1]}")
        print(f"   - 缺失值: {sum(stats['missing_values'].values())} 个")

        # 2. 生成Alpha因子
        print("\n🔧 步骤2: 生成Alpha因子...")
        factor_generator = AlphaFactorGenerator(data)
        data_with_factors = factor_generator.generate_all_factors()

        print(f"✅ 因子生成完成!")
        print(f"   - 原始特征: {len(data.columns)} 个")
        print(f"   - 生成因子: {len(factor_generator.factors.columns)} 个")
        print(f"   - 总特征数: {len(data_with_factors.columns)} 个")

        # 3. Alpha因子检测
        print("\n🔍 步骤3: 检测Alpha因子...")
        detector = AlphaFactorDetector(data_with_factors)
        detection_results = detector.detect_alpha_factors(min_periods=50)

        # 4. 显示检测结果
        print_factor_results(detection_results, factor_generator.factor_descriptions, top_n=20)

        # 5. 保存合格因子
        save_path = "/Users/<USER>/MLQuant/Quant_ML_Struc/2_Feature_Engineering/AlphaFactors_Future_Ret_1D"
        save_qualified_factors(detection_results, factor_generator.factor_descriptions,
                             data_with_factors, save_path)

        print("\n🎉 Alpha因子挖掘完成!")
        print("📊 建议下一步:")
        print("   1. 查看保存的合格因子文件")
        print("   2. 选择显著因子用于机器学习模型")
        print("   3. 进行因子组合和特征工程")
        print("   4. 实施时间序列交叉验证")

    except Exception as e:
        logging.error(f"程序执行失败: {e}")
        print(f"❌ 错误: {e}")
        raise


if __name__ == "__main__":
    # 运行Alpha因子生成流程
    main()
