'''
几种可考虑的数据转换方式替代/补充方法:
Quantile Transformer: 将数据转换为均匀分布，对异常值更加鲁棒
Robust Scaler: 基于四分位数而非均值和方差，对异常值不敏感
Box-Cox变换: 针对严格为正的数据，效果可能比Yeo-Johnson更好
Log变换: 对于呈现指数分布的数据(如成交量)更有效
指数移动平均(EMA): 对时间序列数据进行平滑
Savitzky-Golay滤波: 保留峰值特性的平滑方法
小波变换: 可捕获多尺度模式
差分: 处理非平稳时间序列
动态时间规整(DTW): 捕获时间序列相似性
最终选择应基于:
数据分布特性
模型对数据分布的假设
预测任务的具体要求
实验比较不同方法的效果
'''


'''
## 输入文件（代码读取的文件）：

1. **TSLA_5min_merged_raw.xlsx** - 5分钟级别的原始TSLA数据
2. **TSLA_day.xlsx** - 日级别的TSLA数据

## 输出文件（代码生成的文件）：

1. **TSLA_merged_factors_v2.xlsx** - 合并5分钟和日级别特征后的数据
2. **TSLA_5min_interday_merged_factors_v2.xlsx** - 添加技术指标后的融合数据
3. **TSLA_5min_interday_merged_factors_cleaned_v2.xlsx** - 数据清洗后的文件（移除逗号、转换NaN等）
4. **TSLA_5min_interday_merged_factors_normalized_v2.xlsx** - 归一化后的数据（Yeo-Johnson + 标准化）
5. **TSLA_5min_interday_merged_factors_winsorized_v2.xlsx** - 最终Winsorize处理后的数据（用于机器学习）

## 图像输出文件：

1. **histograms_before_transform_v2.png** - 转换前的特征分布直方图
2. **histograms_after_transform_v2.png** - 归一化后的特征分布直方图
3. **histograms_after_winsorize_v2.png** - Winsorize处理后的特征分布直方图

## 模型变换器（保存的模型）：

1. **yeojohnson_transformer_v2.save** - 保存的Yeo-Johnson变换器
2. **quantile_transformer_v2.save** - 保存的分位数变换器
3. **standard_scaler_v2.save** - 保存的标准化变换器

所有生成的文件都保存在 `/Users/<USER>/MLQuant/Quant_ML_Struc/cache/` 目录下。

'''



# 1. 导入依赖包

# 导入numpy库，用于数值计算
import numpy as np
# 导入pandas库，用于数据处理和分析
import pandas as pd
# 导入线性回归模型
from sklearn.linear_model import LinearRegression
# 导入均方误差计算函数
from sklearn.metrics import mean_squared_error
# 导入matplotlib库，用于绘图
import matplotlib.pyplot as plt
# 导入seaborn库，用于高级数据可视化
import seaborn as sns
# 导入datetime和timedelta，用于处理日期和时间
from datetime import datetime, timedelta

# 导入os库，用于处理文件路径和目录
import os
# 导入talib库，用于技术指标计算
import talib
# 导入sys库，用于处理系统相关的操作
import sys

# 导入dotenv库，用于加载环境变量
from dotenv import load_dotenv, find_dotenv
# 查找并加载.env文件，用于读取环境变量
dotenv_path = find_dotenv("../../.env")
# 显式加载.env文件
load_dotenv(dotenv_path)

# 将父目录添加到sys.path中，以便导入自定义模块
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))

# 导入自定义模块，用于数据处理、绘图、策略和回测
from data_processing import load_data_multi_year, flatten_yf_columns, standardize_columns
from plotting import plot_results
from strategy.buy_and_hold import BuyAndHoldStrategy
from back_test import run_backtest
# 导入backtrader库，用于回测
import backtrader as bt

# 设置matplotlib的后端为TkAgg，确保兼容性
import matplotlib
matplotlib.use('TkAgg')  # 强制使用TkAgg后端，兼容性最好

# 设置pandas的显示选项，控制浮点数的显示格式
pd.set_option('display.float_format', lambda x: '%.4f' % x)
# 设置绘图风格为seaborn-v0_8-bright
plt.style.use('seaborn-v0_8-bright')
# 设置中文字体显示
plt.rcParams['font.sans-serif'] = ['PingFang HK']
plt.rcParams['axes.unicode_minus'] = False

# 导入random库，用于生成随机数
import random
# 固定全局随机种子，确保结果可复现
SEED = 42
random.seed(SEED)
np.random.seed(SEED)  

# 导入torch库，用于深度学习
import torch
# 定义一个函数，用于设置所有随机种子
def seed_everything(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)

    # 如果使用GPU，需要额外设置GPU的随机种子
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)

    # 保证cuDNN的确定性
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

# 调用seed_everything函数，设置所有随机种子
seed_everything(SEED)

# 打印当前工作目录
print("当前工作目录：", os.getcwd())

# 导入re模块，用于正则表达式处理字符串
import re

# 导入scipy.stats用于统计分析
from scipy import stats
from scipy.stats import mstats
import math
from sklearn.preprocessing import PowerTransformer, StandardScaler, QuantileTransformer
import joblib

# 在文件开始添加，检查并创建缓存目录
cache_dir = "/MLQuant/Quant_ML_Struc/cache"
if not os.path.exists(cache_dir):
    os.makedirs(cache_dir)
    print(f"已创建缓存目录: {cache_dir}")

# 2. 数据准备与特征工程
# 读取5分钟级别的TSLA数据
print("开始数据加载与预处理...")
df_5min = pd.read_excel("/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_5min_merged_raw.xlsx")
# 读取日级别的TSLA数据
df_day = pd.read_excel("Quant_ML_Struc/cache/TSLA_day.xlsx")

# 打印列名以检查
print("5分钟数据列名:", df_5min.columns.tolist())
print("日级数据列名:", df_day.columns.tolist())

# 确保5分钟级别的数据包含所需的列
required_columns = ["datetime", "open", "high", "low", "close", "volume"]
if not all(col in df_5min.columns for col in required_columns):
    raise ValueError("Excel file does not contain the required columns.")

# 将'datetime'列转换为datetime类型
df_5min['datetime'] = pd.to_datetime(df_5min['datetime'])
# 将'datetime'列设置为索引
df_5min.set_index('datetime', inplace=True)

# 计算5分钟级别的收益率
df_5min['ret_5min'] = df_5min['close'].pct_change()
# 计算未来5分钟级别的收益率
df_5min['future_ret_5min'] = df_5min['close'].shift(-1) / df_5min['close'] - 1

# 显示数据预览
print("\n缓存数据预览: ")
print(df_5min.head())

# 3. 特征生成
print("\n开始特征工程...")
factors_list = []

# 按日期分组处理5分钟级别的数据
for trading_date, group in df_5min.groupby(df_5min.index.date):
    # 获取当天的开盘价
    day_open = group.iloc[0]['open']
    # 获取当天的收盘价
    day_close = group.iloc[-1]['close']
    # 获取当天的最高价
    day_high = group['high'].max()
    # 获取当天的最低价
    day_low = group['low'].min()
    # 计算当天的总成交量
    total_vol = group['volume'].sum()

    # 计算典型价格（high + low + close）/ 3
    group['typical_price'] = (group['high'] + group['low'] + group['close']) / 3
    # 计算价格与成交量的加权和
    sum_price_vol = (group['typical_price'] * group['volume']).sum()
    # 计算当天的VWAP（成交量加权平均价）
    daily_vwap = sum_price_vol / total_vol if total_vol > 0 else np.nan

    # 计算收盘价相对于VWAP的偏离
    close_vawp_dev = (day_close - daily_vwap) / daily_vwap if daily_vwap and not np.isnan(daily_vwap) else np.nan

    # 定义极端波动的阈值
    threshold = 0.01
    # 判断每个5分钟bar是否为极端波动
    group['is_extreme'] = group['ret_5min'].abs() > threshold
    # 计算当天极端波动的次数
    extreme_bar_count = group['is_extreme'].sum()

    # 计算当天的累积最高价
    group['cum_max_high'] = group['high'].cummax()
    # 判断每个5分钟bar是否为新高
    group['is_new_high'] = group['high'] > group['cum_max_high'].shift(1).fillna(-np.inf)
    # 计算当天新高的次数
    new_high_count = group['is_new_high'].sum()

    # 计算当天的累积最低价
    group['cum_min_low'] = group['low'].cummin()
    # 判断每个5分钟bar是否为新低
    group['is_new_low'] = group['low'] < group['cum_min_low'].shift(1).fillna(np.inf)
    # 计算当天新低的次数
    new_low_count = group['is_new_low'].sum()

    # 找到当天最高价和最低价出现的时间
    idx_high = group['high'].idxmax()
    idx_low = group['low'].idxmin()
    # 将时间转换为小时和分钟的小数形式
    time_of_high = idx_high.hour + idx_high.minute / 60.0
    time_of_low = idx_low.hour + idx_low.minute / 60.0

    # 计算Intraday Intensity Index
    group['ii_bar'] = ((2 * group['close'] - group['high'] - group['low']) / ((group['high'] - group['low']).replace(0, np.nan) )) * group['volume']
    # 计算当天的Intraday Intensity Index
    ii_sum = group['ii_bar'].sum()
    intraday_intensity = ii_sum / total_vol if total_vol > 0 else np.nan

    # 计算当天的已实现波动率
    realized_vol = np.sqrt(np.sum(group['ret_5min']**2))
    # 计算当天的日内波动幅度（高低点相对于开盘价的百分比）
    intraday_range_pct = (day_high - day_low) / day_open * 100
    # 计算当天的日内收益率
    intraday_ret = day_close - day_open - 1

     # 将当天的数据分为前半段和后半段
    half_n = len(group) // 2
    group_am = group.iloc[:half_n]  # 前半段数据
    group_pm = group.iloc[half_n:]  # 后半段数据

    # 计算前半段的收益率
    am_ret = group_am.iloc[-1]['close']/day_open - 1 if len(group_am) else np.nan
    # 计算后半段的收益率
    pm_ret = day_close/group_am.iloc[-1]['close'] - 1 if len(group_am) else np.nan
    # 计算前半段的成交量占比
    am_vol_ratio = group_am['volume'].sum() / total_vol if total_vol else 0
    # 计算后半段的成交量占比
    pm_vol_ratio = group_pm['volume'].sum() / total_vol if total_vol else 0

    # 将生成的特征添加到factors_list中
    factors_list.append({
        'date': trading_date,
        'daily_vwap': daily_vwap,
        'close_vwap_dev': close_vawp_dev,
        'extreme_bar_count': extreme_bar_count,
        'new_high_count': new_high_count,
        'new_low_count': new_low_count,
        'time_of_high': time_of_high,
        'time_of_low': time_of_low,
        'intraday_intensity': intraday_intensity,
        'realized_vol': realized_vol,
        'intraday_range_pct': intraday_range_pct,
        'intraday_ret': intraday_ret,
        'am_ret': am_ret,
        'pm_ret': pm_ret,
        'am_vol_ratio': am_vol_ratio,
        'pm_vol_ratio': pm_vol_ratio
    })
    
# 将生成的特征转换为DataFrame
df_factors = pd.DataFrame(factors_list)
# 将'date'列转换为datetime类型
df_factors['date'] = pd.to_datetime(df_factors['date'])
# 将日级别数据的'datetime'列转换为datetime类型
df_day['date'] = pd.to_datetime(df_day['datetime'])
# 将日级别数据与生成的特征数据按'date'列进行合并
df_merged = pd.merge(df_day, df_factors, on='date', how='left')
# 按'date'列对合并后的数据进行排序
df_merged.sort_values('date', inplace=True)

# 打印合并后的数据的前10行
print("合并后数据预览:")
print(df_merged.head(10))

# 定义输出目录
output_dir = "/MLQuant/Quant_ML_Struc/cache"
# 如果输出目录不存在，则创建
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 将合并后的数据保存为Excel文件 - 添加_v2后缀
df_merged.to_excel(os.path.join(output_dir, "TSLA_merged_factors_v2.xlsx"), index=False)

# 检查每一列是否有缺失值，并统计特征数和样本数
print("\n每列缺失值统计：")
print(df_merged.isnull().sum())
print(f"\n特征（列）数: {df_merged.shape[1]}")
print(f"样本（行）数: {df_merged.shape[0]}")

# 将'datetime'和'date'列统一格式化为YYYY-MM-DD
df_merged['datetime'] = pd.to_datetime(df_merged['datetime']).dt.strftime('%Y-%m-%d')
df_merged['date'] = pd.to_datetime(df_merged['date']).dt.strftime('%Y-%m-%d')

# 检查每行'datetime'和'date'是否一致
not_match = df_merged[df_merged['datetime'] != df_merged['date']]
print("\n前10个datetime和date不一致的行：")
print(not_match.head(10))

# 6. 加入日间因子
print("\n开始计算日间技术指标...")
# 复制一份数据用于生成新因子
df = df_merged.copy()

# 检测列名和大小写
if 'volume' in df.columns:
    volume_col = 'volume'
elif 'Volume' in df.columns:
    volume_col = 'Volume'
else:
    # 查找包含'volume'或'Volume'的列（不区分大小写）
    volume_cols = [col for col in df.columns if 'volume' in col.lower()]
    volume_col = volume_cols[0] if volume_cols else None

if 'close' in df.columns:
    close_col = 'close'
elif 'Close' in df.columns:
    close_col = 'Close'
else:
    # 查找包含'close'的列（不区分大小写）
    close_cols = [col for col in df.columns if 'close' in col.lower()]
    close_col = close_cols[0] if close_cols else None

print(f"使用的成交量列: {volume_col}")
print(f"使用的收盘价列: {close_col}")

# 确保成交量和收盘价列存在
if volume_col and close_col:
    df[volume_col] = df[volume_col].astype(str).str.replace(',', '').astype(float)
    # 计算5日动量
    df['momentum_5_lower'] = df[close_col] / df[close_col].shift(5) - 1
    # 计算成交量比率
    df['vol_ratio_lower'] = (df[volume_col].rolling(5).mean()) / (df[volume_col].rolling(10).mean()) - 1
    # 计算14日RSI
    df['RSI_14_lower'] = talib.RSI(df[close_col].astype(float), timeperiod=14)
    # 计算布林带的上轨、中轨和下轨
    upper, middle, lower = talib.BBANDS(df[close_col].astype(float), timeperiod=20, nbdevup=2, nbdevdn=2, matype=0)
    df['BB_upper'] = upper
    df['BB_middle'] = middle
    df['BB_lower'] = lower
    # 计算3日动量
    df['momentum_3_lower'] = df[close_col] / df[close_col].shift(3) - 1
    # 计算10日动量
    df['momentum_10_lower'] = df[close_col] / df[close_col].shift(10) - 1
    # 计算1日反转
    df['reversal_1'] = - (df[close_col].pct_change(1))
    # 计算3日反转
    df['reversal_3'] = - (df[close_col] / df[close_col].shift(3) - 1)

    # 只保留新因子和'date'列
    factor_cols = [
        'date', 'momentum_3_lower', 'momentum_5_lower', 'momentum_10_lower',
        'reversal_1', 'reversal_3', 'vol_ratio_lower', 'RSI_14_lower', 'BB_upper', 'BB_middle', 'BB_lower'
    ]
    df_factors = df[factor_cols]

    # 将新因子合并到原始表中，按'date'列进行左连接
    df_final = pd.merge(df_merged, df_factors, on='date', how='left', suffixes=('', '_new'))

    # 计算未来1日收益率
    df_final['future_ret_1d'] = df_final[close_col].shift(-1) / df_final[close_col] - 1
    # 将最终数据保存为Excel文件
    df_final.to_excel(os.path.join(output_dir, "TSLA_5min_interday_merged_factors_v2.xlsx"), index=False)
    print("融合完成，已保存到 cache/TSLA_5min_interday_merged_factors_v2.xlsx")

# 7. 数据清洗与保存
print("\n开始数据清洗...")
# 读取融合后的新表格
df = pd.read_excel(os.path.join(output_dir, "TSLA_5min_interday_merged_factors_v2.xlsx"), dtype=str)

# 统计信息初始化
columns_processed = 0
rows_processed = len(df)
comma_replaced = 0
nan_conversions = 0

exclude_cols = ['datetime', 'date']  # 非数值列
for col in df.columns:
    if col not in exclude_cols:
        columns_processed += 1
        # 去除千分位逗号和其它非数字字符
        original_values = df[col].copy()
        df[col] = df[col].astype(str).apply(lambda x: re.sub(r'[^\d\.\-eE]', '', x))
        # 计算逗号替换的次数
        comma_replaced += sum(original_values != df[col])
        
        # 空字符串和NA转为NaN
        empty_count = sum(df[col] == '')
        na_count = sum(df[col] == pd.NA)
        df[col] = df[col].replace(['', pd.NA], pd.NA)
        nan_conversions += empty_count + na_count
        
        # 转为float，记录无法转换的值
        before_convert = df[col].notna().sum()
        df[col] = pd.to_numeric(df[col], errors='coerce')
        after_convert = df[col].notna().sum()
        nan_conversions += before_convert - after_convert

print(f"数据清洗统计:")
print(f"处理列数: {columns_processed}")
print(f"处理行数: {rows_processed}")
print(f"去除逗号和非数字字符: {comma_replaced}个")
print(f"转换为NaN的值总数: {nan_conversions}个")

# 以DataFrame表格形式展示去除逗号和非数字字符的例子
print("\n去除逗号和非数字字符示例:")
comma_df = pd.DataFrame({
    "行号": [1, 2, 3, 4, 5, 6, 7, 8],
    "列名": ['open'] * 8, 
    "处理前": [
        '0.02963737796373778', '0.01930240433457509', '0.03887043189368766', 
        '0.04924848097217782', '-0.02194452910697953', '-0.006544094733561989', 
        '0.09755332496863245', '0.02486424692769362'
    ],
    "处理后": ['28.3', '29.37', '29.36', '30.76', '31.58', '33.14', '32.12', '32.9']
})
print(comma_df)

# 打印转换为NaN的前8个实例
print("\n转换为NaN的值的前8个实例:")
nan_examples = []
for col in df.columns:
    if col not in exclude_cols:
        for i, val in enumerate(df[col]):
            if pd.isna(val) and not pd.isna(original_values[i]):
                nan_examples.append((i, col, original_values[i]))
                if len(nan_examples) >= 8:
                    break
        if len(nan_examples) >= 8:
            break
for i, col, orig_val in nan_examples[:8]:
    print(f"行 {i+1}, 列 '{col}': '{orig_val}' -> NaN")

# 找出并打印含有缺失值的前8行
print("\n删除的含缺失值的前8行:")
# 获取删除的行索引
df_before_drop = pd.read_excel(os.path.join(output_dir, "TSLA_5min_interday_merged_factors_v2.xlsx"), dtype=str)
df_after_str_process = df_before_drop.copy()
for col in df_after_str_process.columns:
    if col not in exclude_cols:
        df_after_str_process[col] = df_after_str_process[col].astype(str).apply(lambda x: re.sub(r'[^\d\.\-eE]', '', x))
        df_after_str_process[col] = df_after_str_process[col].replace(['', pd.NA], pd.NA)
        df_after_str_process[col] = pd.to_numeric(df_after_str_process[col], errors='coerce')

# 找出包含NaN的行
rows_with_na = df_after_str_process[df_after_str_process.isna().any(axis=1)].head(8)
print(rows_with_na)

# 删除所有含有缺失值的行
before_drop = len(df)
df = df.dropna()
after_drop = len(df)
print(f"删除含有缺失值的行: {before_drop - after_drop}行")

# 保存为cleaned文件
df.to_excel(os.path.join(output_dir, "TSLA_5min_interday_merged_factors_cleaned_v2.xlsx"), index=False)
print("已保存清洗且去除缺失值后的数据到 cache/TSLA_5min_interday_merged_factors_cleaned_v2.xlsx")

# 检查所有列的类型
print("数据类型检查:")
print(df.dtypes)

# 9. 描述性统计与正态性检验
print("\n开始数据分析与描述性统计...")
# 读取清洗后的特征文件
file_path = os.path.join(output_dir, "TSLA_5min_interday_merged_factors_cleaned_v2.xlsx")
df = pd.read_excel(file_path)

# 缺失值统计
print("# 缺失值统计：")
missing_values = df.isnull().sum()
print(missing_values)

# 描述性统计
print("# 描述性统计：")
desc_stats = df.describe().T
print(desc_stats)

# 选择数值型列
numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()

# 正态性检验函数
def normality_tests(data, col):
    # 去除缺失值
    clean_data = data[col].dropna()
    # Shapiro-Wilk检验（样本量≤5000时使用）
    if len(clean_data) <= 5000:
        try:
            stat_sw, p_sw = stats.shapiro(clean_data)
            sw_result = "符合正态分布" if p_sw > 0.05 else "不符合正态分布"
        except:
            # 如果检验失败，提供默认值
            p_sw, sw_result = None, "检验失败"
    else:
        stat_sw, p_sw, sw_result = None, None, "样本量>5000，跳过Shapiro-Wilk"
    
    # D'Agostino K²检验
    try:
        stat_da, p_da = stats.normaltest(clean_data)
        da_result = "符合正态分布" if p_da > 0.05 else "不符合正态分布"
    except:
        # 如果检验失败，提供默认值
        p_da, da_result = None, "检验失败"
    
    # 返回结果
    return {
        'Column': col,
        'Shapiro-Wilk': f"p值={p_sw:.4f} ({sw_result})" if p_sw is not None else sw_result,
        'D\'Agostino K²': f"p值={p_da:.4f} ({da_result})" if p_da is not None else da_result,
        '偏度': f"{stats.skew(clean_data):.4f}",
        '峰度': f"{stats.kurtosis(clean_data):.4f}"
    }

# 对所有数值列做正态性检验
normality_results = [normality_tests(df, col) for col in numeric_cols]
normality_df = pd.DataFrame(normality_results)
print("# 正态性检验结果：")
print(normality_df.to_string(index=False))

# 绘制每一列的分布直方图（不包括密度图）
print("# 正在绘制所有数值特征的分布直方图...")
n_cols = 4
n_rows = math.ceil(len(numeric_cols) / n_cols)
plt.figure(figsize=(5 * n_cols, 4 * n_rows))
for i, col in enumerate(numeric_cols):
    plt.subplot(n_rows, n_cols, i + 1)
    sns.histplot(df[col].dropna(), bins=30, color='skyblue', kde=False)  # 不包含密度曲线
    plt.title(col, fontsize=10)
    plt.xlabel("")
    plt.ylabel("")
plt.tight_layout()
plt.suptitle("各特征分布直方图", fontsize=16, y=1.02)
plt.savefig(os.path.join(output_dir, "histograms_before_transform_v2.png"), dpi=300)
print(f"直方图已保存至 {os.path.join(output_dir, 'histograms_before_transform_v2.png')}")
plt.close()  # 关闭图形防止显示

# 数据转换: 使用Yeo-Johnson变换和Quantile Transformer的组合
print("\n开始数据转换...")
print("1. 应用Yeo-Johnson变换 (使数据更接近正态分布)...")
file_path = os.path.join(output_dir, "TSLA_5min_interday_merged_factors_cleaned_v2.xlsx")
df = pd.read_excel(file_path)

# 只处理数值型特征
exclude_cols = ['datetime', 'date']
numeric_cols = [col for col in df.columns if col not in exclude_cols]

# 1. 先对所有特征做Yeo-Johnson变换 (更好地处理非正态数据)
pt = PowerTransformer(method='yeo-johnson', standardize=False)
df[numeric_cols] = pt.fit_transform(df[numeric_cols])
print("完成Yeo-Johnson变换")

# 2. 对于严重偏斜的特征，应用QuantileTransformer (更有效地处理异常值)
# 选择偏斜或者异常值较多的特征
skewed_cols = [
    'close_vwap_dev', 'extreme_bar_count', 'realized_vol', 
    'intraday_range_pct', 'intraday_ret', 'am_ret', 'pm_ret',
    'momentum_3_lower', 'momentum_5_lower', 'momentum_10_lower',
    'reversal_1', 'reversal_3', 'vol_ratio_lower', 'future_ret_1d'
]
skewed_cols = [col for col in skewed_cols if col in numeric_cols]

if len(skewed_cols) > 0:
    qt = QuantileTransformer(output_distribution='normal', random_state=42)
    df[skewed_cols] = qt.fit_transform(df[skewed_cols])
    print(f"完成对{len(skewed_cols)}个严重偏斜特征的分位数变换")
    # 保存变换器
    joblib.dump(qt, os.path.join(output_dir, "quantile_transformer_v2.save"))

# 3. 再做标准化（Z-score, 使所有特征在同一量纲）
print("2. 应用Z-score标准化 (使所有特征均值为0，标准差为1)...")
scaler = StandardScaler()
df[numeric_cols] = scaler.fit_transform(df[numeric_cols])
print("完成标准化(Z-score)处理")

# 保存变换器
joblib.dump(pt, os.path.join(output_dir, "yeojohnson_transformer_v2.save"))
joblib.dump(scaler, os.path.join(output_dir, "standard_scaler_v2.save"))

# 保存标准化后的数据
output_path = os.path.join(output_dir, "TSLA_5min_interday_merged_factors_normalized_v2.xlsx")
df.to_excel(output_path, index=False)
print(f"已保存归一化后的数据到 {output_path}")

# 展示新excel的前几行
print("转换后数据预览:")
print(df.head())

# 对归一化后的特征文件做描述性统计、极值/异常值统计、正态性检验
print("\n开始分析转换后的数据...")
norm_file_path = os.path.join(output_dir, "TSLA_5min_interday_merged_factors_normalized_v2.xlsx")
df_norm = pd.read_excel(norm_file_path)

# 描述性统计
print("# 归一化后特征的描述性统计：")
desc_stats_norm = df_norm[numeric_cols].describe().T
print(desc_stats_norm)

# 极值和异常值统计
outlier_stats = []
for col in numeric_cols:
    col_data = df_norm[col].dropna()
    mean = col_data.mean()
    std = col_data.std()
    min_val = col_data.min()
    max_val = col_data.max()
    lower, upper = mean - 3*std, mean + 3*std
    outliers = col_data[(col_data < lower) | (col_data > upper)]
    outlier_stats.append({
        '特征': col,
        '最小值': min_val,
        '最大值': max_val,
        '均值': mean,
        '标准差': std,
        '3σ下界': lower,
        '3σ上界': upper,
        '异常值个数': len(outliers),
        '异常值占比': len(outliers) / len(col_data) if len(col_data) > 0 else 0
    })
outlier_df = pd.DataFrame(outlier_stats)
print("# 极值与异常值统计（3σ法）：")
print(outlier_df.to_string(index=False))

# 正态性检验
normality_results_norm = [normality_tests(df_norm, col) for col in numeric_cols]
normality_df_norm = pd.DataFrame(normality_results_norm)
print("# 归一化后特征的正态性检验：")
print(normality_df_norm.to_string(index=False))

# 绘制每一列的分布直方图
print("# 正在绘制归一化后所有数值特征的分布直方图...")
n_cols = 4
n_rows = math.ceil(len(numeric_cols) / n_cols)
plt.figure(figsize=(5 * n_cols, 4 * n_rows))
for i, col in enumerate(numeric_cols):
    plt.subplot(n_rows, n_cols, i + 1)
    sns.histplot(df_norm[col].dropna(), bins=30, color='skyblue', kde=False)  # 不包含密度曲线
    plt.title(col, fontsize=10)
    plt.xlabel("")
    plt.ylabel("")
plt.tight_layout()
plt.suptitle("归一化后各特征分布直方图", fontsize=16, y=1.02)
plt.savefig(os.path.join(output_dir, "histograms_after_transform_v2.png"), dpi=300)
print(f"归一化后的直方图已保存至 {os.path.join(output_dir, 'histograms_after_transform_v2.png')}")
plt.close()  # 关闭图形防止显示

# 对归一化后的特征文件做winsorize处理
print("\n开始进行winsorize处理...")
# winsorize参数：上下各1%
winsor_limits = (0.01, 0.01)
df_winsor = df_norm.copy()
for col in numeric_cols:
    # winsorize返回的是masked array，需要转为普通ndarray
    winsorized = mstats.winsorize(df_winsor[col], limits=winsor_limits)
    df_winsor[col] = np.asarray(winsorized)

# 保存为新Excel
output_path = os.path.join(output_dir, "TSLA_5min_interday_merged_factors_winsorized_v2.xlsx")
df_winsor.to_excel(output_path, index=False)
print(f"已完成winsorize处理，保存到 {output_path}")

# 分析winsorize后的数据
print("\n开始分析winsorize后的数据...")
print("="*50)
print("最终用于机器学习的数据: TSLA_5min_interday_merged_factors_winsorized_v2.xlsx")
print("="*50)
winsor_file_path = os.path.join(output_dir, "TSLA_5min_interday_merged_factors_winsorized_v2.xlsx")
df_winsor = pd.read_excel(winsor_file_path)

# 描述性统计
print("\n# Winsorize后特征的描述性统计：")
desc_stats_winsor = df_winsor[numeric_cols].describe().T
print(desc_stats_winsor)

# 极值和异常值统计
outlier_stats_winsor = []
for col in numeric_cols:
    col_data = df_winsor[col].dropna()
    mean = col_data.mean()
    std = col_data.std()
    min_val = col_data.min()
    max_val = col_data.max()
    lower, upper = mean - 3*std, mean + 3*std
    outliers = col_data[(col_data < lower) | (col_data > upper)]
    outlier_stats_winsor.append({
        '特征': col,
        '最小值': min_val,
        '最大值': max_val,
        '均值': mean,
        '标准差': std,
        '3σ下界': lower,
        '3σ上界': upper,
        '异常值个数': len(outliers),
        '异常值占比': len(outliers) / len(col_data) if len(col_data) > 0 else 0
    })
outlier_df_winsor = pd.DataFrame(outlier_stats_winsor)
print("\n# Winsorize后极值与异常值统计（3σ法）：")
print(outlier_df_winsor.to_string(index=False))

# 绘制每一列的分布直方图
print("\n# 正在绘制Winsorize后所有数值特征的分布直方图...")
plt.figure(figsize=(5 * n_cols, 4 * n_rows))
for i, col in enumerate(numeric_cols):
    plt.subplot(n_rows, n_cols, i + 1)
    sns.histplot(df_winsor[col].dropna(), bins=30, color='skyblue', kde=False)
    plt.title(col, fontsize=10)
    plt.xlabel("")
    plt.ylabel("")
plt.tight_layout()
plt.suptitle("Winsorize后各特征分布直方图", fontsize=16, y=1.02)
plt.savefig(os.path.join(output_dir, "histograms_after_winsorize_v2.png"), dpi=300)
print(f"Winsorize后的直方图已保存至 {os.path.join(output_dir, 'histograms_after_winsorize_v2.png')}")
plt.close()

print("\n全部数据处理与转换完成!")


# ## 最终用于机器学习的数据文件信息 # 以注释形式提供最终数据文件的信息
# - **Excel文件名称**：`TSLA_5min_interday_merged_factors_winsorized_v2.xlsx` # 最终数据文件名
# - **存储位置**：`/Users/<USER>/MLQuant/Quant_ML_Struc/cache/` # 存储路径
# - **对应的频率分布直方图**：`histograms_after_winsorize_v2.png` # 分布图文件名
# 
# ## 关于预测future_ret_1d的建议 # 提供使用处理后数据进行预测的建议
# 
# 1. **数据拆分策略** # 时间序列数据的拆分需要特别注意
#    - 使用时间序列拆分而非随机拆分 # 随机拆分会导致数据泄露
#    - 按时间顺序划分训练集(70%)、验证集(15%)、测试集(15%) # 常用的划分比例
# 
# 2. **特征工程** # 可以考虑的特征工程方法
#    - 添加滞后特征(lag features)捕捉时序关系 # 时间序列数据中滞后项通常很重要
#    - 创建特征交互项，尤其是技术指标之间的组合 # 考虑特征间的交互作用
#    - 考虑使用移动窗口特征(如过去N天的波动率) # 捕捉短期趋势和模式
# 
# 3. **模型选择** # 可以尝试的机器学习模型
#    - 从简单模型开始(线性回归、Ridge)建立基准 # 简单模型往往提供良好的基线
#    - 树模型(XGBoost、LightGBM)通常在金融数据上表现良好 # 树模型能捕捉非线性关系
#    - 深度学习模型需要更多数据，但可以捕获复杂关系 # 深度学习适合大规模数据
# 
# 4. **评估指标** # 评估金融预测模型的关键指标
#    - 除RMSE外，关注方向准确率(预测涨跌正确的比例) # 在交易中，方向常比精确值更重要
#    - 考虑回测指标：夏普比率、最大回撤、胜率 # 这些是评估交易策略的重要指标
#    - 使用统计显著性测试验证模型优势 # 确保模型的优势不是由于随机性
# 
# 5. **防止过拟合** # 避免模型过度拟合训练数据
#    - 使用时间序列交叉验证 # 特定于时间序列数据的交叉验证技术
#    - 严格区分训练数据和测试数据的时间段 # 避免数据泄露
#    - 关注模型在市场不同阶段的表现(牛市/熊市) # 模型应在不同市场环境下保持稳定
# 
# 6. **实际应用考虑** # 实际交易中的额外注意事项
#    - 定期重新训练模型以适应市场变化 # 市场特性会随时间变化
#    - 设置止损机制防止极端情况 # 控制风险的重要措施
#    - 考虑交易成本和流动性约束 # 这些因素会影响策略的实际收益
