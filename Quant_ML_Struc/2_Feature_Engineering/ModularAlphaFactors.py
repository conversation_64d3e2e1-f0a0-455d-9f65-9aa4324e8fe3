"""
🔧 模块化Alpha因子生成器
📈 可独立修改和扩展的量化因子计算模块

使用方法:
1. 直接修改各个因子生成方法
2. 添加新的因子类别
3. 自定义因子计算逻辑

作者: Vincent
版本: 1.0
"""

import pandas as pd
import numpy as np
from scipy import stats
from typing import Dict, Tuple
import logging

class ModularAlphaFactors:
    """模块化Alpha因子生成器 - 每个因子类别独立可修改"""
    
    def __init__(self, data: pd.DataFrame):
        """
        初始化因子生成器
        
        Args:
            data: 包含OHLCV数据的DataFrame
                 必须包含列: open, high, low, close, volume
        """
        self.data = data.copy()
        self.factors = pd.DataFrame(index=data.index)
        self.factor_descriptions = {}
        
        # 提取基础价格和成交量数据
        self.close = data['close']
        self.high = data['high'] 
        self.low = data['low']
        self.open = data['open']
        self.volume = data['volume']
        
        logging.info(f"初始化模块化因子生成器，数据形状: {data.shape}")
    
    def generate_all_factors(self) -> Tuple[pd.DataFrame, Dict]:
        """
        生成所有Alpha因子
        
        Returns:
            factors: 生成的因子DataFrame
            descriptions: 因子描述字典
        """
        logging.info("开始生成模块化Alpha因子...")
        
        # 调用各个模块化方法
        self.momentum_factors()
        self.volume_factors()
        self.volatility_factors()
        self.price_structure_factors()
        self.mean_reversion_factors()
        self.complex_factors()

        # 添加新的因子类别以达到500个因子
        self.technical_indicators()
        self.statistical_factors()
        self.microstructure_factors()
        self.regime_factors()

        logging.info(f"成功生成 {len(self.factors.columns)} 个Alpha因子")
        return self.factors, self.factor_descriptions


    def momentum_factors(self):
        """
        🚀 动量类因子 (目标: ~80个因子)

        可修改内容:
        - 调整时间周期参数
        - 添加新的动量计算方法
        - 修改RSI和威廉指标的参数
        """
        logging.info("生成动量类因子...")

        # === 基础动量因子 ===
        # 扩展周期: [1, 2, 3, 5, 7, 10, 15, 20, 30, 45, 60]
        momentum_periods = [1, 2, 3, 5, 7, 10, 15, 20, 30, 45, 60]
        for period in momentum_periods:
            factor_name = f'momentum_{period}d'
            self.factors[factor_name] = self.close / self.close.shift(period) - 1
            self.factor_descriptions[factor_name] = f'{period}日价格动量'

        # === 对数动量因子 ===
        log_momentum_periods = [3, 5, 10, 20, 30]
        for period in log_momentum_periods:
            factor_name = f'log_momentum_{period}d'
            self.factors[factor_name] = np.log(self.close / self.close.shift(period))
            self.factor_descriptions[factor_name] = f'{period}日对数动量'

        # === 动量加速度 ===
        # 扩展周期: [3, 5, 10, 15, 20, 30]
        acceleration_periods = [3, 5, 10, 15, 20, 30]
        for period in acceleration_periods:
            factor_name = f'acceleration_{period}d'
            current_momentum = self.close / self.close.shift(period) - 1
            past_momentum = self.close.shift(period) / self.close.shift(period*2) - 1
            self.factors[factor_name] = current_momentum - past_momentum
            self.factor_descriptions[factor_name] = f'{period}日动量加速度'

        # === 动量强度因子 ===
        momentum_strength_periods = [5, 10, 15, 20, 30]
        for period in momentum_strength_periods:
            factor_name = f'momentum_strength_{period}d'
            momentum = self.close / self.close.shift(period) - 1
            momentum_std = momentum.rolling(period).std()
            self.factors[factor_name] = momentum / (momentum_std + 1e-8)
            self.factor_descriptions[factor_name] = f'{period}日动量强度'

        # === RSI相对强弱指标 ===
        # 扩展周期: [3, 5, 7, 10, 14, 15, 20, 25, 30]
        rsi_periods = [3, 5, 7, 10, 14, 15, 20, 25, 30]
        for period in rsi_periods:
            factor_name = f'rsi_{period}d'
            delta = self.close.diff()
            gain = delta.where(delta > 0, 0).rolling(period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
            rs = gain / loss
            self.factors[factor_name] = 100 - (100 / (1 + rs))
            self.factor_descriptions[factor_name] = f'{period}日RSI指标'

        # === 威廉指标 ===
        # 扩展周期: [5, 10, 14, 20, 25, 30]
        williams_periods = [5, 10, 14, 20, 25, 30]
        for period in williams_periods:
            factor_name = f'williams_r_{period}d'
            highest_high = self.high.rolling(period).max()
            lowest_low = self.low.rolling(period).min()
            self.factors[factor_name] = (highest_high - self.close) / (highest_high - lowest_low) * (-100)
            self.factor_descriptions[factor_name] = f'{period}日威廉指标'

        # === 动量持续性因子 ===
        persistence_periods = [5, 10, 15, 20]
        for period in persistence_periods:
            factor_name = f'momentum_persistence_{period}d'
            momentum_sign = np.sign(self.close.pct_change())
            persistence = momentum_sign.rolling(period).sum() / period
            self.factors[factor_name] = persistence
            self.factor_descriptions[factor_name] = f'{period}日动量持续性'

        # === 动量反转因子 ===
        reversal_periods = [3, 5, 10, 20]
        for period in reversal_periods:
            factor_name = f'momentum_reversal_{period}d'
            short_momentum = self.close / self.close.shift(period//2) - 1
            long_momentum = self.close / self.close.shift(period) - 1
            self.factors[factor_name] = short_momentum - long_momentum
            self.factor_descriptions[factor_name] = f'{period}日动量反转'

        # === MACD类因子 ===
        macd_combinations = [(12, 26), (5, 10), (8, 21), (10, 30)]
        for fast, slow in macd_combinations:
            factor_name = f'macd_{fast}_{slow}d'
            ema_fast = self.close.ewm(span=fast).mean()
            ema_slow = self.close.ewm(span=slow).mean()
            self.factors[factor_name] = (ema_fast - ema_slow) / self.close
            self.factor_descriptions[factor_name] = f'MACD({fast},{slow})指标'
    
    def volume_factors(self):
        """
        📊 成交量类因子 (目标: ~100个因子)

        可修改内容:
        - 调整成交量分析周期
        - 添加新的成交量指标
        - 修改VWAP计算方法
        """
        logging.info("生成成交量类因子...")

        # === 成交量变化率 ===
        # 扩展周期: [1, 2, 3, 5, 7, 10, 15, 20, 30, 45]
        volume_change_periods = [1, 2, 3, 5, 7, 10, 15, 20, 30, 45]
        for period in volume_change_periods:
            factor_name = f'volume_change_{period}d'
            self.factors[factor_name] = self.volume.pct_change(period)
            self.factor_descriptions[factor_name] = f'{period}日成交量变化率'

        # === 成交量相对比率 ===
        # 扩展周期: [3, 5, 7, 10, 15, 20, 30, 45, 60]
        volume_ratio_periods = [3, 5, 7, 10, 15, 20, 30, 45, 60]
        for period in volume_ratio_periods:
            factor_name = f'volume_ratio_{period}d'
            vol_ma = self.volume.rolling(period).mean()
            self.factors[factor_name] = self.volume / (vol_ma + 1e-8)
            self.factor_descriptions[factor_name] = f'{period}日成交量相对比率'

        # === 价格VWAP偏离度 ===
        # 扩展周期: [3, 5, 10, 15, 20, 30]
        vwap_periods = [3, 5, 10, 15, 20, 30]
        for period in vwap_periods:
            factor_name = f'price_vwap_deviation_{period}d'
            vwap = (self.close * self.volume).rolling(period).sum() / self.volume.rolling(period).sum()
            self.factors[factor_name] = (self.close - vwap) / (vwap + 1e-8)
            self.factor_descriptions[factor_name] = f'{period}日价格VWAP偏离度'

        # === 成交量趋势 ===
        # 扩展周期: [5, 10, 15, 20, 30]
        volume_trend_periods = [5, 10, 15, 20, 30]
        for period in volume_trend_periods:
            factor_name = f'volume_trend_{period}d'
            vol_trend = self.volume.rolling(period).apply(
                lambda x: stats.linregress(range(len(x)), x)[0] if len(x) == period else np.nan,
                raw=False
            )
            self.factors[factor_name] = vol_trend
            self.factor_descriptions[factor_name] = f'{period}日成交量趋势'

        # === 成交量波动率 ===
        # 扩展周期: [3, 5, 10, 15, 20, 30]
        volume_volatility_periods = [3, 5, 10, 15, 20, 30]
        for period in volume_volatility_periods:
            factor_name = f'volume_volatility_{period}d'
            self.factors[factor_name] = self.volume.rolling(period).std() / (self.volume.rolling(period).mean() + 1e-8)
            self.factor_descriptions[factor_name] = f'{period}日成交量波动率'

        # === 成交量动量 ===
        volume_momentum_periods = [3, 5, 10, 15, 20]
        for period in volume_momentum_periods:
            factor_name = f'volume_momentum_{period}d'
            self.factors[factor_name] = self.volume / self.volume.shift(period) - 1
            self.factor_descriptions[factor_name] = f'{period}日成交量动量'

        # === 成交量异常检测 ===
        volume_anomaly_periods = [5, 10, 20, 30]
        for period in volume_anomaly_periods:
            factor_name = f'volume_anomaly_{period}d'
            vol_mean = self.volume.rolling(period).mean()
            vol_std = self.volume.rolling(period).std()
            z_score = (self.volume - vol_mean) / (vol_std + 1e-8)
            self.factors[factor_name] = z_score
            self.factor_descriptions[factor_name] = f'{period}日成交量异常度'

        # === 成交量加权价格因子 ===
        vwp_periods = [5, 10, 15, 20]
        for period in vwp_periods:
            factor_name = f'volume_weighted_price_{period}d'
            vwp = (self.close * self.volume).rolling(period).sum() / self.volume.rolling(period).sum()
            self.factors[factor_name] = vwp / self.close - 1
            self.factor_descriptions[factor_name] = f'{period}日成交量加权价格'

        # === 成交量分布因子 ===
        volume_distribution_periods = [10, 20, 30]
        for period in volume_distribution_periods:
            factor_name = f'volume_skewness_{period}d'
            self.factors[factor_name] = self.volume.rolling(period).skew()
            self.factor_descriptions[factor_name] = f'{period}日成交量偏度'

            factor_name = f'volume_kurtosis_{period}d'
            self.factors[factor_name] = self.volume.rolling(period).kurt()
            self.factor_descriptions[factor_name] = f'{period}日成交量峰度'

        # === 成交量效率因子 ===
        volume_efficiency_periods = [5, 10, 20]
        for period in volume_efficiency_periods:
            factor_name = f'volume_efficiency_{period}d'
            price_change = abs(self.close.pct_change(period))
            volume_change = abs(self.volume.pct_change(period))
            efficiency = price_change / (volume_change + 1e-8)
            self.factors[factor_name] = efficiency
            self.factor_descriptions[factor_name] = f'{period}日成交量效率'

        # === 成交量相对强度 ===
        volume_strength_periods = [10, 20, 30, 60]
        for period in volume_strength_periods:
            factor_name = f'volume_relative_strength_{period}d'
            volume_rank = self.volume.rolling(period).rank(pct=True)
            self.factors[factor_name] = volume_rank
            self.factor_descriptions[factor_name] = f'{period}日成交量相对强度'
    
    def volatility_factors(self):
        """
        📈 波动率类因子 (目标: ~100个因子)

        可修改内容:
        - 调整波动率计算周期
        - 添加新的波动率估计方法
        - 修改ATR和GK波动率参数
        """
        logging.info("生成波动率类因子...")

        # === 收益率波动率 ===
        # 扩展周期: [3, 5, 7, 10, 15, 20, 30, 45, 60]
        return_vol_periods = [3, 5, 7, 10, 15, 20, 30, 45, 60]
        for period in return_vol_periods:
            factor_name = f'return_volatility_{period}d'
            returns = self.close.pct_change()
            self.factors[factor_name] = returns.rolling(period).std()
            self.factor_descriptions[factor_name] = f'{period}日收益率波动率'

        # === 平均真实波动率(ATR) ===
        # 扩展周期: [3, 5, 7, 10, 14, 20, 30]
        atr_periods = [3, 5, 7, 10, 14, 20, 30]
        for period in atr_periods:
            factor_name = f'atr_{period}d'
            tr1 = self.high - self.low
            tr2 = abs(self.high - self.close.shift(1))
            tr3 = abs(self.low - self.close.shift(1))
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            self.factors[factor_name] = true_range.rolling(period).mean()
            self.factor_descriptions[factor_name] = f'{period}日平均真实波动率'

        # === 价格范围波动率 ===
        # 扩展周期: [3, 5, 10, 15, 20, 30]
        range_vol_periods = [3, 5, 10, 15, 20, 30]
        for period in range_vol_periods:
            factor_name = f'price_range_volatility_{period}d'
            price_range = (self.high - self.low) / self.close
            self.factors[factor_name] = price_range.rolling(period).std()
            self.factor_descriptions[factor_name] = f'{period}日价格范围波动率'

        # === Garman-Klass波动率估计 ===
        # 扩展周期: [5, 10, 15, 20, 30]
        gk_periods = [5, 10, 15, 20, 30]
        for period in gk_periods:
            factor_name = f'gk_volatility_{period}d'
            gk_vol = (np.log(self.high/self.low) * np.log(self.high/self.low) -
                     (2*np.log(2)-1) * np.log(self.close/self.open) * np.log(self.close/self.open))
            self.factors[factor_name] = gk_vol.rolling(period).mean()
            self.factor_descriptions[factor_name] = f'{period}日GK波动率'

        # === 波动率比率 ===
        # 扩展周期组合: [(3,10), (5,20), (10,30), (5,15), (15,45)]
        volatility_ratios = [(3,10), (5,20), (10,30), (5,15), (15,45)]
        for short, long in volatility_ratios:
            factor_name = f'volatility_ratio_{short}_{long}d'
            vol_short = self.close.pct_change().rolling(short).std()
            vol_long = self.close.pct_change().rolling(long).std()
            self.factors[factor_name] = vol_short / (vol_long + 1e-8)
            self.factor_descriptions[factor_name] = f'{short}日vs{long}日波动率比率'

        # === 波动率偏度和峰度 ===
        vol_distribution_periods = [10, 20, 30]
        for period in vol_distribution_periods:
            returns = self.close.pct_change()

            factor_name = f'volatility_skewness_{period}d'
            self.factors[factor_name] = returns.rolling(period).skew()
            self.factor_descriptions[factor_name] = f'{period}日波动率偏度'

            factor_name = f'volatility_kurtosis_{period}d'
            self.factors[factor_name] = returns.rolling(period).kurt()
            self.factor_descriptions[factor_name] = f'{period}日波动率峰度'

        # === 实现波动率 ===
        realized_vol_periods = [5, 10, 20, 30]
        for period in realized_vol_periods:
            factor_name = f'realized_volatility_{period}d'
            returns = self.close.pct_change()
            realized_vol = np.sqrt((returns ** 2).rolling(period).sum())
            self.factors[factor_name] = realized_vol
            self.factor_descriptions[factor_name] = f'{period}日实现波动率'

        # === 波动率趋势 ===
        vol_trend_periods = [10, 20, 30]
        for period in vol_trend_periods:
            factor_name = f'volatility_trend_{period}d'
            returns = self.close.pct_change()
            vol_series = returns.rolling(5).std()
            vol_trend = vol_series.rolling(period).apply(
                lambda x: stats.linregress(range(len(x)), x)[0] if len(x) == period else np.nan,
                raw=False
            )
            self.factors[factor_name] = vol_trend
            self.factor_descriptions[factor_name] = f'{period}日波动率趋势'

        # === 波动率突破 ===
        vol_breakout_periods = [10, 20, 30]
        for period in vol_breakout_periods:
            factor_name = f'volatility_breakout_{period}d'
            returns = self.close.pct_change()
            current_vol = returns.rolling(5).std()
            historical_vol = returns.rolling(period).std()
            self.factors[factor_name] = current_vol / (historical_vol + 1e-8) - 1
            self.factor_descriptions[factor_name] = f'{period}日波动率突破'

        # === 条件波动率 ===
        conditional_vol_periods = [10, 20]
        for period in conditional_vol_periods:
            factor_name = f'conditional_volatility_{period}d'
            returns = self.close.pct_change()
            # 上涨日和下跌日的波动率
            up_vol = returns[returns > 0].rolling(period).std()
            down_vol = returns[returns < 0].rolling(period).std()
            self.factors[factor_name] = up_vol / (down_vol + 1e-8)
            self.factor_descriptions[factor_name] = f'{period}日条件波动率'
    
    def price_structure_factors(self):
        """
        🏗️ 价格结构类因子
        
        可修改内容:
        - 调整K线形态分析参数
        - 添加新的价格形态识别
        - 修改支撑阻力判断逻辑
        """
        logging.info("生成价格结构类因子...")
        
        # === K线形态因子 ===
        self.factors['body_size'] = abs(self.close - self.open) / self.close
        self.factors['upper_shadow'] = (self.high - np.maximum(self.close, self.open)) / self.close
        self.factors['lower_shadow'] = (np.minimum(self.close, self.open) - self.low) / self.close
        self.factors['total_range'] = (self.high - self.low) / self.close
        
        self.factor_descriptions.update({
            'body_size': 'K线实体大小',
            'upper_shadow': '上影线长度', 
            'lower_shadow': '下影线长度',
            'total_range': 'K线总范围'
        })
        
        # === 价格相对位置 ===
        # 可修改周期: [5, 10, 20]
        position_periods = [5, 10, 20]
        for period in position_periods:
            factor_name = f'price_position_{period}d'
            highest = self.high.rolling(period).max()
            lowest = self.low.rolling(period).min()
            self.factors[factor_name] = (self.close - lowest) / (highest - lowest)
            self.factor_descriptions[factor_name] = f'{period}日价格相对位置'
        
        # === 缺口因子 ===
        self.factors['gap_up'] = (self.open > self.close.shift(1)).astype(int)
        self.factors['gap_down'] = (self.open < self.close.shift(1)).astype(int)
        self.factors['gap_size'] = (self.open - self.close.shift(1)) / self.close.shift(1)
        
        self.factor_descriptions.update({
            'gap_up': '向上跳空',
            'gap_down': '向下跳空',
            'gap_size': '跳空幅度'
        })
        
        # === 支撑阻力突破 ===
        # 可修改周期: [10, 20, 30]
        breakout_periods = [10, 20, 30]
        for period in breakout_periods:
            # 新高突破
            factor_name = f'new_high_{period}d'
            self.factors[factor_name] = (self.high >= self.high.rolling(period).max()).astype(int)
            self.factor_descriptions[factor_name] = f'{period}日新高突破'
            
            # 新低突破
            factor_name = f'new_low_{period}d'
            self.factors[factor_name] = (self.low <= self.low.rolling(period).min()).astype(int)
            self.factor_descriptions[factor_name] = f'{period}日新低突破'


    def mean_reversion_factors(self):
        """
        🔄 均值回复类因子

        可修改内容:
        - 调整均线偏离计算周期
        - 修改布林带参数(标准差倍数)
        - 添加新的均值回复指标
        """
        logging.info("生成均值回复类因子...")

        # === 价格偏离移动平均线 ===
        # 可修改周期: [5, 10, 20, 30, 50]
        ma_deviation_periods = [5, 10, 20, 30, 50]
        for period in ma_deviation_periods:
            factor_name = f'price_ma_deviation_{period}d'
            ma = self.close.rolling(period).mean()
            self.factors[factor_name] = (self.close - ma) / ma
            self.factor_descriptions[factor_name] = f'{period}日均线偏离度'

        # === 布林带相关因子 ===
        # 可修改周期: [10, 20] 和标准差倍数: 2
        bollinger_periods = [10, 20]
        std_multiplier = 2  # 可修改标准差倍数
        for period in bollinger_periods:
            ma = self.close.rolling(period).mean()
            std = self.close.rolling(period).std()
            upper_band = ma + std_multiplier * std
            lower_band = ma - std_multiplier * std

            # 布林带位置
            factor_name = f'bollinger_position_{period}d'
            self.factors[factor_name] = (self.close - lower_band) / (upper_band - lower_band)
            self.factor_descriptions[factor_name] = f'{period}日布林带位置'

            # 布林带宽度
            factor_name = f'bollinger_width_{period}d'
            self.factors[factor_name] = (upper_band - lower_band) / ma
            self.factor_descriptions[factor_name] = f'{period}日布林带宽度'

        # === 价格回归偏离 ===
        # 可修改周期: [5, 10, 20]
        regression_periods = [5, 10, 20]
        for period in regression_periods:
            factor_name = f'regression_deviation_{period}d'

            def calc_regression_deviation(series):
                if len(series) < period:
                    return np.nan
                x = np.arange(len(series))
                slope, intercept = np.polyfit(x, series, 1)
                trend_line = slope * x + intercept
                return (series.iloc[-1] - trend_line[-1]) / series.iloc[-1]

            self.factors[factor_name] = self.close.rolling(period).apply(calc_regression_deviation, raw=False)
            self.factor_descriptions[factor_name] = f'{period}日回归偏离度'

        # === 均值回复强度 ===
        # 可修改周期: [5, 10, 20]
        reversion_periods = [5, 10, 20]
        for period in reversion_periods:
            factor_name = f'mean_reversion_strength_{period}d'
            returns = self.close.pct_change()

            def calc_autocorr(series):
                if len(series) < 2:
                    return np.nan
                return series.autocorr(lag=1)

            self.factors[factor_name] = returns.rolling(period).apply(calc_autocorr, raw=False)
            self.factor_descriptions[factor_name] = f'{period}日均值回复强度'

    def complex_factors(self):
        """
        🧩 复杂组合因子

        可修改内容:
        - 调整价量结合计算方法
        - 修改趋势强度计算逻辑
        - 添加新的复合指标
        """
        logging.info("生成复杂组合因子...")

        # === 价量结合因子 ===
        # 可修改周期: [5, 10, 20]
        price_volume_periods = [5, 10, 20]
        for period in price_volume_periods:
            factor_name = f'price_volume_correlation_{period}d'
            price_change = self.close.pct_change(period)
            volume_change = self.volume.pct_change(period)
            self.factors[factor_name] = price_change * volume_change
            self.factor_descriptions[factor_name] = f'{period}日价量相关性'

        # === 动量与波动率结合 ===
        # 可修改周期: [5, 10, 20]
        momentum_vol_periods = [5, 10, 20]
        for period in momentum_vol_periods:
            factor_name = f'momentum_volatility_ratio_{period}d'
            momentum = self.close / self.close.shift(period) - 1
            volatility = self.close.pct_change().rolling(period).std()
            self.factors[factor_name] = momentum / volatility
            self.factor_descriptions[factor_name] = f'{period}日动量波动率比'

        # === 趋势强度因子 ===
        # 可修改周期: [10, 20, 30]
        trend_strength_periods = [10, 20, 30]
        for period in trend_strength_periods:
            factor_name = f'trend_strength_{period}d'

            def calc_trend_strength(series):
                if len(series) < period:
                    return np.nan
                x = np.arange(len(series))
                try:
                    slope, intercept, r_value, p_value, std_err = stats.linregress(x, series)
                    return r_value ** 2
                except:
                    return np.nan

            self.factors[factor_name] = self.close.rolling(period).apply(calc_trend_strength, raw=False)
            self.factor_descriptions[factor_name] = f'{period}日趋势强度'

        # === 多时间框架动量分歧 ===
        # 可修改短期周期: [3, 5, 7] 和长期周期: [15, 20, 30]
        short_periods = [3, 5, 7]
        long_periods = [15, 20, 30]
        for short in short_periods:
            for long in long_periods:
                if short < long:
                    factor_name = f'momentum_divergence_{short}_{long}d'
                    short_mom = self.close / self.close.shift(short) - 1
                    long_mom = self.close / self.close.shift(long) - 1
                    self.factors[factor_name] = short_mom - long_mom
                    self.factor_descriptions[factor_name] = f'{short}vs{long}日动量分歧'

        # === 波动率调整收益率 ===
        # 可修改周期: [5, 10, 20]
        vol_adj_periods = [5, 10, 20]
        for period in vol_adj_periods:
            factor_name = f'volatility_adjusted_return_{period}d'
            returns = self.close.pct_change(period)
            volatility = self.close.pct_change().rolling(period).std()
            self.factors[factor_name] = returns / volatility
            self.factor_descriptions[factor_name] = f'{period}日波动率调整收益率'

        # === 相对强度因子 ===
        # 可修改周期: [20, 50, 100]
        relative_strength_periods = [20, 50, 100]
        for period in relative_strength_periods:
            factor_name = f'relative_strength_{period}d'
            historical_percentile = self.close.rolling(period).rank(pct=True)
            self.factors[factor_name] = historical_percentile
            self.factor_descriptions[factor_name] = f'{period}日相对强度'

        # === 成交量加权动量 ===
        # 可修改周期: [5, 10, 20]
        vol_weighted_periods = [5, 10, 20]
        for period in vol_weighted_periods:
            factor_name = f'volume_weighted_momentum_{period}d'
            price_change = self.close.pct_change()
            volume_weight = self.volume / self.volume.rolling(period).mean()
            weighted_momentum = (price_change * volume_weight).rolling(period).sum()
            self.factors[factor_name] = weighted_momentum
            self.factor_descriptions[factor_name] = f'{period}日成交量加权动量'

        # === 价格效率比率 ===
        # 可修改周期: [10, 20]
        efficiency_periods = [10, 20]
        for period in efficiency_periods:
            factor_name = f'efficiency_ratio_{period}d'
            price_change = abs(self.close - self.close.shift(period))
            path_length = abs(self.close.diff()).rolling(period).sum()
            efficiency_ratio = price_change / path_length
            self.factors[factor_name] = efficiency_ratio
            self.factor_descriptions[factor_name] = f'{period}日价格效率比率'

    def technical_indicators(self):
        """
        📊 技术指标类因子 (目标: ~80个因子)

        包含各种经典技术指标的变种
        """
        logging.info("生成技术指标类因子...")

        # === 布林带指标 ===
        bollinger_periods = [10, 15, 20, 25, 30]
        std_multipliers = [1.5, 2.0, 2.5]
        for period in bollinger_periods:
            for std_mult in std_multipliers:
                ma = self.close.rolling(period).mean()
                std = self.close.rolling(period).std()
                upper_band = ma + std_mult * std
                lower_band = ma - std_mult * std

                factor_name = f'bollinger_position_{period}d_{std_mult}std'
                self.factors[factor_name] = (self.close - lower_band) / (upper_band - lower_band)
                self.factor_descriptions[factor_name] = f'{period}日布林带位置({std_mult}倍标准差)'

                factor_name = f'bollinger_width_{period}d_{std_mult}std'
                self.factors[factor_name] = (upper_band - lower_band) / ma
                self.factor_descriptions[factor_name] = f'{period}日布林带宽度({std_mult}倍标准差)'

        # === KDJ指标 ===
        kdj_periods = [9, 14, 21]
        for period in kdj_periods:
            factor_name = f'kdj_k_{period}d'
            lowest_low = self.low.rolling(period).min()
            highest_high = self.high.rolling(period).max()
            rsv = (self.close - lowest_low) / (highest_high - lowest_low) * 100
            k_value = rsv.ewm(alpha=1/3).mean()
            self.factors[factor_name] = k_value
            self.factor_descriptions[factor_name] = f'{period}日KDJ-K值'

            factor_name = f'kdj_d_{period}d'
            d_value = k_value.ewm(alpha=1/3).mean()
            self.factors[factor_name] = d_value
            self.factor_descriptions[factor_name] = f'{period}日KDJ-D值'

            factor_name = f'kdj_j_{period}d'
            j_value = 3 * k_value - 2 * d_value
            self.factors[factor_name] = j_value
            self.factor_descriptions[factor_name] = f'{period}日KDJ-J值'

        # === CCI商品通道指标 ===
        cci_periods = [14, 20, 30]
        for period in cci_periods:
            factor_name = f'cci_{period}d'
            typical_price = (self.high + self.low + self.close) / 3
            sma = typical_price.rolling(period).mean()
            mad = typical_price.rolling(period).apply(lambda x: np.mean(np.abs(x - x.mean())))
            cci = (typical_price - sma) / (0.015 * mad)
            self.factors[factor_name] = cci
            self.factor_descriptions[factor_name] = f'{period}日CCI指标'

        # === 动量振荡器 ===
        momentum_osc_periods = [10, 14, 20]
        for period in momentum_osc_periods:
            factor_name = f'momentum_oscillator_{period}d'
            momentum_osc = (self.close / self.close.shift(period) - 1) * 100
            self.factors[factor_name] = momentum_osc
            self.factor_descriptions[factor_name] = f'{period}日动量振荡器'

        # === 价格振荡器 ===
        price_osc_combinations = [(12, 26), (5, 10), (8, 21)]
        for fast, slow in price_osc_combinations:
            factor_name = f'price_oscillator_{fast}_{slow}d'
            fast_ma = self.close.rolling(fast).mean()
            slow_ma = self.close.rolling(slow).mean()
            price_osc = (fast_ma - slow_ma) / slow_ma * 100
            self.factors[factor_name] = price_osc
            self.factor_descriptions[factor_name] = f'价格振荡器({fast},{slow})'

    def statistical_factors(self):
        """
        📈 统计类因子 (目标: ~60个因子)

        基于统计学的价格和成交量分析
        """
        logging.info("生成统计类因子...")

        # === 价格分布特征 ===
        distribution_periods = [10, 20, 30, 60]
        for period in distribution_periods:
            returns = self.close.pct_change()

            # 偏度
            factor_name = f'price_skewness_{period}d'
            self.factors[factor_name] = returns.rolling(period).skew()
            self.factor_descriptions[factor_name] = f'{period}日价格偏度'

            # 峰度
            factor_name = f'price_kurtosis_{period}d'
            self.factors[factor_name] = returns.rolling(period).kurt()
            self.factor_descriptions[factor_name] = f'{period}日价格峰度'

            # 变异系数
            factor_name = f'price_cv_{period}d'
            cv = returns.rolling(period).std() / (returns.rolling(period).mean() + 1e-8)
            self.factors[factor_name] = cv
            self.factor_descriptions[factor_name] = f'{period}日价格变异系数'

        # === 分位数因子 ===
        quantile_periods = [10, 20, 30]
        quantiles = [0.1, 0.25, 0.75, 0.9]
        for period in quantile_periods:
            for q in quantiles:
                factor_name = f'price_quantile_{period}d_q{int(q*100)}'
                price_quantile = self.close.rolling(period).quantile(q)
                self.factors[factor_name] = (self.close - price_quantile) / price_quantile
                self.factor_descriptions[factor_name] = f'{period}日价格{int(q*100)}%分位数偏离'

        # === 相关性因子 ===
        correlation_periods = [10, 20, 30]
        for period in correlation_periods:
            # 价格与成交量相关性
            factor_name = f'price_volume_corr_{period}d'
            price_returns = self.close.pct_change()
            volume_returns = self.volume.pct_change()
            correlation = price_returns.rolling(period).corr(volume_returns)
            self.factors[factor_name] = correlation
            self.factor_descriptions[factor_name] = f'{period}日价量相关性'

            # 高低价相关性
            factor_name = f'high_low_corr_{period}d'
            high_returns = self.high.pct_change()
            low_returns = self.low.pct_change()
            hl_correlation = high_returns.rolling(period).corr(low_returns)
            self.factors[factor_name] = hl_correlation
            self.factor_descriptions[factor_name] = f'{period}日高低价相关性'

    def microstructure_factors(self):
        """
        🔬 市场微观结构因子 (目标: ~80个因子)

        基于市场微观结构理论的因子
        """
        logging.info("生成市场微观结构因子...")

        # === 价差因子 ===
        spread_periods = [1, 3, 5, 10, 20]
        for period in spread_periods:
            # 高低价差
            factor_name = f'hl_spread_{period}d'
            hl_spread = (self.high - self.low) / self.close
            self.factors[factor_name] = hl_spread.rolling(period).mean()
            self.factor_descriptions[factor_name] = f'{period}日高低价差'

            # 开收价差
            factor_name = f'oc_spread_{period}d'
            oc_spread = (self.close - self.open) / self.open
            self.factors[factor_name] = oc_spread.rolling(period).mean()
            self.factor_descriptions[factor_name] = f'{period}日开收价差'

        # === 流动性代理指标 ===
        liquidity_periods = [5, 10, 20, 30]
        for period in liquidity_periods:
            # Amihud流动性指标
            factor_name = f'amihud_illiquidity_{period}d'
            returns = abs(self.close.pct_change())
            dollar_volume = self.close * self.volume
            amihud = returns / (dollar_volume + 1e-8)
            self.factors[factor_name] = amihud.rolling(period).mean()
            self.factor_descriptions[factor_name] = f'{period}日Amihud非流动性'

            # 成交量加权价格影响
            factor_name = f'price_impact_{period}d'
            price_change = abs(self.close.pct_change())
            volume_ratio = self.volume / self.volume.rolling(period).mean()
            price_impact = price_change / (volume_ratio + 1e-8)
            self.factors[factor_name] = price_impact.rolling(period).mean()
            self.factor_descriptions[factor_name] = f'{period}日价格影响'

        # === 订单流因子 ===
        order_flow_periods = [3, 5, 10, 20]
        for period in order_flow_periods:
            # 买卖压力指标
            factor_name = f'buying_pressure_{period}d'
            buying_pressure = (self.close - self.low) / (self.high - self.low)
            self.factors[factor_name] = buying_pressure.rolling(period).mean()
            self.factor_descriptions[factor_name] = f'{period}日买入压力'

            factor_name = f'selling_pressure_{period}d'
            selling_pressure = (self.high - self.close) / (self.high - self.low)
            self.factors[factor_name] = selling_pressure.rolling(period).mean()
            self.factor_descriptions[factor_name] = f'{period}日卖出压力'

        # === 价格发现因子 ===
        discovery_periods = [5, 10, 20]
        for period in discovery_periods:
            # 价格发现效率
            factor_name = f'price_discovery_{period}d'
            intraday_range = (self.high - self.low) / self.open
            overnight_gap = abs(self.open - self.close.shift(1)) / self.close.shift(1)
            discovery_ratio = intraday_range / (overnight_gap + 1e-8)
            self.factors[factor_name] = discovery_ratio.rolling(period).mean()
            self.factor_descriptions[factor_name] = f'{period}日价格发现效率'

        # === 信息不对称因子 ===
        info_asymmetry_periods = [10, 20, 30]
        for period in info_asymmetry_periods:
            # 信息不对称指标
            factor_name = f'info_asymmetry_{period}d'
            returns = self.close.pct_change()
            volume_surprise = self.volume / self.volume.rolling(period).mean() - 1
            info_asymmetry = abs(returns) * volume_surprise
            self.factors[factor_name] = info_asymmetry.rolling(period).mean()
            self.factor_descriptions[factor_name] = f'{period}日信息不对称'

    def regime_factors(self):
        """
        🌊 市场状态因子 (目标: ~80个因子)

        识别不同市场状态的因子
        """
        logging.info("生成市场状态因子...")

        # === 趋势状态因子 ===
        trend_periods = [10, 20, 30, 60]
        for period in trend_periods:
            # 趋势强度
            factor_name = f'trend_strength_{period}d'
            price_series = self.close.rolling(period)
            trend_strength = price_series.apply(
                lambda x: stats.linregress(range(len(x)), x)[2]**2 if len(x) == period else np.nan,
                raw=False
            )
            self.factors[factor_name] = trend_strength
            self.factor_descriptions[factor_name] = f'{period}日趋势强度'

            # 趋势一致性
            factor_name = f'trend_consistency_{period}d'
            returns = self.close.pct_change()
            trend_direction = np.sign(returns)
            consistency = abs(trend_direction.rolling(period).sum()) / period
            self.factors[factor_name] = consistency
            self.factor_descriptions[factor_name] = f'{period}日趋势一致性'

        # === 波动率状态 ===
        vol_regime_periods = [20, 30, 60]
        for period in vol_regime_periods:
            # 波动率状态
            factor_name = f'volatility_regime_{period}d'
            returns = self.close.pct_change()
            current_vol = returns.rolling(10).std()
            historical_vol = returns.rolling(period).std()
            vol_regime = current_vol / (historical_vol + 1e-8)
            self.factors[factor_name] = vol_regime
            self.factor_descriptions[factor_name] = f'{period}日波动率状态'

        # === 成交量状态 ===
        volume_regime_periods = [20, 30, 60]
        for period in volume_regime_periods:
            # 成交量状态
            factor_name = f'volume_regime_{period}d'
            current_volume = self.volume.rolling(5).mean()
            historical_volume = self.volume.rolling(period).mean()
            volume_regime = current_volume / (historical_volume + 1e-8)
            self.factors[factor_name] = volume_regime
            self.factor_descriptions[factor_name] = f'{period}日成交量状态'

        # === 市场压力指标 ===
        stress_periods = [10, 20, 30]
        for period in stress_periods:
            # 市场压力
            factor_name = f'market_stress_{period}d'
            returns = self.close.pct_change()
            vol = returns.rolling(period).std()
            skewness = returns.rolling(period).skew()
            stress = vol * abs(skewness)
            self.factors[factor_name] = stress
            self.factor_descriptions[factor_name] = f'{period}日市场压力'

        # === 均值回复状态 ===
        mean_reversion_periods = [10, 20, 30]
        for period in mean_reversion_periods:
            # 均值回复强度
            factor_name = f'mean_reversion_state_{period}d'
            returns = self.close.pct_change()
            autocorr = returns.rolling(period).apply(
                lambda x: x.autocorr(lag=1) if len(x) >= 2 else np.nan,
                raw=False
            )
            self.factors[factor_name] = -autocorr  # 负自相关表示均值回复
            self.factor_descriptions[factor_name] = f'{period}日均值回复状态'


# 使用示例和说明
if __name__ == "__main__":
    print("""
    🔧 模块化Alpha因子生成器使用指南
    =====================================

    1. 基本使用:
       from ModularAlphaFactors import ModularAlphaFactors
       generator = ModularAlphaFactors(data)
       factors, descriptions = generator.generate_all_factors()

    2. 单独生成某类因子:
       generator.momentum_factors()      # 只生成动量因子
       generator.volume_factors()        # 只生成成交量因子

    3. 自定义修改:
       - 直接修改各方法中的参数列表
       - 添加新的计算逻辑
       - 扩展因子类别

    4. 参数修改示例:
       - momentum_periods = [1, 3, 5, 10]  # 修改动量周期
       - std_multiplier = 2.5              # 修改布林带标准差倍数
       - breakout_periods = [5, 15, 25]    # 修改突破判断周期
    """)
