
# TSLA Alpha因子挖掘结果
# 生成时间: 2025-06-07 10:38:39
# 数据时间范围: 2020-01-02 00:00:00 至 2025-04-28 00:00:00
#
# 因子分类标准:
# - 统计显著因子: p < 0.05 (共64个)
# - 高质量因子: p < 0.05 且 |t| > 1.96 (共64个)
# - 顶级因子: p < 0.01 且 |t| > 2.58 (共17个)

总计发现 64 个统计显著的Alpha因子，用于预测TSLA未来1日收益率

================================================================================
统计显著Alpha因子详细信息
================================================================================


1. 因子名称: lower_shadow [🥇 顶级]
   描述: 下影线长度

   统计指标:
   - R²: 0.009397 (解释0.94%的收益率变异)
   - t值: -3.5573 (绝对值: 3.5573)
   - p值: 0.00038786 ***
   - 相关系数: -0.096937
   - 方向准确率: 0.4970 (49.7%)
   - 有效观测数: 1336

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 高度适用 (强统计显著性)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

2. 因子名称: price_volume_correlation_20d [🥇 顶级]
   描述: 20日价量相关性

   统计指标:
   - R²: 0.009256 (解释0.93%的收益率变异)
   - t值: -3.5037 (绝对值: 3.5037)
   - p值: 0.00047422 ***
   - 相关系数: -0.096208
   - 方向准确率: 0.4954 (49.5%)
   - 有效观测数: 1316

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      成交量因子：反映市场参与度和资金流向变化

   机器学习适用性:
   ✅ 线性模型: 高度适用 (强统计显著性)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

3. 因子名称: amihud_illiquidity_20d [🥇 顶级]
   描述: 20日Amihud非流动性

   统计指标:
   - R²: 0.007012 (解释0.70%的收益率变异)
   - t值: 3.0461 (绝对值: 3.0461)
   - p值: 0.00236442 **
   - 相关系数: 0.083737
   - 方向准确率: 0.4916 (49.2%)
   - 有效观测数: 1316

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 高度适用 (强统计显著性)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

4. 因子名称: amihud_illiquidity_5d [🥇 顶级]
   描述: 5日Amihud非流动性

   统计指标:
   - R²: 0.006253 (解释0.63%的收益率变异)
   - t值: 2.8919 (绝对值: 2.8919)
   - p值: 0.00389131 **
   - 相关系数: 0.079079
   - 方向准确率: 0.5229 (52.3%)
   - 有效观测数: 1331

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 高度适用 (强统计显著性)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ✅ 神经网络: 适用 (中等方向预测)

--------------------------------------------------------------------------------

5. 因子名称: amihud_illiquidity_10d [🥇 顶级]
   描述: 10日Amihud非流动性

   统计指标:
   - R²: 0.006159 (解释0.62%的收益率变异)
   - t值: 2.8644 (绝对值: 2.8644)
   - p值: 0.00424380 **
   - 相关系数: 0.078478
   - 方向准确率: 0.5271 (52.7%)
   - 有效观测数: 1326

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 高度适用 (强统计显著性)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ✅ 神经网络: 适用 (中等方向预测)

--------------------------------------------------------------------------------

6. 因子名称: cci_20d [🥇 顶级]
   描述: 20日CCI指标

   统计指标:
   - R²: 0.005900 (解释0.59%的收益率变异)
   - t值: 2.7937 (绝对值: 2.7937)
   - p值: 0.00528713 **
   - 相关系数: 0.076811
   - 方向准确率: 0.4897 (49.0%)
   - 有效观测数: 1317

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 高度适用 (强统计显著性)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

7. 因子名称: atr_14d [🥇 顶级]
   描述: 14日平均真实波动率

   统计指标:
   - R²: 0.005756 (解释0.58%的收益率变异)
   - t值: -2.7655 (绝对值: 2.7655)
   - p值: 0.00576249 **
   - 相关系数: -0.075870
   - 方向准确率: 0.5155 (51.5%)
   - 有效观测数: 1323

   计算方式:
      ATR(14) = MA(TrueRange, 14)
   TrueRange = max(high-low, |high-prev_close|, |low-prev_close|)

   因子意义与作用:
      波动率因子：衡量价格波动程度，高波动率通常伴随高风险高收益

   机器学习适用性:
   ✅ 线性模型: 高度适用 (强统计显著性)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

8. 因子名称: amihud_illiquidity_30d [🥇 顶级]
   描述: 30日Amihud非流动性

   统计指标:
   - R²: 0.005783 (解释0.58%的收益率变异)
   - t值: 2.7540 (绝对值: 2.7540)
   - p值: 0.00596842 **
   - 相关系数: 0.076045
   - 方向准确率: 0.5038 (50.4%)
   - 有效观测数: 1306

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 高度适用 (强统计显著性)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

9. 因子名称: atr_7d [🥇 顶级]
   描述: 7日平均真实波动率

   统计指标:
   - R²: 0.005604 (解释0.56%的收益率变异)
   - t值: -2.7358 (绝对值: 2.7358)
   - p值: 0.00630585 **
   - 相关系数: -0.074862
   - 方向准确率: 0.5090 (50.9%)
   - 有效观测数: 1330

   计算方式:
      ATR(7) = MA(TrueRange, 7)
   TrueRange = max(high-low, |high-prev_close|, |low-prev_close|)

   因子意义与作用:
      波动率因子：衡量价格波动程度，高波动率通常伴随高风险高收益

   机器学习适用性:
   ✅ 线性模型: 高度适用 (强统计显著性)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

10. 因子名称: price_volume_corr_10d [🥇 顶级]
   描述: 10日价量相关性

   统计指标:
   - R²: 0.005446 (解释0.54%的收益率变异)
   - t值: 2.6927 (绝对值: 2.6927)
   - p值: 0.00717797 **
   - 相关系数: 0.073799
   - 方向准确率: 0.5053 (50.5%)
   - 有效观测数: 1326

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      成交量因子：反映市场参与度和资金流向变化

   机器学习适用性:
   ✅ 线性模型: 高度适用 (强统计显著性)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

11. 因子名称: atr_10d [🥇 顶级]
   描述: 10日平均真实波动率

   统计指标:
   - R²: 0.005425 (解释0.54%的收益率变异)
   - t值: -2.6884 (绝对值: 2.6884)
   - p值: 0.00726911 **
   - 相关系数: -0.073656
   - 方向准确率: 0.5486 (54.9%)
   - 有效观测数: 1327

   计算方式:
      ATR(10) = MA(TrueRange, 10)
   TrueRange = max(high-low, |high-prev_close|, |low-prev_close|)

   因子意义与作用:
      波动率因子：衡量价格波动程度，高波动率通常伴随高风险高收益

   机器学习适用性:
   ✅ 线性模型: 高度适用 (强统计显著性)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ✅ 神经网络: 适用 (中等方向预测)

--------------------------------------------------------------------------------

12. 因子名称: bollinger_position_20d_1.5std [🥇 顶级]
   描述: 20日布林带位置(1.5倍标准差)

   统计指标:
   - R²: 0.005371 (解释0.54%的收益率变异)
   - t值: 2.6648 (绝对值: 2.6648)
   - p值: 0.00779710 **
   - 相关系数: 0.073289
   - 方向准确率: 0.4746 (47.5%)
   - 有效观测数: 1317

   计算方式:
      (close - lower_band) / (upper_band - lower_band)
   布林带位置，20日周期

   因子意义与作用:
      布林带因子：衡量价格相对于统计均值的位置，用于判断超买超卖

   机器学习适用性:
   ✅ 线性模型: 高度适用 (强统计显著性)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

13. 因子名称: bollinger_position_20d_2.5std [🥇 顶级]
   描述: 20日布林带位置(2.5倍标准差)

   统计指标:
   - R²: 0.005371 (解释0.54%的收益率变异)
   - t值: 2.6648 (绝对值: 2.6648)
   - p值: 0.00779710 **
   - 相关系数: 0.073289
   - 方向准确率: 0.4746 (47.5%)
   - 有效观测数: 1317

   计算方式:
      (close - lower_band) / (upper_band - lower_band)
   布林带位置，20日周期

   因子意义与作用:
      布林带因子：衡量价格相对于统计均值的位置，用于判断超买超卖

   机器学习适用性:
   ✅ 线性模型: 高度适用 (强统计显著性)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

14. 因子名称: bollinger_position_20d_2.0std [🥇 顶级]
   描述: 20日布林带位置(2.0倍标准差)

   统计指标:
   - R²: 0.005371 (解释0.54%的收益率变异)
   - t值: 2.6648 (绝对值: 2.6648)
   - p值: 0.00779710 **
   - 相关系数: 0.073289
   - 方向准确率: 0.4746 (47.5%)
   - 有效观测数: 1317

   计算方式:
      (close - lower_band) / (upper_band - lower_band)
   布林带位置，20日周期

   因子意义与作用:
      布林带因子：衡量价格相对于统计均值的位置，用于判断超买超卖

   机器学习适用性:
   ✅ 线性模型: 高度适用 (强统计显著性)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

15. 因子名称: bollinger_position_20d [🥇 顶级]
   描述: 20日布林带位置

   统计指标:
   - R²: 0.005371 (解释0.54%的收益率变异)
   - t值: 2.6648 (绝对值: 2.6648)
   - p值: 0.00779710 **
   - 相关系数: 0.073289
   - 方向准确率: 0.4746 (47.5%)
   - 有效观测数: 1317

   计算方式:
      (close - lower_band) / (upper_band - lower_band)
   布林带位置，20日周期

   因子意义与作用:
      布林带因子：衡量价格相对于统计均值的位置，用于判断超买超卖

   机器学习适用性:
   ✅ 线性模型: 高度适用 (强统计显著性)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

16. 因子名称: momentum_strength_10d [🥇 顶级]
   描述: 10日动量强度

   统计指标:
   - R²: 0.005321 (解释0.53%的收益率变异)
   - t值: 2.6522 (绝对值: 2.6522)
   - p值: 0.00809372 **
   - 相关系数: 0.072943
   - 方向准确率: 0.4943 (49.4%)
   - 有效观测数: 1317

   计算方式:
      close / close.shift(strength) - 1
   (当前收盘价 / strength日前收盘价 - 1)

   因子意义与作用:
      动量因子：衡量价格趋势强度，正值表示上涨趋势，负值表示下跌趋势

   机器学习适用性:
   ✅ 线性模型: 高度适用 (强统计显著性)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

17. 因子名称: price_cv_30d [🥇 顶级]
   描述: 30日价格变异系数

   统计指标:
   - R²: 0.005161 (解释0.52%的收益率变异)
   - t值: 2.6009 (绝对值: 2.6009)
   - p值: 0.00940216 **
   - 相关系数: 0.071840
   - 方向准确率: 0.5054 (50.5%)
   - 有效观测数: 1306

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 高度适用 (强统计显著性)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

18. 因子名称: cci_30d [🥈 高质量]
   描述: 30日CCI指标

   统计指标:
   - R²: 0.005058 (解释0.51%的收益率变异)
   - t值: 2.5756 (绝对值: 2.5756)
   - p值: 0.01011480 *
   - 相关系数: 0.071118
   - 方向准确率: 0.5004 (50.0%)
   - 有效观测数: 1307

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

19. 因子名称: mean_reversion_state_10d [🥈 高质量]
   描述: 10日均值回复状态

   统计指标:
   - R²: 0.004933 (解释0.49%的收益率变异)
   - t值: -2.5619 (绝对值: 2.5619)
   - p值: 0.01051921 *
   - 相关系数: -0.070234
   - 方向准确率: 0.4985 (49.8%)
   - 有效观测数: 1326

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

20. 因子名称: mean_reversion_strength_10d [🥈 高质量]
   描述: 10日均值回复强度

   统计指标:
   - R²: 0.004933 (解释0.49%的收益率变异)
   - t值: 2.5619 (绝对值: 2.5619)
   - p值: 0.01051921 *
   - 相关系数: 0.070234
   - 方向准确率: 0.5000 (50.0%)
   - 有效观测数: 1326

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

21. 因子名称: volatility_adjusted_return_10d [🥈 高质量]
   描述: 10日波动率调整收益率

   统计指标:
   - R²: 0.004895 (解释0.49%的收益率变异)
   - t值: 2.5519 (绝对值: 2.5519)
   - p值: 0.01082445 *
   - 相关系数: 0.069961
   - 方向准确率: 0.5030 (50.3%)
   - 有效观测数: 1326

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      波动率因子：衡量价格波动程度，高波动率通常伴随高风险高收益

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

22. 因子名称: momentum_volatility_ratio_10d [🥈 高质量]
   描述: 10日动量波动率比

   统计指标:
   - R²: 0.004895 (解释0.49%的收益率变异)
   - t值: 2.5519 (绝对值: 2.5519)
   - p值: 0.01082445 *
   - 相关系数: 0.069961
   - 方向准确率: 0.5030 (50.3%)
   - 有效观测数: 1326

   计算方式:
      close / close.shift(volatility) - 1
   (当前收盘价 / volatility日前收盘价 - 1)

   因子意义与作用:
      动量因子：衡量价格趋势强度，正值表示上涨趋势，负值表示下跌趋势

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

23. 因子名称: atr_20d [🥈 高质量]
   描述: 20日平均真实波动率

   统计指标:
   - R²: 0.004869 (解释0.49%的收益率变异)
   - t值: -2.5364 (绝对值: 2.5364)
   - p值: 0.01131360 *
   - 相关系数: -0.069775
   - 方向准确率: 0.5133 (51.3%)
   - 有效观测数: 1317

   计算方式:
      ATR(20) = MA(TrueRange, 20)
   TrueRange = max(high-low, |high-prev_close|, |low-prev_close|)

   因子意义与作用:
      波动率因子：衡量价格波动程度，高波动率通常伴随高风险高收益

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

24. 因子名称: rsi_10d [🥈 高质量]
   描述: 10日RSI指标

   统计指标:
   - R²: 0.004762 (解释0.48%的收益率变异)
   - t值: 2.5179 (绝对值: 2.5179)
   - p值: 0.01192355 *
   - 相关系数: 0.069007
   - 方向准确率: 0.4913 (49.1%)
   - 有效观测数: 1327

   计算方式:
      RSI(10) = 100 - (100 / (1 + RS))
   其中RS = 10日平均涨幅 / 10日平均跌幅

   因子意义与作用:
      相对强弱指标：衡量超买超卖状态，>70超买，<30超卖

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

25. 因子名称: volume_volatility_15d [🥈 高质量]
   描述: 15日成交量波动率

   统计指标:
   - R²: 0.004747 (解释0.47%的收益率变异)
   - t值: 2.5092 (绝对值: 2.5092)
   - p值: 0.01221803 *
   - 相关系数: 0.068901
   - 方向准确率: 0.5076 (50.8%)
   - 有效观测数: 1322

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      成交量因子：反映市场参与度和资金流向变化

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

26. 因子名称: bollinger_position_25d_1.5std [🥈 高质量]
   描述: 25日布林带位置(1.5倍标准差)

   统计指标:
   - R²: 0.004709 (解释0.47%的收益率变异)
   - t值: 2.4897 (绝对值: 2.4897)
   - p值: 0.01291009 *
   - 相关系数: 0.068624
   - 方向准确率: 0.4870 (48.7%)
   - 有效观测数: 1312

   计算方式:
      (close - lower_band) / (upper_band - lower_band)
   布林带位置，25日周期

   因子意义与作用:
      布林带因子：衡量价格相对于统计均值的位置，用于判断超买超卖

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

27. 因子名称: bollinger_position_25d_2.0std [🥈 高质量]
   描述: 25日布林带位置(2.0倍标准差)

   统计指标:
   - R²: 0.004709 (解释0.47%的收益率变异)
   - t值: 2.4897 (绝对值: 2.4897)
   - p值: 0.01291009 *
   - 相关系数: 0.068624
   - 方向准确率: 0.4870 (48.7%)
   - 有效观测数: 1312

   计算方式:
      (close - lower_band) / (upper_band - lower_band)
   布林带位置，25日周期

   因子意义与作用:
      布林带因子：衡量价格相对于统计均值的位置，用于判断超买超卖

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

28. 因子名称: bollinger_position_25d_2.5std [🥈 高质量]
   描述: 25日布林带位置(2.5倍标准差)

   统计指标:
   - R²: 0.004709 (解释0.47%的收益率变异)
   - t值: 2.4897 (绝对值: 2.4897)
   - p值: 0.01291009 *
   - 相关系数: 0.068624
   - 方向准确率: 0.4870 (48.7%)
   - 有效观测数: 1312

   计算方式:
      (close - lower_band) / (upper_band - lower_band)
   布林带位置，25日周期

   因子意义与作用:
      布林带因子：衡量价格相对于统计均值的位置，用于判断超买超卖

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

29. 因子名称: atr_30d [🥈 高质量]
   描述: 30日平均真实波动率

   统计指标:
   - R²: 0.004668 (解释0.47%的收益率变异)
   - t值: -2.4738 (绝对值: 2.4738)
   - p值: 0.01349397 *
   - 相关系数: -0.068320
   - 方向准确率: 0.5249 (52.5%)
   - 有效观测数: 1307

   计算方式:
      ATR(30) = MA(TrueRange, 30)
   TrueRange = max(high-low, |high-prev_close|, |low-prev_close|)

   因子意义与作用:
      波动率因子：衡量价格波动程度，高波动率通常伴随高风险高收益

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ✅ 神经网络: 适用 (中等方向预测)

--------------------------------------------------------------------------------

30. 因子名称: rsi_15d [🥈 高质量]
   描述: 15日RSI指标

   统计指标:
   - R²: 0.004552 (解释0.46%的收益率变异)
   - t值: 2.4567 (绝对值: 2.4567)
   - p值: 0.01414932 *
   - 相关系数: 0.067465
   - 方向准确率: 0.4985 (49.8%)
   - 有效观测数: 1322

   计算方式:
      RSI(15) = 100 - (100 / (1 + RS))
   其中RS = 15日平均涨幅 / 15日平均跌幅

   因子意义与作用:
      相对强弱指标：衡量超买超卖状态，>70超买，<30超卖

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

31. 因子名称: price_position_10d [🥈 高质量]
   描述: 10日价格相对位置

   统计指标:
   - R²: 0.004409 (解释0.44%的收益率变异)
   - t值: 2.4225 (绝对值: 2.4225)
   - p值: 0.01554885 *
   - 相关系数: 0.066403
   - 方向准确率: 0.4959 (49.6%)
   - 有效观测数: 1327

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

32. 因子名称: williams_r_10d [🥈 高质量]
   描述: 10日威廉指标

   统计指标:
   - R²: 0.004409 (解释0.44%的收益率变异)
   - t值: 2.4225 (绝对值: 2.4225)
   - p值: 0.01554885 *
   - 相关系数: 0.066403
   - 方向准确率: 0.4959 (49.6%)
   - 有效观测数: 1327

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

33. 因子名称: volume_volatility_5d [🥈 高质量]
   描述: 5日成交量波动率

   统计指标:
   - R²: 0.004322 (解释0.43%的收益率变异)
   - t值: 2.4029 (绝对值: 2.4029)
   - p值: 0.01640252 *
   - 相关系数: 0.065746
   - 方向准确率: 0.5143 (51.4%)
   - 有效观测数: 1332

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      成交量因子：反映市场参与度和资金流向变化

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

34. 因子名称: rsi_14d [🥈 高质量]
   描述: 14日RSI指标

   统计指标:
   - R²: 0.004301 (解释0.43%的收益率变异)
   - t值: 2.3887 (绝对值: 2.3887)
   - p值: 0.01704735 *
   - 相关系数: 0.065581
   - 方向准确率: 0.4890 (48.9%)
   - 有效观测数: 1323

   计算方式:
      RSI(14) = 100 - (100 / (1 + RS))
   其中RS = 14日平均涨幅 / 14日平均跌幅

   因子意义与作用:
      相对强弱指标：衡量超买超卖状态，>70超买，<30超卖

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

35. 因子名称: info_asymmetry_20d [🥈 高质量]
   描述: 20日信息不对称

   统计指标:
   - R²: 0.004342 (解释0.43%的收益率变异)
   - t值: 2.3774 (绝对值: 2.3774)
   - p值: 0.01758046 *
   - 相关系数: 0.065895
   - 方向准确率: 0.5200 (52.0%)
   - 有效观测数: 1298

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ✅ 神经网络: 适用 (中等方向预测)

--------------------------------------------------------------------------------

36. 因子名称: kdj_k_14d [🥈 高质量]
   描述: 14日KDJ-K值

   统计指标:
   - R²: 0.004116 (解释0.41%的收益率变异)
   - t值: 2.3367 (绝对值: 2.3367)
   - p值: 0.01960475 *
   - 相关系数: 0.064158
   - 方向准确率: 0.4853 (48.5%)
   - 有效观测数: 1323

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

37. 因子名称: williams_r_14d [🥈 高质量]
   描述: 14日威廉指标

   统计指标:
   - R²: 0.004107 (解释0.41%的收益率变异)
   - t值: 2.3340 (绝对值: 2.3340)
   - p值: 0.01974354 *
   - 相关系数: 0.064086
   - 方向准确率: 0.5019 (50.2%)
   - 有效观测数: 1323

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

38. 因子名称: return_volatility_60d [🥈 高质量]
   描述: 60日收益率波动率

   统计指标:
   - R²: 0.004253 (解释0.43%的收益率变异)
   - t值: 2.3327 (绝对值: 2.3327)
   - p值: 0.01981853 *
   - 相关系数: 0.065215
   - 方向准确率: 0.5063 (50.6%)
   - 有效观测数: 1276

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      波动率因子：衡量价格波动程度，高波动率通常伴随高风险高收益

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

39. 因子名称: kdj_k_9d [🥈 高质量]
   描述: 9日KDJ-K值

   统计指标:
   - R²: 0.004050 (解释0.41%的收益率变异)
   - t值: 2.3221 (绝对值: 2.3221)
   - p值: 0.02037802 *
   - 相关系数: 0.063640
   - 方向准确率: 0.4902 (49.0%)
   - 有效观测数: 1328

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

40. 因子名称: info_asymmetry_10d [🥈 高质量]
   描述: 10日信息不对称

   统计指标:
   - R²: 0.003986 (解释0.40%的收益率变异)
   - t值: 2.2949 (绝对值: 2.2949)
   - p值: 0.02189591 *
   - 相关系数: 0.063135
   - 方向准确率: 0.5379 (53.8%)
   - 有效观测数: 1318

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ✅ 神经网络: 适用 (中等方向预测)

--------------------------------------------------------------------------------

41. 因子名称: relative_strength_20d [🥈 高质量]
   描述: 20日相对强度

   统计指标:
   - R²: 0.003896 (解释0.39%的收益率变异)
   - t值: 2.2680 (绝对值: 2.2680)
   - p值: 0.02348973 *
   - 相关系数: 0.062422
   - 方向准确率: 0.3591 (35.9%)
   - 有效观测数: 1317

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

42. 因子名称: kdj_d_9d [🥈 高质量]
   描述: 9日KDJ-D值

   统计指标:
   - R²: 0.003850 (解释0.38%的收益率变异)
   - t值: 2.2638 (绝对值: 2.2638)
   - p值: 0.02374773 *
   - 相关系数: 0.062048
   - 方向准确率: 0.4985 (49.8%)
   - 有效观测数: 1328

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

43. 因子名称: bollinger_position_30d_2.5std [🥈 高质量]
   描述: 30日布林带位置(2.5倍标准差)

   统计指标:
   - R²: 0.003892 (解释0.39%的收益率变异)
   - t值: 2.2579 (绝对值: 2.2579)
   - p值: 0.02411365 *
   - 相关系数: 0.062382
   - 方向准确率: 0.4927 (49.3%)
   - 有效观测数: 1307

   计算方式:
      (close - lower_band) / (upper_band - lower_band)
   布林带位置，30日周期

   因子意义与作用:
      布林带因子：衡量价格相对于统计均值的位置，用于判断超买超卖

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

44. 因子名称: bollinger_position_30d_2.0std [🥈 高质量]
   描述: 30日布林带位置(2.0倍标准差)

   统计指标:
   - R²: 0.003892 (解释0.39%的收益率变异)
   - t值: 2.2579 (绝对值: 2.2579)
   - p值: 0.02411365 *
   - 相关系数: 0.062382
   - 方向准确率: 0.4927 (49.3%)
   - 有效观测数: 1307

   计算方式:
      (close - lower_band) / (upper_band - lower_band)
   布林带位置，30日周期

   因子意义与作用:
      布林带因子：衡量价格相对于统计均值的位置，用于判断超买超卖

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

45. 因子名称: bollinger_position_30d_1.5std [🥈 高质量]
   描述: 30日布林带位置(1.5倍标准差)

   统计指标:
   - R²: 0.003892 (解释0.39%的收益率变异)
   - t值: 2.2579 (绝对值: 2.2579)
   - p值: 0.02411365 *
   - 相关系数: 0.062382
   - 方向准确率: 0.4927 (49.3%)
   - 有效观测数: 1307

   计算方式:
      (close - lower_band) / (upper_band - lower_band)
   布林带位置，30日周期

   因子意义与作用:
      布林带因子：衡量价格相对于统计均值的位置，用于判断超买超卖

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

46. 因子名称: atr_5d [🥈 高质量]
   描述: 5日平均真实波动率

   统计指标:
   - R²: 0.003781 (解释0.38%的收益率变异)
   - t值: -2.2468 (绝对值: 2.2468)
   - p值: 0.02481841 *
   - 相关系数: -0.061491
   - 方向准确率: 0.5203 (52.0%)
   - 有效观测数: 1332

   计算方式:
      ATR(5) = MA(TrueRange, 5)
   TrueRange = max(high-low, |high-prev_close|, |low-prev_close|)

   因子意义与作用:
      波动率因子：衡量价格波动程度，高波动率通常伴随高风险高收益

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ✅ 神经网络: 适用 (中等方向预测)

--------------------------------------------------------------------------------

47. 因子名称: bollinger_position_15d_2.0std [🥈 高质量]
   描述: 15日布林带位置(2.0倍标准差)

   统计指标:
   - R²: 0.003779 (解释0.38%的收益率变异)
   - t值: 2.2377 (绝对值: 2.2377)
   - p值: 0.02540436 *
   - 相关系数: 0.061475
   - 方向准确率: 0.4750 (47.5%)
   - 有效观测数: 1322

   计算方式:
      (close - lower_band) / (upper_band - lower_band)
   布林带位置，15日周期

   因子意义与作用:
      布林带因子：衡量价格相对于统计均值的位置，用于判断超买超卖

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

48. 因子名称: bollinger_position_15d_2.5std [🥈 高质量]
   描述: 15日布林带位置(2.5倍标准差)

   统计指标:
   - R²: 0.003779 (解释0.38%的收益率变异)
   - t值: 2.2377 (绝对值: 2.2377)
   - p值: 0.02540436 *
   - 相关系数: 0.061475
   - 方向准确率: 0.4750 (47.5%)
   - 有效观测数: 1322

   计算方式:
      (close - lower_band) / (upper_band - lower_band)
   布林带位置，15日周期

   因子意义与作用:
      布林带因子：衡量价格相对于统计均值的位置，用于判断超买超卖

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

49. 因子名称: bollinger_position_15d_1.5std [🥈 高质量]
   描述: 15日布林带位置(1.5倍标准差)

   统计指标:
   - R²: 0.003779 (解释0.38%的收益率变异)
   - t值: 2.2377 (绝对值: 2.2377)
   - p值: 0.02540436 *
   - 相关系数: 0.061475
   - 方向准确率: 0.4750 (47.5%)
   - 有效观测数: 1322

   计算方式:
      (close - lower_band) / (upper_band - lower_band)
   布林带位置，15日周期

   因子意义与作用:
      布林带因子：衡量价格相对于统计均值的位置，用于判断超买超卖

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

50. 因子名称: volume_volatility_10d [🥈 高质量]
   描述: 10日成交量波动率

   统计指标:
   - R²: 0.003753 (解释0.38%的收益率变异)
   - t值: 2.2341 (绝对值: 2.2341)
   - p值: 0.02564585 *
   - 相关系数: 0.061259
   - 方向准确率: 0.5139 (51.4%)
   - 有效观测数: 1327

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      成交量因子：反映市场参与度和资金流向变化

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

51. 因子名称: kdj_d_14d [🥈 高质量]
   描述: 14日KDJ-D值

   统计指标:
   - R²: 0.003679 (解释0.37%的收益率变异)
   - t值: 2.2086 (绝对值: 2.2086)
   - p值: 0.02737524 *
   - 相关系数: 0.060654
   - 方向准确率: 0.5117 (51.2%)
   - 有效观测数: 1323

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

52. 因子名称: cci_14d [🥈 高质量]
   描述: 14日CCI指标

   统计指标:
   - R²: 0.003674 (解释0.37%的收益率变异)
   - t值: 2.2072 (绝对值: 2.2072)
   - p值: 0.02747455 *
   - 相关系数: 0.060615
   - 方向准确率: 0.4906 (49.1%)
   - 有效观测数: 1323

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

53. 因子名称: kdj_j_21d [🥈 高质量]
   描述: 21日KDJ-J值

   统计指标:
   - R²: 0.003686 (解释0.37%的收益率变异)
   - t值: 2.2048 (绝对值: 2.2048)
   - p值: 0.02764147 *
   - 相关系数: 0.060711
   - 方向准确率: 0.5023 (50.2%)
   - 有效观测数: 1316

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

54. 因子名称: price_position_20d [🥈 高质量]
   描述: 20日价格相对位置

   统计指标:
   - R²: 0.003507 (解释0.35%的收益率变异)
   - t值: 2.1513 (绝对值: 2.1513)
   - p值: 0.03163111 *
   - 相关系数: 0.059222
   - 方向准确率: 0.4935 (49.4%)
   - 有效观测数: 1317

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

55. 因子名称: williams_r_20d [🥈 高质量]
   描述: 20日威廉指标

   统计指标:
   - R²: 0.003507 (解释0.35%的收益率变异)
   - t值: 2.1513 (绝对值: 2.1513)
   - p值: 0.03163111 *
   - 相关系数: 0.059222
   - 方向准确率: 0.4935 (49.4%)
   - 有效观测数: 1317

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

56. 因子名称: kdj_j_14d [🥈 高质量]
   描述: 14日KDJ-J值

   统计指标:
   - R²: 0.003487 (解释0.35%的收益率变异)
   - t值: 2.1499 (绝对值: 2.1499)
   - p值: 0.03174418 *
   - 相关系数: 0.059048
   - 方向准确率: 0.4906 (49.1%)
   - 有效观测数: 1323

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

57. 因子名称: acceleration_15d [🥈 高质量]
   描述: 15日动量加速度

   统计指标:
   - R²: 0.003518 (解释0.35%的收益率变异)
   - t值: 2.1456 (绝对值: 2.1456)
   - p值: 0.03208673 *
   - 相关系数: 0.059313
   - 方向准确率: 0.5046 (50.5%)
   - 有效观测数: 1306

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

58. 因子名称: kdj_k_21d [🥈 高质量]
   描述: 21日KDJ-K值

   统计指标:
   - R²: 0.003343 (解释0.33%的收益率变异)
   - t值: 2.0994 (绝对值: 2.0994)
   - p值: 0.03597264 *
   - 相关系数: 0.057819
   - 方向准确率: 0.5038 (50.4%)
   - 有效观测数: 1316

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

59. 因子名称: trend_consistency_10d [🥈 高质量]
   描述: 10日趋势一致性

   统计指标:
   - R²: 0.003255 (解释0.33%的收益率变异)
   - t值: 2.0795 (绝对值: 2.0795)
   - p值: 0.03776378 *
   - 相关系数: 0.057057
   - 方向准确率: 0.2647 (26.5%)
   - 有效观测数: 1326

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

60. 因子名称: rsi_7d [🥈 高质量]
   描述: 7日RSI指标

   统计指标:
   - R²: 0.003165 (解释0.32%的收益率变异)
   - t值: 2.0533 (绝对值: 2.0533)
   - p值: 0.04023888 *
   - 相关系数: 0.056256
   - 方向准确率: 0.4805 (48.0%)
   - 有效观测数: 1330

   计算方式:
      RSI(7) = 100 - (100 / (1 + RS))
   其中RS = 7日平均涨幅 / 7日平均跌幅

   因子意义与作用:
      相对强弱指标：衡量超买超卖状态，>70超买，<30超卖

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

61. 因子名称: price_volume_corr_20d [🥈 高质量]
   描述: 20日价量相关性

   统计指标:
   - R²: 0.003175 (解释0.32%的收益率变异)
   - t值: 2.0459 (绝对值: 2.0459)
   - p值: 0.04096904 *
   - 相关系数: 0.056349
   - 方向准确率: 0.5023 (50.2%)
   - 有效观测数: 1316

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      成交量因子：反映市场参与度和资金流向变化

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

62. 因子名称: volume_weighted_momentum_10d [🥈 高质量]
   描述: 10日成交量加权动量

   统计指标:
   - R²: 0.003012 (解释0.30%的收益率变异)
   - t值: 1.9940 (绝对值: 1.9940)
   - p值: 0.04635282 *
   - 相关系数: 0.054885
   - 方向准确率: 0.4985 (49.8%)
   - 有效观测数: 1318

   计算方式:
      close / close.shift(weighte) - 1
   (当前收盘价 / weighte日前收盘价 - 1)

   因子意义与作用:
      动量因子：衡量价格趋势强度，正值表示上涨趋势，负值表示下跌趋势

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

63. 因子名称: efficiency_ratio_10d [🥈 高质量]
   描述: 10日价格效率比率

   统计指标:
   - R²: 0.002956 (解释0.30%的收益率变异)
   - t值: 1.9811 (绝对值: 1.9811)
   - p值: 0.04778736 *
   - 相关系数: 0.054365
   - 方向准确率: 0.5008 (50.1%)
   - 有效观测数: 1326

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------

64. 因子名称: kdj_j_9d [🥈 高质量]
   描述: 9日KDJ-J值

   统计指标:
   - R²: 0.002929 (解释0.29%的收益率变异)
   - t值: 1.9736 (绝对值: 1.9736)
   - p值: 0.04863204 *
   - 相关系数: 0.054120
   - 方向准确率: 0.5000 (50.0%)
   - 有效观测数: 1328

   计算方式:
      详细计算方式请参考AlphaFactorGenerator类中的相应方法

   因子意义与作用:
      复合因子：结合多种市场信息的综合指标

   机器学习适用性:
   ✅ 线性模型: 适用 (统计显著)
   ⚠️ 树模型: 谨慎使用 (解释能力较弱)
   ⚠️ 神经网络: 谨慎使用 (方向预测能力不足)

--------------------------------------------------------------------------------
