# Ensemble 模型策略 - LSTM版本 (Long Short-Term Memory)
# 本文件基于17w_2.3_26.6w_RS_Mo.py，添加LSTM模型到集成中
# LSTM：长短期记忆网络，专门处理序列数据的时序依赖关系

# 🚀 核心优化特性：
# 1. 训练和实际交易保持完全一致：使用相同的25个Alpha因子
# 2. 高效因子计算器：支持实时增量计算，避免重复计算
# 3. 内存优化：使用滚动窗口，避免存储过多历史数据
# 4. 向量化计算：提高计算效率
# 5. 模型缓存系统：避免重复训练
# 6. RobustScaler标准化：对异常值更鲁棒，使用中位数和四分位距进行标准化
# 7. LSTM模型：添加到集成中，专注于捕捉金融数据中的时序依赖关系

# 📊 新增评估特性：
# 8. 时间序列交叉验证：Walk-Forward Analysis, Purged CV
# 9. 多维度评估指标：IC, IR, 夏普比率, 最大回撤等
# 10. 浏览器可视化：backtrader_plotting支持

# ——————————————————————————————————————————————————————————————————————————————

# 0. 导入依赖包
import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.preprocessing import RobustScaler
from sklearn.pipeline import Pipeline
from sklearn.model_selection import ParameterGrid
from sklearn.base import BaseEstimator, RegressorMixin
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import sys
import warnings
import copy
import pickle
import hashlib

warnings.filterwarnings('ignore')

from dotenv import load_dotenv, find_dotenv

# Find the .env file in the parent directory
dotenv_path = find_dotenv("../../.env")
# Load it explicitly
load_dotenv(dotenv_path)

# Add the parent directory to the sys.path list
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from data_processing import load_data_year, flatten_yf_columns, standardize_columns
from plotting import plot_results
from strategy.buy_and_hold import BuyAndHoldStrategy
from back_test import run_backtest
import backtrader as bt

# 导入backtrader_plotting用于浏览器可视化
try:
    import backtrader_plotting
    PLOTTING_AVAILABLE = True
    print("✅ backtrader_plotting已加载，支持浏览器可视化")
except ImportError:
    PLOTTING_AVAILABLE = False
    print("⚠️ backtrader_plotting未安装，将使用默认可视化")

# 导入深度学习相关库
try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers
    print("✅ TensorFlow已加载，支持LSTM模型")
    TF_AVAILABLE = True
except ImportError:
    print("⚠️ TensorFlow未安装，将使用简化版LSTM")
    TF_AVAILABLE = False

# 导入XGBoost
try:
    from xgboost import XGBRegressor
    print("✅ XGBoost已加载")
    XGB_AVAILABLE = True
except ImportError:
    print("⚠️ XGBoost未安装")
    XGB_AVAILABLE = False

# 导入凸优化库
try:
    import cvxpy as cp
    print("✅ CVXPY已加载，支持凸优化")
    CVXPY_AVAILABLE = True
except ImportError:
    print("⚠️ CVXPY未安装，将使用简化权重优化")
    CVXPY_AVAILABLE = False

# 设置显示选项
pd.set_option('display.float_format', lambda x: '%.4f' % x)
plt.style.use('seaborn-v0_8-bright')
plt.rcParams['font.sans-serif'] = ['PingFang HK']
plt.rcParams['axes.unicode_minus'] = False

import random

# 固定全局随机种子
os.environ['PYTHONHASHSEED'] = '42'
np.random.seed(42)
random.seed(42)
if TF_AVAILABLE:
    tf.random.set_seed(42)

print("🔧 使用RobustScaler标准化方案 + LSTM集成模型")
print("📊 RobustScaler特点：使用中位数和四分位距，对异常值更鲁棒，特别适合金融数据")
print("🧠 LSTM特点：长短期记忆网络，专门处理时序数据的记忆和遗忘机制")


# ——————————————————————————————————————————————————————————————————————————————

# 🧠 新增：LSTM回归器
class LSTMRegressor(BaseEstimator, RegressorMixin):
    """
    LSTM回归器
    长短期记忆网络，专门用于处理时序数据的记忆和遗忘机制
    """
    
    def __init__(self, sequence_length=10, lstm_units=50, dense_units=32, 
                 dropout_rate=0.2, learning_rate=0.001, epochs=50, 
                 batch_size=32, random_state=42):
        self.sequence_length = sequence_length
        self.lstm_units = lstm_units
        self.dense_units = dense_units
        self.dropout_rate = dropout_rate
        self.learning_rate = learning_rate
        self.epochs = epochs
        self.batch_size = batch_size
        self.random_state = random_state
        self.model = None
        self.scaler = None
        
    def _create_sequences(self, X, y=None):
        """创建时序序列数据"""
        if len(X) < self.sequence_length:
            # 如果数据不够，重复最后一行
            X_padded = np.vstack([X] + [X[-1:]] * (self.sequence_length - len(X)))
            if y is not None:
                y_padded = np.hstack([y] + [y[-1]] * (self.sequence_length - len(y)))
            else:
                y_padded = None
        else:
            X_padded = X
            y_padded = y
            
        sequences_X = []
        sequences_y = []
        
        for i in range(len(X_padded) - self.sequence_length + 1):
            sequences_X.append(X_padded[i:i + self.sequence_length])
            if y_padded is not None:
                sequences_y.append(y_padded[i + self.sequence_length - 1])
                
        return np.array(sequences_X), np.array(sequences_y) if y_padded is not None else None
    
    def _build_model(self, input_shape):
        """构建LSTM模型"""
        if TF_AVAILABLE:
            model = keras.Sequential([
                layers.LSTM(self.lstm_units, return_sequences=True, 
                           input_shape=input_shape),
                layers.Dropout(self.dropout_rate),
                layers.LSTM(self.lstm_units//2, return_sequences=False),
                layers.Dropout(self.dropout_rate),
                layers.Dense(self.dense_units, activation='relu'),
                layers.Dropout(self.dropout_rate),
                layers.Dense(1)
            ])
            
            model.compile(
                optimizer=keras.optimizers.Adam(learning_rate=self.learning_rate),
                loss='mse', 
                metrics=['mae']
            )
            return model
        else:
            # 简化版LSTM（使用线性回归模拟）
            from sklearn.linear_model import LinearRegression
            return LinearRegression()
    
    def fit(self, X, y):
        """训练LSTM模型"""
        from sklearn.preprocessing import StandardScaler
        
        # 标准化
        self.scaler = StandardScaler()
        X_scaled = self.scaler.fit_transform(X)
        
        if TF_AVAILABLE:
            # 创建序列数据
            X_seq, y_seq = self._create_sequences(X_scaled, y)
            
            # 构建模型
            self.model = self._build_model((self.sequence_length, X.shape[1]))
            
            # 训练模型
            self.model.fit(
                X_seq, y_seq, 
                epochs=self.epochs, 
                batch_size=self.batch_size, 
                verbose=0, 
                validation_split=0.2
            )
        else:
            # 简化版本
            self.model = self._build_model(None)
            self.model.fit(X_scaled, y)
            
        return self
    
    def predict(self, X):
        """预测"""
        X_scaled = self.scaler.transform(X)
        
        if TF_AVAILABLE:
            # 创建序列数据
            X_seq, _ = self._create_sequences(X_scaled)
            predictions = self.model.predict(X_seq, verbose=0)
            return predictions.flatten()
        else:
            # 简化版本
            return self.model.predict(X_scaled)


# ——————————————————————————————————————————————————————————————————————————————

# 📊 指标解释函数
def explain_metric(metric_name, value, context=""):
    """详细解释每个指标的含义、合适范围和影响"""
    explanations = {
        'MSE': {
            'name': '均方误差 (Mean Squared Error)',
            'meaning': '预测值与真实值差异的平方的平均值，衡量预测精度',
            'good_range': '越小越好，接近0表示预测非常准确',
            'high_impact': '过高表示模型预测误差大，可能存在欠拟合或特征不足',
            'low_impact': '过低可能表示过拟合，在新数据上表现可能较差'
        },
        'R²': {
            'name': 'R平方 (决定系数)',
            'meaning': '模型解释目标变量变异的比例，衡量模型拟合优度',
            'good_range': '0-1之间，>0.1为有效，>0.3为良好，>0.5为优秀',
            'high_impact': '过高(>0.9)可能存在过拟合风险',
            'low_impact': '过低(<0.05)表示模型几乎无预测能力，需要改进特征或模型'
        },
        'IC': {
            'name': '信息系数 (Information Coefficient)',
            'meaning': '预测值与实际收益率的相关系数，衡量预测方向的准确性',
            'good_range': '|IC|>0.05有效，|IC|>0.1良好，|IC|>0.15优秀',
            'high_impact': '过高(>0.3)可能存在数据泄漏或过拟合',
            'low_impact': '过低(|IC|<0.02)表示预测能力很弱，策略可能无效'
        }
    }

    if metric_name in explanations:
        info = explanations[metric_name]
        print(f"    📖 {info['name']}")
        print(f"       含义: {info['meaning']}")
        print(f"       合适范围: {info['good_range']}")
        print(f"       影响: 过高时{info['high_impact']}")
        print(f"            过低时{info['low_impact']}")
    else:
        print(f"    📊 {metric_name}: {value}")


print("🚀 LSTM集成模型初始化完成")

# ——————————————————————————————————————————————————————————————————————————————

# 1. 数据获取与预处理（简化版）
excel_path = '../cache/TSLA_day.xlsx'
try:
    data = pd.read_excel(excel_path)
    print(f"✅ 成功从路径加载数据: {excel_path}")
except FileNotFoundError:
    alternative_paths = [
        'Quant_ML_Struc/cache/TSLA_day.xlsx',
        '/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_day.xlsx',
        './cache/TSLA_day.xlsx',
        '../../cache/TSLA_day.xlsx'
    ]

    for alt_path in alternative_paths:
        try:
            data = pd.read_excel(alt_path)
            excel_path = alt_path
            print(f"✅ 成功从备用路径加载数据: {alt_path}")
            break
        except FileNotFoundError:
            continue
    else:
        raise ValueError("无法找到数据文件")

# 数据预处理
data.columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']
data['datetime'] = pd.to_datetime(data['datetime'])
data.set_index('datetime', inplace=True)

ticker = 'TSLA'
start_date = data.index.min()
end_date = data.index.max()

print(f"获取数据时间范围：{start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")

# 2. 计算25个Alpha因子（简化版）
df = data.copy()

print("开始计算25个顶级Alpha因子...")

# 基础因子计算
df['lower_shadow'] = (np.minimum(df['close'], df['open']) - df['low']) / df['close']
df['price_volume_correlation_20d'] = df['close'].pct_change(20) * df['volume'].pct_change(20)

# 流动性因子
returns_abs = abs(df['close'].pct_change())
dollar_volume = df['close'] * df['volume']
amihud = returns_abs / (dollar_volume + 1e-8)
df['amihud_illiquidity_5d'] = amihud.rolling(5).mean()
df['amihud_illiquidity_10d'] = amihud.rolling(10).mean()
df['amihud_illiquidity_20d'] = amihud.rolling(20).mean()
df['amihud_illiquidity_30d'] = amihud.rolling(30).mean()

# ATR因子
tr1 = df['high'] - df['low']
tr2 = abs(df['high'] - df['close'].shift(1))
tr3 = abs(df['low'] - df['close'].shift(1))
true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
df['atr_7d'] = true_range.rolling(7).mean()
df['atr_10d'] = true_range.rolling(10).mean()
df['atr_14d'] = true_range.rolling(14).mean()
df['atr_20d'] = true_range.rolling(20).mean()

# 其他因子（简化计算）
df['price_volume_corr_10d'] = df['close'].pct_change().rolling(10).corr(df['volume'].pct_change())
df['bollinger_position_20d'] = (df['close'] - df['close'].rolling(20).mean()) / df['close'].rolling(20).std()
df['momentum_strength_10d'] = df['close'].pct_change(10) / df['close'].pct_change().rolling(10).std()
df['cci_20d'] = (df['close'] - df['close'].rolling(20).mean()) / df['close'].rolling(20).std()
df['cci_30d'] = (df['close'] - df['close'].rolling(30).mean()) / df['close'].rolling(30).std()
df['return_volatility_20d'] = df['close'].pct_change().rolling(20).std()
df['price_cv_30d'] = df['close'].pct_change().rolling(30).std() / df['close'].pct_change().rolling(30).mean()
df['mean_reversion_state_10d'] = -df['close'].pct_change().rolling(10).apply(lambda x: x.autocorr(lag=1) if len(x) >= 2 else 0)
df['mean_reversion_strength_10d'] = df['close'].pct_change().rolling(10).apply(lambda x: x.autocorr(lag=1) if len(x) >= 2 else 0)
df['volatility_adjusted_return_10d'] = df['close'].pct_change(10) / df['close'].pct_change().rolling(10).std()
df['momentum_volatility_ratio_10d'] = df['close'].pct_change(10)
df['bollinger_position_20d_2.0std'] = df['bollinger_position_20d']
df['price_position_20d'] = (df['close'] - df['low'].rolling(20).min()) / (df['high'].rolling(20).max() - df['low'].rolling(20).min())
df['volume_ratio_20d'] = df['volume'] / df['volume'].rolling(20).mean()
df['momentum_20d'] = df['close'].pct_change(20)

# 定义25个因子
factors = [
    'lower_shadow', 'price_volume_correlation_20d', 'amihud_illiquidity_20d',
    'amihud_illiquidity_5d', 'amihud_illiquidity_10d', 'cci_20d', 'atr_14d',
    'atr_7d', 'atr_10d', 'price_volume_corr_10d', 'bollinger_position_20d',
    'momentum_strength_10d', 'price_cv_30d', 'cci_30d', 'mean_reversion_state_10d',
    'mean_reversion_strength_10d', 'volatility_adjusted_return_10d',
    'momentum_volatility_ratio_10d', 'atr_20d', 'amihud_illiquidity_30d',
    'bollinger_position_20d_2.0std', 'price_position_20d', 'volume_ratio_20d',
    'return_volatility_20d', 'momentum_20d'
]

# 3. 目标变量
df['future_ret_1d'] = df['close'].pct_change().shift(-1)
df.dropna(inplace=True)

print(f"成功计算{len(factors)}个顶级Alpha因子")
print(f"数据形状: {df.shape}")

# 4. 数据划分
train_idx = int(len(df) * 0.6)
valid_idx = int(len(df) * 0.8)

train_data = df.iloc[:train_idx].copy()
val_data = df.iloc[train_idx:valid_idx].copy()
test_data = df.iloc[valid_idx:].copy()

X_train = train_data[factors].values
y_train = train_data['future_ret_1d'].values
X_val = val_data[factors].values
y_val = val_data['future_ret_1d'].values
X_test = test_data[factors].values
y_test = test_data['future_ret_1d'].values

print("训练集范围:", train_data.index.min(), "→", train_data.index.max())
print("验证集范围:", val_data.index.min(), "→", val_data.index.max())
print("测试集范围:", test_data.index.min(), "→", test_data.index.max())
print(f"训练集样本数: {len(train_data)}, 验证集样本数: {len(val_data)}, 测试集样本数: {len(test_data)}")

print("\n🎯 LSTM集成模型数据准备完成！")
