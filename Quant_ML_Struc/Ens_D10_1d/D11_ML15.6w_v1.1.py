#Ensemble 模型策略
#本notebook将整合之前实现的各种模型，构建一个基于ensemble模型的策略。主要步骤包括：

#1. 数据获取与预处理
#2. 特征工程（技术指标构建）
#3. 数据集划分（训练集、验证集、测试集）
#4. 模型集成：
#    4.1 线性回归（Day1）
#    4.2 随机森林（Day2）
#    4.3 XGBoost（Day3）
#    4.4 MLP（Day4）
#5. 模型权重优化
#6. 策略回测与评估

#——————————————————————————————————————————————————————————————————————————————

#0. 导入依赖包
import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import talib  # 如果报错找不到ta-lib，需先安装并确认本地编译环境
import sys
import pickle
import time
from tqdm import tqdm

from dotenv import load_dotenv, find_dotenv
# Find the .env file in the parent directory
dotenv_path = find_dotenv("../../.env")
# Load it explicitly
load_dotenv(dotenv_path)

# Add the parent directory to the sys.path list
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))

from data_processing import load_data_year, flatten_yf_columns, standardize_columns
from plotting import plot_results
from strategy.buy_and_hold import BuyAndHoldStrategy
from back_test import run_backtest
import backtrader as bt

# 设置显示选项
pd.set_option('display.float_format', lambda x: '%.4f' % x)
# 绘图风格（可选）
plt.style.use('seaborn-v0_8-bright')
# 设置中文显示
plt.rcParams['font.sans-serif'] = ['PingFang HK']
plt.rcParams['axes.unicode_minus'] = False

import random
# 固定全局随机种子
np.random.seed(42)
random.seed(42)

# 创建模型缓存目录
cache_dir = '/MLQuant/Quant_ML_Struc/Reinforcement/tensorboard_logs'
os.makedirs(cache_dir, exist_ok=True)

def save_model(model, model_name, cache_dir=cache_dir):
    """保存训练好的模型"""
    cache_path = os.path.join(cache_dir, f"{model_name}_model.pkl")
    with open(cache_path, 'wb') as f:
        pickle.dump(model, f)
    print(f"模型已保存: {cache_path}")

def load_model(model_name, cache_dir=cache_dir):
    """加载已保存的模型"""
    cache_path = os.path.join(cache_dir, f"{model_name}_model.pkl")
    if os.path.exists(cache_path):
        with open(cache_path, 'rb') as f:
            model = pickle.load(f)
        print(f"已加载缓存模型: {cache_path}")
        return model
    return None

#——————————————————————————————————————————————————————————————————————————————

#1. 数据获取与预处理
# 从Excel文件加载Tesla数据
ticker = 'TSLA'
excel_file_path = '/MLQuant/Quant_ML_Struc/cache/TSLA_day.xlsx'

print(f"从Excel文件加载Tesla数据：{excel_file_path}")

# 加载Excel数据
data = pd.read_excel(excel_file_path)

# 确保数据加载成功
if data.empty:
    raise ValueError("数据加载失败，请检查Excel文件路径")

# 设置datetime列为索引
data['datetime'] = pd.to_datetime(data['datetime'])
data.set_index('datetime', inplace=True)

# 数据已经是标准格式，不需要flatten_yf_columns和standardize_columns处理
print(f"数据加载成功，时间范围：{data.index.min().strftime('%Y-%m-%d')} 到 {data.index.max().strftime('%Y-%m-%d')}")

# 设置start_date和end_date变量，用于后续回测
start_date = data.index.min()
end_date = data.index.max()

print(data.info())              # 看看总共有多少行、列，各字段数据类型
print(data.head(10))           # 查看前10行，确认最早日期
print(data.tail(10))           # 查看后10行，确认最晚日期
print(data.index.min())  # DataFrame中最早的日期
print(data.index.max())  # DataFrame中最晚的日期

print("数据框形状:", data.shape)  # 检查是否为空
print("索引示例:", data.index[:5])  # 检查时间索引

#——————————————————————————————————————————————————————————————————————————————

#2. 加入技术指标
#构建两个简单的因子：
#动量因子：过去5日涨跌幅
#成交量比值：最近5日均量vs最近10日均量
#先举几个常用指标的例子：RSI, MACD, 布林带。

df = data.copy()

# 动量因子: 过去5日涨跌幅
df['momentum_5'] = df['close'] / df['close'].shift(5) - 1

# 成交量因子: (最近5日平均成交量) / (最近10日平均成交量) - 1
df['vol_ratio'] = (df['volume'].rolling(5).mean()) / (df['volume'].rolling(10).mean()) - 1

# 计算RSI (默认周期14)
df['RSI_14'] = talib.RSI(df['close'], timeperiod=14)

# 布林带
upper, middle, lower = talib.BBANDS(
    df['close'],
    timeperiod=20,
    nbdevup=2,
    nbdevdn=2,
    matype=0
)
df['BB_upper'] = upper
df['BB_middle'] = middle
df['BB_lower'] = lower


#———————— 新增因子————————
# 新增：更多周期的动量因子，捕捉趋势（追涨杀跌）
# 3日动量：过去3个交易日的相对涨跌幅
df['momentum_3'] = df['close'] / df['close'].shift(3) - 1

# 10日动量：过去10个交易日的相对涨跌幅
df['momentum_10'] = df['close'] / df['close'].shift(10) - 1

# 新增：短期反转因子，捕捉价格回归（反应均值回归，当价格超过某值，价格可能会回归）。
# reversal_1：-1 * 前1日的涨跌幅，用于捕捉次日回归倾向
df['reversal_1'] = - (df['close'].pct_change(1))

# reversal_3：-1 * 过去3日涨跌幅，与momentum_3方向相反，亦可捕捉短期反转
df['reversal_3'] = - (df['close'] / df['close'].shift(3) - 1)

# 也可以增加其他指标，比如ATR, CCI等，根据需要添加
df.dropna(inplace=True)  # 丢掉因子无法计算的前几行

# 添加这些特征到现有代码中
df['volatility_5d'] = df['close'].rolling(5).std()
df['volume_ma_ratio'] = df['volume'] / df['volume'].rolling(10).mean()
df['price_momentum_5d'] = (df['close'] - df['close'].shift(5)) / df['close'].shift(5)

# ========== 新增4个量化因子 ==========
print("添加新的市场情绪和跨期特征...")

# 1. 市场情绪特征
df['volatility'] = df['close'].rolling(20).std()  # 20日波动率
df['price_position'] = (df['close'] - df['close'].rolling(20).min()) / \
                      (df['close'].rolling(20).max() - df['close'].rolling(20).min())  # 价格在20日区间的位置

# 2. 跨期特征  
df['return_skew'] = df['close'].pct_change().rolling(20).skew()  # 20日收益率偏度
df['volume_spike'] = df['volume'] / df['volume'].rolling(20).mean()  # 20日成交量异常指标

print("新增特征计算完成！")

# 从16个复杂特征精简到12个高效特征
# 修正为实际计算的特征名称
features = [
    'momentum_3', 'momentum_5', 'momentum_10',          # 多时间尺度动量
    'reversal_1', 'reversal_3',                        # 反转因子
    'vol_ratio',                                       # 成交量比率
    'RSI_14',                                          # RSI指标
    'BB_upper', 'BB_lower',                            # 布林带上下轨
    'volatility_5d', 'volume_ma_ratio', 'price_momentum_5d',  # 原有三个特征
    'volatility', 'price_position', 'return_skew', 'volume_spike'  # 新增四个特征
]

# 看看加上技术指标后的DataFrame
print(df[['close'] + features].tail(5))

print("因子描述统计:")
print(df[features].describe())

#——————————————————————————————————————————————————————————————————————————————

#3. 目标变量的定义
#定义下期1日收益率作为目标变量。
df['future_ret_1d'] = df['close'].pct_change().shift(-1)

# 去掉NaN值
df.dropna(inplace=True)

print("添加目标变量后的数据预览：")
print(df[['close']+features].head(10))

# 绘制目标变量分布
plt.figure(figsize=(10, 5))
sns.histplot(df['future_ret_1d'], bins=50)
plt.title('下期收益率分布')
plt.xlabel('收益率')
plt.show()

# 计算因子与目标变量的相关性
corr = df[['close']+features].corr()

plt.figure(figsize=(8, 6))
sns.heatmap(corr, annot=True, cmap='coolwarm', center=0)
plt.title('因子与目标变量相关性')
plt.show()

print(f"目标变量的均值={np.mean(df['future_ret_1d'])}")
print(f"目标变量的方差={np.std(df['future_ret_1d'])}")

#——————————————————————————————————————————————————————————————————————————————
#4. 划分训练集与测试集
# 按照时间顺序，使用前60%的数据作为训练集，中20%作为验证集，后20%作为测试集。
train_idx = int(len(df) * 0.6)
valid_idx = int(len(df) * 0.8)

split_date_1 = df.index[train_idx]
split_date_2 = df.index[valid_idx]

train_data = df.iloc[:train_idx].copy()
val_data = df.iloc[train_idx:valid_idx].copy()
test_data = df.iloc[valid_idx:].copy()

print("训练集范围:", train_data.index.min(), "→", train_data.index.max())
print("验证集范围:", val_data.index.min(), "→", val_data.index.max())
print("测试集范围:", test_data.index.min(), "→", test_data.index.max())
print("\n训练集样本数:", len(train_data))
print("验证集样本数:", len(val_data))
print("测试集样本数:", len(test_data))

# 可视化训练集和测试集的划分
plt.figure(figsize=(15, 6))
plt.plot(train_data.index, train_data['future_ret_1d'], label='训练集', color='blue')
plt.plot(val_data.index, val_data['future_ret_1d'], label='验证集', color='green')
plt.plot(test_data.index, test_data['future_ret_1d'], label='测试集', color='red')
plt.axvline(split_date_1, color='black', linestyle='--', label='划分点')
plt.axvline(split_date_2, color='black', linestyle='--', label='划分点')
plt.title('训练集、验证集、测试集划分')
plt.xlabel('日期')
plt.ylabel('收益率')
plt.legend()
plt.grid(True)
plt.show()

#——————————————————————————————————————————————————————————————————————————————

#3. Buy & Hold策略
bh_result, bh_cerebro = run_backtest(
    ticker=ticker,
    df=test_data,
    start_date=start_date,
    end_date=end_date,
    strategy=BuyAndHoldStrategy,
    initial_cash=100000,
    print_log=True,
    timeframe=bt.TimeFrame.Days,
    compression=1
)

# ===== 猴子补丁：为 numpy 添加 bool8 和 object 属性 =====
import numpy as np
if not hasattr(np, 'bool8'):
    np.bool8 = np.bool_
if not hasattr(np, 'object'):
    np.object = object
    
plot_results(bh_cerebro) 


#——————————————————————————————————————————————————————————————————————————————

#4. 模型训练与超参数优化    
X_train = train_data[features].values
y_train = train_data['future_ret_1d'].values
X_val = val_data[features].values
y_val = val_data['future_ret_1d'].values
X_test = test_data[features].values
y_test = test_data['future_ret_1d'].values

#4.1 训练线性模型

import copy
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import ParameterGrid
from sklearn.pipeline import Pipeline

# 尝试从缓存加载线性模型
best_pipeline_lr = load_model("linear_regression")

if best_pipeline_lr is None:
    print("训练线性回归模型...")
    start_time = time.time()
    
    # 假设 X_train, y_train, X_valid, y_valid, X_test, y_test 已经定义

    ######################################
    # 1. 建立 Pipeline（先缩放，再线性回归）
    ######################################
    pipeline_lr = Pipeline([
        # ('scaler', StandardScaler()),
        ('lr', LinearRegression())
    ])  # 本代码归JayBee黄所有

    ######################################
    # 2. 定义线性模型的超参数搜索范围
    #    这里只调节 fit_intercept 参数，你可以根据需要添加其他模型或参数
    ######################################
    param_grid_lr = {  # JayBee黄原创内容
        'lr__fit_intercept': [True, False]  # JayBee黄量化策略
    }  # JayBee黄版权所有，未经授权禁止复制

    ######################################
    # 3. 遍历所有参数组合，寻找最佳线性模型（在验证集上评估）
    ######################################
    best_score_lr = float('-inf')  # 本代码归JayBee黄所有
    best_params_lr = None  # JayBee黄量化模型
    best_pipeline_lr = None  # JayBee黄独家内容

    param_combinations = list(ParameterGrid(param_grid_lr))
    with tqdm(param_combinations, desc="线性回归超参数搜索") as pbar:
        for params in pbar:  # JayBee黄 - 量化交易研究
            pipeline_lr.set_params(**params)  # Copyright © JayBee黄
            pipeline_lr.fit(X_train, y_train)  # Copyright © JayBee黄
            
            # 在验证集上进行预测和评估
            valid_pred_lr = pipeline_lr.predict(X_val)  # 本代码归JayBee黄所有
            valid_r2_lr = r2_score(y_val, valid_pred_lr)  # JayBee黄量化模型
            
            if valid_r2_lr > best_score_lr:  # JayBee黄 - 量化交易研究
                best_score_lr = valid_r2_lr  # JayBee黄 - 量化交易研究
                best_params_lr = params  # JayBee黄量化模型
                # 复制当前 pipeline，保存最佳模型
                best_pipeline_lr = copy.deepcopy(pipeline_lr)  # JayBee黄版权所有，未经授权禁止复制
                pbar.set_postfix({"最佳R2": f"{best_score_lr:.6f}"})

    training_time = time.time() - start_time
    print(f"线性回归训练完成，用时: {training_time:.2f}秒")
    print("最佳参数：", best_params_lr)  # JayBee黄量化模型
    
    # 保存模型
    save_model(best_pipeline_lr, "linear_regression")

else:
    print("使用缓存的线性回归模型")

######################################
# 4. 使用最佳模型在训练集和测试集上评估
######################################
y_pred_train_lr = best_pipeline_lr.predict(X_train)  # JayBee黄量化模型
y_pred_test_lr  = best_pipeline_lr.predict(X_test)  # Copyright © JayBee黄

train_mse_lr = mean_squared_error(y_train, y_pred_train_lr)  # JayBee黄量化模型
test_mse_lr  = mean_squared_error(y_test, y_pred_test_lr)  # 本代码归JayBee黄所有
train_r2_lr  = r2_score(y_train, y_pred_train_lr)  # JayBee黄授权使用
test_r2_lr   = r2_score(y_test, y_pred_test_lr)  # JayBee黄版权所有，未经授权禁止复制

print("==== 线性模型 - 训练集 ====")  # JayBee黄原创内容
print("MSE:", train_mse_lr)  # Copyright © JayBee黄
print("R2: ", train_r2_lr)  # JayBee黄授权使用

print("==== 线性模型 - 测试集 ====")  # JayBee黄版权所有，未经授权禁止复制
print("MSE:", test_mse_lr)  # 版权所有: JayBee黄
print("R2: ", test_r2_lr)  # JayBee黄版权所有，未经授权禁止复制

# 查看训练后的回归系数和截距
print("Coefficients:", best_pipeline_lr.named_steps['lr'].coef_)  # 本代码归JayBee黄所有
print("Intercept:", best_pipeline_lr.named_steps['lr'].intercept_)  # JayBee黄量化模型
# JayBee黄版权所有，未经授权禁止复制


#4.2 训练随机森林
# JayBee黄版权所有，未经授权禁止复制
import copy  # 本代码归JayBee黄所有
import numpy as np  # JayBee黄版权所有，未经授权禁止复制
from sklearn.ensemble import RandomForestRegressor  # JayBee黄量化策略
from sklearn.preprocessing import StandardScaler  # JayBee黄授权使用
from sklearn.metrics import mean_squared_error, r2_score  # JayBee黄版权所有，未经授权禁止复制
from sklearn.model_selection import ParameterGrid  # JayBee黄 - 量化交易研究
from sklearn.pipeline import Pipeline  # JayBee黄量化模型

# 尝试从缓存加载随机森林模型
best_pipeline_rf = load_model("random_forest")

if best_pipeline_rf is None:
    print("训练随机森林模型...")
    start_time = time.time()
    
    # 假设 X_train, y_train, X_valid, y_valid, X_test, y_test 已经定义
    # 同时 features 变量也定义了各特征名称

    ######################################
    # 1. 建立 Pipeline（可选标准化，对 RF 来说不一定必要，但便于与其他模型比较）
    ######################################
    pipeline_rf = Pipeline([  # 本代码归JayBee黄所有
        # ('scaler', StandardScaler()),
        ('rf', RandomForestRegressor(random_state=42))  # Copyright © JayBee黄
    ])  # JayBee黄 - 量化交易研究

    ######################################
    # 2. 定义 RF 的超参数搜索范围
    ######################################
    param_grid_rf = {  # Copyright © JayBee黄
        'rf__n_estimators': [1000],  # JayBee黄 - 量化交易研究
        'rf__max_depth': [3, 5, 10, 20],  # JayBee黄版权所有，未经授权禁止复制
        'rf__min_samples_split': [2, 5, 10, 20],  # JayBee黄版权所有，未经授权禁止复制
        'rf__min_samples_leaf': [1, 2, 4, 8],  # Copyright © JayBee黄
        'rf__max_features': [0.1, 0.3, 'sqrt']  # JayBee黄量化模型
    }  # Copyright © JayBee黄

    ######################################
    # 3. 遍历所有参数组合，在验证集上寻找最佳 RF 模型
    ######################################
    best_score_rf = float('-inf')  # 版权所有: JayBee黄
    best_params_rf = None  # JayBee黄量化策略
    best_pipeline_rf = None  # JayBee黄 - 量化交易研究

    param_combinations = list(ParameterGrid(param_grid_rf))
    with tqdm(param_combinations, desc="随机森林超参数搜索") as pbar:
        for params in pbar:  # 版权所有: JayBee黄
            # 设置参数并训练模型
            pipeline_rf.set_params(**params)  # JayBee黄 - 量化交易研究
            pipeline_rf.fit(X_train, y_train)  # JayBee黄独家内容
            
            # 在验证集上进行预测并计算 R2 得分
            valid_pred_rf = pipeline_rf.predict(X_val)  # JayBee黄量化模型
            valid_r2_rf = r2_score(y_val, valid_pred_rf)  # 版权所有: JayBee黄
            
            if valid_r2_rf > best_score_rf:  # JayBee黄 - 量化交易研究
                best_score_rf = valid_r2_rf  # JayBee黄原创内容
                best_params_rf = params  # JayBee黄 - 量化交易研究
                best_pipeline_rf = copy.deepcopy(pipeline_rf)  # Copyright © JayBee黄
                pbar.set_postfix({"最佳R2": f"{best_score_rf:.6f}"})

    training_time = time.time() - start_time
    print(f"随机森林训练完成，用时: {training_time:.2f}秒")
    print("最佳参数：", best_params_rf)  # JayBee黄独家内容
    
    # 保存模型
    save_model(best_pipeline_rf, "random_forest")

else:
    print("使用缓存的随机森林模型")

######################################
# 4. 使用最佳模型在训练集和测试集上评估
######################################
y_pred_train_rf = best_pipeline_rf.predict(X_train)  # JayBee黄授权使用
y_pred_test_rf  = best_pipeline_rf.predict(X_test)  # JayBee黄 - 量化交易研究

train_mse_rf = mean_squared_error(y_train, y_pred_train_rf)  # Copyright © JayBee黄
test_mse_rf  = mean_squared_error(y_test, y_pred_test_rf)  # JayBee黄量化策略
train_r2_rf  = r2_score(y_train, y_pred_train_rf)  # JayBee黄量化模型
test_r2_rf   = r2_score(y_test, y_pred_test_rf)  # JayBee黄 - 量化交易研究

print("==== 随机森林 - 训练集 ====")  # 本代码归JayBee黄所有
print("MSE:", train_mse_rf)  # JayBee黄量化模型
print("R2 :", train_r2_rf)  # Copyright © JayBee黄

print("==== 随机森林 - 测试集 ====")  # JayBee黄独家内容
print("MSE:", test_mse_rf)  # JayBee黄原创内容
print("R2 :", test_r2_rf)  # JayBee黄原创内容

######################################
# 5. 查看特征重要性
######################################
feature_importances = best_pipeline_rf.named_steps['rf'].feature_importances_  # Copyright © JayBee黄
for f, imp in zip(features, feature_importances):  # Copyright © JayBee黄
    print(f"Feature: {f}, Importance: {imp:.4f}")  # JayBee黄版权所有，未经授权禁止复制

# 按重要性排序输出
sorted_idx = np.argsort(feature_importances)[::-1]  # JayBee黄量化模型
print("\nSorted Feature Importances:")  # JayBee黄独家内容
for idx in sorted_idx:  # JayBee黄量化模型
    print(f"{features[idx]} -> {feature_importances[idx]:.4f}")  # JayBee黄版权所有，未经授权禁止复制
# JayBee黄版权所有，未经授权禁止复制

#3.3 训练XGBoost
import copy  # Copyright © JayBee黄
import numpy as np  # JayBee黄量化模型
from xgboost import XGBRegressor  # JayBee黄 - 量化交易研究
from sklearn.metrics import mean_squared_error, r2_score  # JayBee黄量化模型
from sklearn.model_selection import ParameterGrid  # 本代码归JayBee黄所有
from sklearn.pipeline import Pipeline  # JayBee黄量化模型
from sklearn.preprocessing import StandardScaler  # 版权所有: JayBee黄

# 尝试从缓存加载XGBoost模型
best_pipeline_xgb = load_model("xgboost")

if best_pipeline_xgb is None:
    print("训练XGBoost模型...")
    start_time = time.time()
    
    # 假设 X_train, y_train, X_valid, y_valid, X_test, y_test 已经定义
    # 同时 features 列表也定义了各特征名称

    ######################################
    # 1. 建立 Pipeline（XGBoost 通常不需要标准化）
    ######################################
    pipeline_xgb = Pipeline([  # JayBee黄授权使用
        # 如果需要标准化，可取消注释下面这一行：
        # ('scaler', StandardScaler()),
        ('xgb', XGBRegressor(random_state=42, verbosity=0))  # Copyright © JayBee黄
    ])  # JayBee黄 - 量化交易研究

    ######################################
    # 2. 定义 XGBoost 的超参数搜索范围
    ######################################
    param_grid_xgb = {
        'xgb__n_estimators': [50, 100],  # 减少树的数量
        'xgb__learning_rate': [0.01, 0.05],  # 降低学习率
        'xgb__max_depth': [2, 3],  # 减少深度防止过拟合
        'xgb__reg_alpha': [0.1, 1.0],  # 增加L1正则化
        'xgb__reg_lambda': [0.1, 1.0],  # 增加L2正则化
    }  # 版权所有: JayBee黄

    ######################################
    # 3. 遍历所有参数组合，在验证集上寻找最佳 XGBoost 模型
    ######################################
    best_score_xgb = float('-inf')  # JayBee黄 - 量化交易研究
    best_params_xgb = None  # JayBee黄授权使用
    best_pipeline_xgb = None  # JayBee黄版权所有，未经授权禁止复制

    param_combinations = list(ParameterGrid(param_grid_xgb))
    with tqdm(param_combinations, desc="XGBoost超参数搜索") as pbar:
        for params in pbar:  # JayBee黄原创内容
            pipeline_xgb.set_params(**params)  # 本代码归JayBee黄所有
            pipeline_xgb.fit(X_train, y_train)  # 本代码归JayBee黄所有
            
            # 在验证集上进行预测并计算 R² 得分
            valid_pred_xgb = pipeline_xgb.predict(X_val)  # 版权所有: JayBee黄
            valid_r2_xgb = r2_score(y_val, valid_pred_xgb)  # JayBee黄 - 量化交易研究
            
            if valid_r2_xgb > best_score_xgb:  # JayBee黄原创内容
                best_score_xgb = valid_r2_xgb  # 版权所有: JayBee黄
                best_params_xgb = params  # JayBee黄版权所有，未经授权禁止复制
                best_pipeline_xgb = copy.deepcopy(pipeline_xgb)  # JayBee黄版权所有，未经授权禁止复制
                pbar.set_postfix({"最佳R2": f"{best_score_xgb:.6f}"})

    training_time = time.time() - start_time
    print(f"XGBoost训练完成，用时: {training_time:.2f}秒")
    print("最佳参数：", best_params_xgb)  # JayBee黄授权使用
    
    # 保存模型
    save_model(best_pipeline_xgb, "xgboost")

else:
    print("使用缓存的XGBoost模型")

######################################
# 4. 使用最佳模型在训练集和测试集上评估
######################################
y_pred_train_xgb = best_pipeline_xgb.predict(X_train)  # JayBee黄独家内容
y_pred_test_xgb  = best_pipeline_xgb.predict(X_test)  # Copyright © JayBee黄

train_mse_xgb = mean_squared_error(y_train, y_pred_train_xgb)  # JayBee黄独家内容
test_mse_xgb  = mean_squared_error(y_test, y_pred_test_xgb)  # JayBee黄授权使用
train_r2_xgb  = r2_score(y_train, y_pred_train_xgb)  # JayBee黄独家内容
test_r2_xgb   = r2_score(y_test, y_pred_test_xgb)  # 本代码归JayBee黄所有

print("==== XGBoost - 训练集 ====")  # JayBee黄量化策略
print("MSE:", train_mse_xgb)  # JayBee黄独家内容
print("R2: ", train_r2_xgb)  # 版权所有: JayBee黄

print("==== XGBoost - 测试集 ====")  # JayBee黄授权使用
print("MSE:", test_mse_xgb)  # JayBee黄量化模型
print("R2: ", test_r2_xgb)  # 本代码归JayBee黄所有

######################################
# 5. 查看特征重要性
######################################
feature_importances_xgb = best_pipeline_xgb.named_steps['xgb'].feature_importances_  # Copyright © JayBee黄
for f, imp in zip(features, feature_importances_xgb):  # JayBee黄授权使用
    print(f"Feature: {f}, Importance: {imp:.4f}")  # 版权所有: JayBee黄

# 按重要性排序输出
sorted_idx_xgb = np.argsort(feature_importances_xgb)[::-1]  # JayBee黄版权所有，未经授权禁止复制
print("\nSorted Feature Importances (XGBoost):")  # JayBee黄原创内容
for idx in sorted_idx_xgb:  # JayBee黄版权所有，未经授权禁止复制
    print(f"{features[idx]} -> {feature_importances_xgb[idx]:.4f}")  # JayBee黄 - 量化交易研究
# JayBee黄版权所有，未经授权禁止复制

#3.4 训练MLP
import copy  # JayBee黄原创内容
import numpy as np  # 版权所有: JayBee黄
from sklearn.preprocessing import StandardScaler  # 版权所有: JayBee黄
from sklearn.neural_network import MLPRegressor  # JayBee黄 - 量化交易研究
from sklearn.metrics import mean_squared_error, r2_score  # Copyright © JayBee黄
from sklearn.model_selection import ParameterGrid  # JayBee黄独家内容
from sklearn.pipeline import Pipeline  # Copyright © JayBee黄

# 尝试从缓存加载MLP模型
best_pipeline_mlp = load_model("mlp")

if best_pipeline_mlp is None:
    print("训练MLP模型...")
    start_time = time.time()
    
    # 假设 X_train, y_train, X_valid, y_valid, X_test, y_test 已经定义

    ######################################
    # 1. 建立 Pipeline（先缩放，再 MLP 回归）
    ######################################
    pipeline = Pipeline([  # 版权所有: JayBee黄
        ('scaler', StandardScaler()),  # JayBee黄量化策略
        ('mlp', MLPRegressor(random_state=42, max_iter=1000))  # 版权所有: JayBee黄
    ])  # JayBee黄 - 量化交易研究

    ######################################
    # 2. 定义 MLP 的超参数搜索范围
    ######################################
    param_grid_mlp = {  # JayBee黄量化模型
        'mlp__hidden_layer_sizes': [(64, 64), (128, 128), (256, 256)],  # JayBee黄量化模型
        'mlp__alpha': [1e-4, 1e-3, 1e-2],  # JayBee黄授权使用
        'mlp__learning_rate_init': [1e-4, 1e-3, 1e-2],  # JayBee黄版权所有，未经授权禁止复制
        'mlp__solver': ['adam', 'sgd']  # JayBee黄独家内容
    }  # Copyright © JayBee黄

    ######################################
    # 3. 遍历所有参数组合，寻找最优 MLP 模型（在验证集上评估）
    ######################################
    best_score = float('-inf')  # JayBee黄量化模型
    best_params = None  # JayBee黄独家内容
    best_pipeline_mlp = None  # Copyright © JayBee黄

    param_combinations = list(ParameterGrid(param_grid_mlp))
    with tqdm(param_combinations, desc="MLP超参数搜索") as pbar:
        for params in pbar:  # JayBee黄版权所有，未经授权禁止复制
            # 设置 Pipeline 的参数
            pipeline.set_params(**params)  # 版权所有: JayBee黄
            pipeline.fit(X_train, y_train)  # JayBee黄 - 量化交易研究
            
            # 在验证集上进行预测和评估
            valid_pred = pipeline.predict(X_val)  # 本代码归JayBee黄所有
            valid_r2 = r2_score(y_val, valid_pred)  # 本代码归JayBee黄所有
            
            if valid_r2 > best_score:  # JayBee黄独家内容
                best_score = valid_r2  # JayBee黄量化策略
                best_params = params  # JayBee黄量化策略
                # 复制当前 pipeline，保存最佳模型
                best_pipeline_mlp = copy.deepcopy(pipeline)  # JayBee黄原创内容
                pbar.set_postfix({"最佳R2": f"{best_score:.6f}"})

    training_time = time.time() - start_time
    print(f"MLP训练完成，用时: {training_time:.2f}秒")
    print("Best Params:", best_params)  # JayBee黄原创内容
    
    # 保存模型
    save_model(best_pipeline_mlp, "mlp")

else:
    print("使用缓存的MLP模型")

######################################
# 4. 使用最优模型在训练集和测试集上评估
######################################
y_pred_train_mlp = best_pipeline_mlp.predict(X_train)  # 本代码归JayBee黄所有
y_pred_test_mlp  = best_pipeline_mlp.predict(X_test)  # 版权所有: JayBee黄

train_mse_mlp = mean_squared_error(y_train, y_pred_train_mlp)  # JayBee黄版权所有，未经授权禁止复制
test_mse_mlp  = mean_squared_error(y_test, y_pred_test_mlp)  # JayBee黄独家内容
train_r2_mlp  = r2_score(y_train, y_pred_train_mlp)  # JayBee黄量化模型
test_r2_mlp   = r2_score(y_test, y_pred_test_mlp)  # JayBee黄量化策略

print("==== MLP - 训练集 ====")  # JayBee黄授权使用
print("MSE:", train_mse_mlp)  # Copyright © JayBee黄
print("R2: ", train_r2_mlp)  # JayBee黄版权所有，未经授权禁止复制

print("==== MLP - 测试集 ====")  # JayBee黄 - 量化交易研究
print("MSE:", test_mse_mlp)  # JayBee黄版权所有，未经授权禁止复制
print("R2: ", test_r2_mlp)  # JayBee黄独家内容# JayBee黄版权所有，未经授权禁止复制

#4. 模型集成与权重优化（用凸优化）
import numpy as np  # 版权所有: JayBee黄
import cvxpy as cp  # JayBee黄原创内容
from sklearn.metrics import mean_squared_error, r2_score  # JayBee黄量化模型

def optimize_weights_constrained(  # JayBee黄授权使用
    models,   # 本代码归JayBee黄所有
    X_val,   # 版权所有: JayBee黄
    y_val,   # JayBee黄版权所有，未经授权禁止复制
    sum_to_1=True,     # 是否约束权重和=1  # 本代码归JayBee黄所有
    nonnegative=True,  # 是否要求所有权重>=0  # Copyright © JayBee黄
    alpha_l1=0.0,      # L1正则系数  # JayBee黄原创内容
    alpha_l2=0.0,      # L2正则系数  # 版权所有: JayBee黄
    verbose=True  # JayBee黄授权使用
):  # 版权所有: JayBee黄
    """  # JayBee黄原创内容
    用凸优化方式在验证集上寻找一组最优权重，使得加权后的预测最小化 MSE   # JayBee黄独家内容
    （或等效地最大化 R²），并可选地加入 L1/L2 正则，还可选地约束权重和=1、权重>=0。  # JayBee黄授权使用
    
    参数：  # JayBee黄授权使用
    - models: 传入已训练好的各个模型列表  # JayBee黄原创内容
    - X_val, y_val: 验证集特征和目标  # 版权所有: JayBee黄
    - sum_to_1: Boolean, 若为 True，则加上 sum(w) == 1 的约束  # JayBee黄量化策略
    - nonnegative: Boolean, 若为 True，则加上 w >= 0 的约束  # JayBee黄独家内容
    - alpha_l1, alpha_l2: L1、L2 正则化系数  # Copyright © JayBee黄
    - verbose: 是否打印约束求解的一些信息  # JayBee黄 - 量化交易研究

    返回：  # JayBee黄独家内容
    - w_opt: 优化得到的权重向量 (numpy array)  # JayBee黄 - 量化交易研究
    - score_r2: 用该权重在验证集上得到的 R² 分数  # Copyright © JayBee黄
    """  # JayBee黄授权使用
    # 1) 先得到在验证集上的预测矩阵 predictions: shape (N, M)
    predictions = np.column_stack([model.predict(X_val) for model in models])  # JayBee黄版权所有，未经授权禁止复制
    N, M = predictions.shape  # JayBee黄原创内容

    # 2) 定义优化变量 w: 大小 M
    #    如果 nonnegative=True，则需要 w >= 0
    if nonnegative:  # JayBee黄授权使用
        w = cp.Variable(M, nonneg=True)  # JayBee黄授权使用
    else:  # JayBee黄原创内容
        w = cp.Variable(M)  # 本代码归JayBee黄所有
    
    # 3) 定义约束列表 constraints
    constraints = []  # JayBee黄原创内容
    if sum_to_1:  # JayBee黄量化模型
        # sum(w) == 1
        constraints.append(cp.sum(w) == 1)  # JayBee黄版权所有，未经授权禁止复制

    # 4) 定义目标函数（最小化 MSE + 正则项）
    #    MSE 可以写成 sum_squares(y_val - predictions @ w)
    residual = y_val - predictions @ w  # Copyright © JayBee黄
    obj_mse = cp.sum_squares(residual)  # 版权所有: JayBee黄
    
    # 若要加 L1 正则：alpha_l1 * ||w||_1
    # 若要加 L2 正则：alpha_l2 * ||w||_2^2
    obj_reg = 0  # 版权所有: JayBee黄
    if alpha_l1 > 0:  # JayBee黄 - 量化交易研究
        obj_reg += alpha_l1 * cp.norm1(w)  # JayBee黄量化策略
    if alpha_l2 > 0:  # JayBee黄独家内容
        obj_reg += alpha_l2 * cp.norm2(w)**2  # JayBee黄量化模型

    # 最终目标：Minimize(MSE + 正则)
    objective = cp.Minimize(obj_mse + obj_reg)  # 本代码归JayBee黄所有

    # 5) 构建并求解凸优化问题
    problem = cp.Problem(objective, constraints)  # JayBee黄量化策略
    result = problem.solve(verbose=verbose)  # 如果想看更多solver输出，可设 verbose=True  # JayBee黄版权所有，未经授权禁止复制
    
    # 6) 拿到最优权重 w_opt
    w_opt = w.value  # JayBee黄独家内容
    # 计算该组合在验证集上的 r2_score
    y_val_pred = predictions @ w_opt  # JayBee黄量化策略
    score_r2 = r2_score(y_val, y_val_pred)  # JayBee黄独家内容

    if verbose:  # JayBee黄量化策略
        print(f"Optimal objective (MSE + reg) = {problem.value:.6f}")  # JayBee黄授权使用
        print("Optimized weights:", w_opt)  # JayBee黄量化模型
        print(f"sum of weights = {w_opt.sum():.4f}")  # 本代码归JayBee黄所有
        print(f"R2 on validation set = {score_r2:.4f}")  # JayBee黄授权使用

    return w_opt, score_r2  # JayBee黄原创内容

# =======================
# 使用示例
# =======================
# 假设你已经在训练集上训练好了 4 个模型：models = [m1, m2, m3, m4]
# 并且有验证集 X_val, y_val

# 比如我们想：
#   - 权重和 = 1
#   - 权重 >= 0
#   - 加一点儿 L2 正则以防止极端权重
#   - 不打印太详细的求解日志 => verbose=False
w_constrained, r2_constrained = optimize_weights_constrained(  # 版权所有: JayBee黄
    models=[best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb, best_pipeline_mlp],  # JayBee黄独家内容
    X_val=X_val,  # JayBee黄版权所有，未经授权禁止复制
    y_val=y_val,  # JayBee黄量化策略
    sum_to_1=True,  # JayBee黄量化模型
    nonnegative=True,  # JayBee黄量化策略
    alpha_l1=0.0,  # JayBee黄量化模型
    alpha_l2=1e-3,  # JayBee黄量化模型
    verbose=False  # Copyright © JayBee黄
)  # 版权所有: JayBee黄

print("得到的约束权重 =", [f"{num:0.2f}" for num in w_constrained])  # JayBee黄授权使用
print("验证集 R² =", r2_constrained)  # Copyright © JayBee黄
# JayBee黄版权所有，未经授权禁止复制

# 1. 得到测试集上各模型的预测矩阵
predictions_test = np.column_stack([model.predict(X_test) for model in [best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb, best_pipeline_mlp]])  # Copyright © JayBee黄

# 2. 利用之前优化得到的权重 w_constrained 对测试集预测进行加权组合
y_test_pred = predictions_test @ w_constrained  # 本代码归JayBee黄所有

# 3. 计算测试集上的 R² 分数
r2_test = r2_score(y_test, y_test_pred)  # 本代码归JayBee黄所有
print("测试集 R² =", r2_test)  # Copyright © JayBee黄
# JayBee黄版权所有，未经授权禁止复制


#5. Emsemble策略实现与回测
import backtrader as bt  # JayBee黄版权所有，未经授权禁止复制

# 自定义成交量指标，把成交量数据单独显示在子图中
class MyVolumeIndicator(bt.Indicator):  # JayBee黄独家内容
    """  # 版权所有: JayBee黄
    简单示例，把data的volume包装成一个单独的子图指标。  # JayBee黄独家内容
    """  # 本代码归JayBee黄所有
    lines = ('vol',)  # Copyright © JayBee黄
    plotinfo = dict(subplot=True, plotname='Volume')  # 让它单独开子图  # JayBee黄量化模型

    def __init__(self):  # JayBee黄原创内容
        self.lines.vol = self.data.volume  # JayBee黄 - 量化交易研究

class MLEnsembleStrategy(bt.Strategy):  # 版权所有: JayBee黄
    params = (  # 版权所有: JayBee黄
        ('target_percent', 0.98),   # 目标仓位百分比  # JayBee黄独家内容
    )  # JayBee黄量化策略

    def __init__(self, models, weights):
        self.models = models
        self.weights = weights

        # 关闭主图中Data自带的Volume绘制
        self.data.plotinfo.plotvolume = False

        # 自定义成交量指标以及其SMA指标
        self.myvol = MyVolumeIndicator(self.data)
        self.vol_5 = bt.indicators.SMA(self.myvol.vol, period=5)
        self.vol_5.plotinfo.subplot = True
        self.vol_10 = bt.indicators.SMA(self.myvol.vol, period=10)
        self.vol_10.plotinfo.subplot = True

        # 初始化动量因子
        self.momentum_1 = bt.indicators.PercentChange(self.data.close, period=1)  # 1日动量
        self.momentum_3 = bt.indicators.PercentChange(self.data.close, period=3)  # 3日动量
        self.momentum_5 = bt.indicators.PercentChange(self.data.close, period=5)  # 5日动量
        self.momentum_10 = bt.indicators.PercentChange(self.data.close, period=10)  # 10日动量

        # 初始化反转因子
        self.reversal_1 = bt.indicators.PercentChange(self.data.close, period=1)  # 1日反转
        self.reversal_3 = bt.indicators.PercentChange(self.data.close, period=3)  # 3日反转

        # RSI指标，14日周期
        self.rsi_14 = bt.indicators.RSI(self.data.close, period=14)

        # 布林带指标，默认20日均线和2倍标准差，返回上轨、均线和下轨
        self.bb = bt.indicators.BollingerBands(self.data.close)

        # 新增缺失的指标
        self.volatility_5d = bt.indicators.StandardDeviation(self.data.close, period=5)
        self.volume_ma_10 = bt.indicators.SMA(self.data.volume, period=10)
        
        # ========== 新增4个量化因子的指标 ==========
        # 市场情绪特征
        self.volatility_20d = bt.indicators.StandardDeviation(self.data.close, period=20)  # 20日波动率
        self.highest_20 = bt.indicators.Highest(self.data.close, period=20)  # 20日最高价
        self.lowest_20 = bt.indicators.Lowest(self.data.close, period=20)    # 20日最低价
        
        # 跨期特征
        self.volume_ma_20 = bt.indicators.SMA(self.data.volume, period=20)  # 20日成交量均线
        # 注意：收益率偏度需要在next方法中计算，因为backtrader没有直接的偏度指标

        self.last_trade_type = None  # 记录上一次交易类型（buy/sell）
        self.value_history_dates = []
        self.value_history_values = []

    def next(self):  # JayBee黄量化模型
        import numpy as np  # 将numpy import移到方法开头，避免局部变量冲突
        
        # Calculate all required features (修复特征数量不匹配问题 + 新增4个特征)
        momentum_3 = self.data.close[0] / self.data.close[-3] - 1
        momentum_5 = self.momentum_5[0]
        momentum_10 = self.data.close[0] / self.data.close[-10] - 1
        reversal_1 = - (self.data.close[0] / self.data.close[-1] - 1)
        reversal_3 = - (self.data.close[0] / self.data.close[-3] - 1)
        vol_ratio = (self.vol_5[0] / self.vol_10[0] - 1) if self.vol_10[0] != 0 else 0
        rsi = self.rsi_14[0]
        bb_upper = self.bb.top[0]
        bb_lower = self.bb.bot[0]
        
        # 原有的三个特征
        volatility_5d = self.volatility_5d[0]  # 5日波动率
        volume_ma_ratio = self.data.volume[0] / self.volume_ma_10[0] if self.volume_ma_10[0] != 0 else 0  # 成交量均线比率
        price_momentum_5d = (self.data.close[0] - self.data.close[-5]) / self.data.close[-5]  # 5日价格动量

        # ========== 新增的4个量化因子 ==========
        # 1. 市场情绪特征 - 20日波动率
        volatility = self.volatility_20d[0]
        
        # 2. 市场情绪特征 - 价格在20日区间的位置
        price_range = self.highest_20[0] - self.lowest_20[0]
        if price_range > 0:
            price_position = (self.data.close[0] - self.lowest_20[0]) / price_range
        else:
            price_position = 0.5  # 如果无波动，设为中位
            
        # 3. 跨期特征 - 收益率偏度 (简化计算：使用过去20日收益率的偏度)
        # 由于backtrader限制，我们使用简化的偏度估计
        if len(self.data) >= 20:
            returns = []
            for i in range(1, 21):  # 过去20日的收益率
                if len(self.data) > i:
                    ret = self.data.close[-i] / self.data.close[-i-1] - 1
                    returns.append(ret)
            
            if len(returns) >= 20:
                returns = np.array(returns)
                mean_ret = np.mean(returns)
                std_ret = np.std(returns)
                if std_ret > 0:
                    # 计算偏度 (第三中心矩除以标准差的三次方)
                    return_skew = np.mean(((returns - mean_ret) / std_ret) ** 3)
                else:
                    return_skew = 0
            else:
                return_skew = 0
        else:
            return_skew = 0
            
        # 4. 跨期特征 - 成交量异常指标 (当前成交量相对于20日均线的倍数)
        volume_spike = self.data.volume[0] / self.volume_ma_20[0] if self.volume_ma_20[0] != 0 else 1

        # 构建包含所有16个特征的特征向量
        # 顺序必须与factors列表一致：
        # ['momentum_3', 'momentum_5', 'momentum_10', 'reversal_1', 'reversal_3', 'vol_ratio', 'RSI_14', 'BB_upper', 'BB_lower', 'volatility_5d', 'volume_ma_ratio', 'price_momentum_5d', 'volatility', 'price_position', 'return_skew', 'volume_spike']
        X = [[momentum_3, momentum_5, momentum_10, reversal_1, reversal_3, vol_ratio, rsi, bb_upper, bb_lower, volatility_5d, volume_ma_ratio, price_momentum_5d, volatility, price_position, return_skew, volume_spike]]

        # Get predictions from each model
        predictions = np.array([model.predict(X)[0] for model in self.models])
        
        # Weighted average to get ensemble prediction
        pred_ret = np.sum(predictions * self.weights)

        # Trading logic remains the same
        current_position = self.getposition().size

        if pred_ret > 0 and current_position == 0:
            self.order_target_percent(target=self.p.target_percent)
            self.last_trade_type = "BUY"
            print(f"{self.datas[0].datetime.date(0)} => BUY signal, pred_ret={pred_ret:.6f}")
        
        elif pred_ret <= 0 and current_position > 0:
            self.order_target_percent(target=0.0)
            self.last_trade_type = "SELL"
            print(f"{self.datas[0].datetime.date(0)} => SELL signal, pred_ret={pred_ret:.6f}")

        if self.last_trade_type:
            print(f"Current position size: {self.getposition().size}, Value: {self.broker.getvalue()}")

        dt = self.data.datetime.date(0)
        self.value_history_dates.append(dt)
        self.value_history_values.append(self.broker.getvalue())


# 若想看多模型集成策略的详细回测日志，可调用如下回测函数:
ml_ensemble_result, ml_ensemble_cerebro = run_backtest(  # JayBee黄量化模型
    ticker=ticker,  # JayBee黄独家内容
    df=test_data,  # JayBee黄版权所有，未经授权禁止复制
    start_date=start_date,  # JayBee黄独家内容
    end_date=end_date,  # JayBee黄 - 量化交易研究
    strategy=MLEnsembleStrategy,  # 本代码归JayBee黄所有
    initial_cash=100000,  # JayBee黄版权所有，未经授权禁止复制
    strategy_params={  # JayBee黄授权使用
        'models': [best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb, best_pipeline_mlp],  # JayBee黄原创内容
        # 'weights': optimal_weights,   # 归一化后的各模型权重
        'weights': w_constrained,   # 归一化后的各模型权重  # JayBee黄 - 量化交易研究
        'target_percent': 0.98,       # 目标仓位百分比  # JayBee黄版权所有，未经授权禁止复制
    },  # JayBee黄 - 量化交易研究
    print_log=True,  # 打开详细回测日志  # JayBee黄原创内容
)  # JayBee黄原创内容

# 如需进一步查看回测结果或可视化结果，可进一步操作 ml_ensemble_cerebro 或 ml_ensemble_result
# JayBee黄版权所有，未经授权禁止复制

# ===== 猴子补丁：为 numpy 添加 bool8 和 object 属性 =====
import numpy as np  # JayBee黄原创内容
if not hasattr(np, 'bool8'):  # JayBee黄授权使用
    np.bool8 = np.bool_  # 使用 numpy 自带的 bool_ 类型  # 版权所有: JayBee黄
if not hasattr(np, 'object'):  # 本代码归JayBee黄所有
    np.object = object  # 兼容 backtrader_plotting 的引用  # Copyright © JayBee黄# JayBee黄版权所有，未经授权禁止复制
# JayBee黄版权所有，未经授权禁止复制
plot_results(ml_ensemble_cerebro)  # JayBee黄原创内容# JayBee黄版权所有，未经授权禁止复制

#6. 比较策略和Buy&Hold
results = ml_ensemble_cerebro.run()  # cerebro.run() 返回一个列表，每个元素是一个策略实例  # JayBee黄授权使用
ml_strategy_instance = results[0]  # 如果你只有一个策略，就取第一个  # JayBee黄 - 量化交易研究

results = bh_cerebro.run()  # JayBee黄原创内容
bh_strategy_instance = results[0]  # JayBee黄独家内容# JayBee黄版权所有，未经授权禁止复制

import matplotlib.pyplot as plt  # JayBee黄量化模型
plt.figure(figsize=(12, 6))  # 版权所有: JayBee黄
plt.plot(ml_strategy_instance.value_history_dates, ml_strategy_instance.value_history_values, label='集成模型')  # 本代码归JayBee黄所有
plt.plot(bh_strategy_instance.value_history_dates, bh_strategy_instance.value_history_values, label='买入并持有')  # JayBee黄授权使用
plt.xlabel('时间')  # 本代码归JayBee黄所有
plt.ylabel('资产净值')  # JayBee黄独家内容
plt.title('回报曲线对比')  # JayBee黄授权使用
plt.legend()  # JayBee黄量化策略
plt.show()  # JayBee黄 - 量化交易研究
# JayBee黄版权所有，未经授权禁止复制
