# 线性回归模型家族对比分析

## 📋 模型概述

本分析基于原始的 `17w_2.3_26.6w_RS_Mo.py` 模型，将其中的LinearRegression替换为不同的线性回归变体，以客观比较各种线性回归方法在量化交易中的表现。

### 🎯 生成的模型文件

| 文件名 | 模型类型 | 核心特性 | 正则化方法 |
|--------|----------|----------|------------|
| `17w_2.3_26.6w_RS_Lasso.py` | Lasso Regression | L1正则化，自动特征选择 | L1正则化 |
| `17w_2.3_26.6w_RS_ElasticNet.py` | ElasticNet Regression | L1+L2混合正则化，平衡稳定性 | L1+L2正则化 |

## 🔍 模型技术特性对比

### Lasso Regression
- **正则化类型**: L1正则化 (||w||₁)
- **特征选择**: ✅ 自动特征选择，将不重要特征系数压缩为0
- **稀疏性**: ✅ 产生稀疏解，模型简洁
- **稳定性**: ⚠️ 在特征高度相关时可能不稳定
- **超参数**: 
  - `alpha`: 正则化强度 [0.001, 0.01, 0.1, 1.0, 10.0]
  - `fit_intercept`: 是否拟合截距 [True, False]
  - `max_iter`: 最大迭代次数 [1000, 2000]

### ElasticNet Regression
- **正则化类型**: L1+L2混合正则化 (α[ρ||w||₁ + (1-ρ)||w||₂²])
- **特征选择**: ✅ 平衡的特征选择，比Lasso更稳定
- **稀疏性**: ✅ 可控的稀疏性，通过l1_ratio调节
- **稳定性**: ✅ L2正则化提供稳定性保障
- **超参数**:
  - `alpha`: 总正则化强度 [0.001, 0.01, 0.1, 1.0, 10.0]
  - `l1_ratio`: L1/L2混合比例 [0.1, 0.5, 0.7, 0.9]
  - `fit_intercept`: 是否拟合截距 [True, False]
  - `max_iter`: 最大迭代次数 [1000, 2000]

## 📊 预期性能差异

### 特征选择能力
- **Lasso**: 强特征选择，可能选择相关特征组中的任意一个
- **ElasticNet**: 温和特征选择，倾向于保留相关特征组

### 模型稳定性
- **Lasso**: 在特征高度相关时可能不稳定
- **ElasticNet**: L2正则化提供更好的稳定性

### 泛化能力
- **Lasso**: 通过特征选择提高泛化能力
- **ElasticNet**: 通过双重正则化平衡偏差和方差

## 🎯 评估指标

两个模型将使用相同的评估框架进行对比：

### 📈 预测性能指标
- **MSE (均方误差)**: 预测精度
- **R² (决定系数)**: 模型拟合优度
- **IC (信息系数)**: 预测方向准确性
- **IR (信息比率)**: 风险调整后的预测能力

### 💰 交易性能指标
- **夏普比率**: 风险调整后收益
- **最大回撤**: 风险控制能力
- **卡尔马比率**: 回撤调整后收益
- **胜率**: 盈利交易比例
- **盈亏比**: 平均盈利/平均亏损

### 🔄 时间序列验证
- **Walk-Forward Analysis**: 模拟真实交易环境
- **Purged Cross-Validation**: 避免数据泄漏

## 🚀 运行说明

### 执行顺序
1. 首先运行 `17w_2.3_26.6w_RS_Lasso.py`
2. 然后运行 `17w_2.3_26.6w_RS_ElasticNet.py`
3. 收集两个模型的终端输出结果
4. 更新本文档的结果对比部分

### 缓存机制
- 两个模型都使用相同的缓存系统
- 避免重复训练相同的模型配置
- 缓存文件保存在 `/Users/<USER>/MLQuant/Quant_ML_Struc/cache/models`

## 📋 待更新：实际运行结果对比

*注：以下部分将在模型运行完成后更新*

### 模型训练结果

| 指标 | Lasso Regression | ElasticNet Regression | 差异 |
|------|------------------|----------------------|------|
| 训练集R² | 待更新 | 待更新 | 待更新 |
| 测试集R² | 待更新 | 待更新 | 待更新 |
| 选择特征数 | 待更新 | 待更新 | 待更新 |
| 最佳alpha | 待更新 | 待更新 | 待更新 |
| 最佳l1_ratio | N/A | 待更新 | N/A |

### 集成模型权重

| 模型组件 | Lasso版本权重 | ElasticNet版本权重 | 差异 |
|----------|---------------|-------------------|------|
| 线性回归 | 待更新 | 待更新 | 待更新 |
| 随机森林 | 待更新 | 待更新 | 待更新 |
| XGBoost | 待更新 | 待更新 | 待更新 |
| MLP | 待更新 | 待更新 | 待更新 |

### 回测结果对比

| 指标 | 原版Mo模型 | Lasso版本 | ElasticNet版本 | 最佳表现 |
|------|------------|-----------|----------------|----------|
| 最终资金 | 待更新 | 待更新 | 待更新 | 待更新 |
| 总收益率 | 待更新 | 待更新 | 待更新 | 待更新 |
| 夏普比率 | 待更新 | 待更新 | 待更新 | 待更新 |
| 最大回撤 | 待更新 | 待更新 | 待更新 | 待更新 |
| IC | 待更新 | 待更新 | 待更新 | 待更新 |
| IR | 待更新 | 待更新 | 待更新 | 待更新 |

### 特征选择分析

#### Lasso特征选择结果
- 选择的特征数量: 待更新
- 主要选择的特征: 待更新
- 特征重要性排序: 待更新

#### ElasticNet特征选择结果
- 选择的特征数量: 待更新
- 主要选择的特征: 待更新
- L1/L2混合比例: 待更新
- 与Lasso选择差异: 待更新

## 🎯 结论与建议

*待模型运行完成后更新*

### 性能总结
- 最佳模型: 待确定
- 关键优势: 待分析
- 适用场景: 待总结

### 实际应用建议
- 推荐使用场景: 待更新
- 参数调优建议: 待更新
- 风险控制要点: 待更新

---

**文档状态**: 🔄 等待模型运行结果更新  
**最后更新**: 2024年12月19日  
**下次更新**: 模型运行完成后
