# D11_ML15.6w_v2.0_Optimized.py
# 优化版本：修复15.6w模型的核心问题
# 主要改进：
# 1. 简化特征工程，使用更有效的技术指标
# 2. 改进模型选择：Ridge回归 + 随机森林 + LightGBM + 优化MLP
# 3. 增加交易信号阈值和风险控制
# 4. 改进权重优化方法
# 5. 添加数据泄露防护

import numpy as np
import pandas as pd
from sklearn.linear_model import Ridge
from sklearn.ensemble import RandomForestRegressor
from lightgbm import LGBMRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import TimeSeriesSplit
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import talib
import sys
import pickle
import time
import warnings
from tqdm import tqdm

# 导入必要的库
from dotenv import load_dotenv, find_dotenv
dotenv_path = find_dotenv("../../.env")
load_dotenv(dotenv_path)

# ===== 改进的numpy兼容性补丁 =====
def safe_numpy_patch():
    """安全地为numpy添加向后兼容的属性，避免deprecation warnings"""
    try:
        # 只在属性不存在时添加，避免重复警告
        if not hasattr(np, 'bool8'):
            with warnings.catch_warnings():
                warnings.simplefilter("ignore", DeprecationWarning)
                np.bool8 = np.bool_
        
        if not hasattr(np, 'object'):
            with warnings.catch_warnings():
                warnings.simplefilter("ignore", FutureWarning)
                np.object = object
    except Exception as e:
        print(f"注意: numpy兼容性补丁应用失败: {e}")

# 应用补丁
safe_numpy_patch()

sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))
from data_processing import load_data_year, flatten_yf_columns, standardize_columns
from plotting import plot_results
from strategy.buy_and_hold import BuyAndHoldStrategy
from back_test import run_backtest
import backtrader as bt

# 设置随机种子
np.random.seed(42)
import random
random.seed(42)

# 忽略警告
warnings.filterwarnings('ignore')

# 设置显示选项
pd.set_option('display.float_format', lambda x: '%.4f' % x)
plt.style.use('seaborn-v0_8-bright')
plt.rcParams['font.sans-serif'] = ['PingFang HK']
plt.rcParams['axes.unicode_minus'] = False

# 创建模型缓存目录
cache_dir = '/MLQuant/Quant_ML_Struc/Reinforcement/tensorboard_logs'
os.makedirs(cache_dir, exist_ok=True)

print("🚀 启动优化版本集成模型...")
print("📊 主要改进：简化特征工程 + 模型升级 + 风险控制")

# ==================== 数据加载 ====================
print("\n📈 加载Tesla股票数据...")
excel_path = '../cache/TSLA_day.xlsx'
data = pd.read_excel(excel_path)

# 数据预处理
data.columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']
data['datetime'] = pd.to_datetime(data['datetime'])
data.set_index('datetime', inplace=True)

ticker = 'TSLA'
start_date = data.index.min()
end_date = data.index.max()

print(f"数据时间范围：{start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
print(f"数据样本数：{len(data)}")

# ==================== 优化特征工程 ====================
print("\n🔧 构建优化的技术指标...")

df = data.copy()

# 1. 核心技术指标（经过筛选的高效指标）
print("计算核心技术指标...")

# 动量指标（多时间尺度）
df['momentum_5'] = df['close'].pct_change(5)
df['momentum_10'] = df['close'].pct_change(10)
df['momentum_20'] = df['close'].pct_change(20)

# 成交量指标
df['volume_sma_5'] = df['volume'].rolling(5).mean()
df['volume_sma_20'] = df['volume'].rolling(20).mean()
df['vol_ratio'] = df['volume_sma_5'] / df['volume_sma_20']

# RSI指标
df['rsi_14'] = talib.RSI(df['close'], timeperiod=14)
df['rsi_signal'] = (df['rsi_14'] - 50) / 50  # 标准化RSI

# 布林带指标
bb_upper, bb_middle, bb_lower = talib.BBANDS(df['close'], timeperiod=20, nbdevup=2, nbdevdn=2, matype=0)
df['bb_position'] = (df['close'] - bb_lower) / (bb_upper - bb_lower)  # 价格在布林带中的位置
df['bb_width'] = (bb_upper - bb_lower) / bb_middle  # 布林带宽度

# 波动率指标
df['volatility_10'] = df['close'].rolling(10).std()
df['volatility_20'] = df['close'].rolling(20).std()

# MACD指标
macd, macdsignal, macdhist = talib.MACD(df['close'], fastperiod=12, slowperiod=26, signalperiod=9)
df['macd_signal'] = macd - macdsignal

# 价格位置指标
df['price_rank_20'] = df['close'].rolling(20).rank(pct=True)

# 反转指标
df['reversal_5'] = -df['close'].pct_change(5)

print("技术指标计算完成!")

# 特征列表（优化后的精选特征）
features = [
    'momentum_5', 'momentum_10', 'momentum_20',
    'vol_ratio', 'rsi_signal', 'bb_position', 'bb_width',
    'volatility_10', 'volatility_20', 'macd_signal',
    'price_rank_20', 'reversal_5'
]

print(f"选定特征数量：{len(features)}")

# 定义目标变量
df['future_ret_1d'] = df['close'].pct_change().shift(-1)

# 清理数据
df.dropna(inplace=True)
print(f"清理后数据样本数：{len(df)}")

# 检查特征与目标变量的相关性
print("\n📊 特征与目标变量相关性分析:")
correlation = df[features + ['future_ret_1d']].corr()['future_ret_1d'].abs().sort_values(ascending=False)
print(correlation[:-1].head(10))

# ==================== 防止数据泄露的划分方法 ====================
print("\n🔒 执行时间序列划分（防止数据泄露）...")

# 按时间顺序划分：60% 训练，20% 验证，20% 测试
train_size = int(len(df) * 0.6)
val_size = int(len(df) * 0.8)

train_data = df.iloc[:train_size].copy()
val_data = df.iloc[train_size:val_size].copy()
test_data = df.iloc[val_size:].copy()

print(f"训练集: {len(train_data)} 样本 ({train_data.index.min()} → {train_data.index.max()})")
print(f"验证集: {len(val_data)} 样本 ({val_data.index.min()} → {val_data.index.max()})")
print(f"测试集: {len(test_data)} 样本 ({test_data.index.min()} → {test_data.index.max()})")

# 准备特征矩阵
X_train = train_data[features].values
y_train = train_data['future_ret_1d'].values
X_val = val_data[features].values
y_val = val_data['future_ret_1d'].values
X_test = test_data[features].values
y_test = test_data['future_ret_1d'].values

print(f"特征矩阵形状: 训练{X_train.shape}, 验证{X_val.shape}, 测试{X_test.shape}")

# ==================== Buy & Hold基准策略 ====================
print("\n📊 运行Buy & Hold基准策略...")

bh_result, bh_cerebro = run_backtest(
    ticker=ticker,
    df=test_data,
    start_date=start_date,
    end_date=end_date,
    strategy=BuyAndHoldStrategy,
    initial_cash=100000,
    print_log=True,
    timeframe=bt.TimeFrame.Days,
    compression=1
)

# ==================== 模型训练与优化 ====================
print("\n🤖 开始训练优化的集成模型...")

# 1. Ridge回归（替代普通线性回归）
print("\n📈 训练Ridge回归模型...")
scaler_ridge = RobustScaler()
X_train_scaled = scaler_ridge.fit_transform(X_train)
X_val_scaled = scaler_ridge.transform(X_val)
X_test_scaled = scaler_ridge.transform(X_test)

ridge_model = Ridge(alpha=1.0, random_state=42)
ridge_model.fit(X_train_scaled, y_train)

y_pred_train_ridge = ridge_model.predict(X_train_scaled)
y_pred_val_ridge = ridge_model.predict(X_val_scaled)
y_pred_test_ridge = ridge_model.predict(X_test_scaled)

train_r2_ridge = r2_score(y_train, y_pred_train_ridge)
val_r2_ridge = r2_score(y_val, y_pred_val_ridge)
test_r2_ridge = r2_score(y_test, y_pred_test_ridge)

print(f"Ridge回归 - 训练集R²: {train_r2_ridge:.6f}, 验证集R²: {val_r2_ridge:.6f}, 测试集R²: {test_r2_ridge:.6f}")

# 2. 随机森林（优化参数）
print("\n🌲 训练随机森林模型...")
rf_model = RandomForestRegressor(
    n_estimators=200,
    max_depth=10,
    min_samples_split=10,
    min_samples_leaf=5,
    max_features='sqrt',
    random_state=42,
    n_jobs=-1
)
rf_model.fit(X_train, y_train)

y_pred_train_rf = rf_model.predict(X_train)
y_pred_val_rf = rf_model.predict(X_val)
y_pred_test_rf = rf_model.predict(X_test)

train_r2_rf = r2_score(y_train, y_pred_train_rf)
val_r2_rf = r2_score(y_val, y_pred_val_rf)
test_r2_rf = r2_score(y_test, y_pred_test_rf)

print(f"随机森林 - 训练集R²: {train_r2_rf:.6f}, 验证集R²: {val_r2_rf:.6f}, 测试集R²: {test_r2_rf:.6f}")

# 3. LightGBM（替代XGBoost）
print("\n⚡ 训练LightGBM模型...")
lgb_model = LGBMRegressor(
    objective='regression',
    num_leaves=31,
    learning_rate=0.05,
    feature_fraction=0.8,
    bagging_fraction=0.8,
    bagging_freq=5,
    random_state=42,
    verbose=-1
)
lgb_model.fit(X_train, y_train)

y_pred_train_lgb = lgb_model.predict(X_train)
y_pred_val_lgb = lgb_model.predict(X_val)
y_pred_test_lgb = lgb_model.predict(X_test)

train_r2_lgb = r2_score(y_train, y_pred_train_lgb)
val_r2_lgb = r2_score(y_val, y_pred_val_lgb)
test_r2_lgb = r2_score(y_test, y_pred_test_lgb)

print(f"LightGBM - 训练集R²: {train_r2_lgb:.6f}, 验证集R²: {val_r2_lgb:.6f}, 测试集R²: {test_r2_lgb:.6f}")

# 4. 优化MLP神经网络
print("\n🧠 训练优化MLP神经网络...")
mlp_model = MLPRegressor(
    hidden_layer_sizes=(128, 64),
    alpha=0.001,
    learning_rate_init=0.01,
    max_iter=500,
    random_state=42,
    early_stopping=True,
    validation_fraction=0.1
)
mlp_model.fit(X_train_scaled, y_train)

y_pred_train_mlp = mlp_model.predict(X_train_scaled)
y_pred_val_mlp = mlp_model.predict(X_val_scaled)
y_pred_test_mlp = mlp_model.predict(X_test_scaled)

train_r2_mlp = r2_score(y_train, y_pred_train_mlp)
val_r2_mlp = r2_score(y_val, y_pred_val_mlp)
test_r2_mlp = r2_score(y_test, y_pred_test_mlp)

print(f"优化MLP - 训练集R²: {train_r2_mlp:.6f}, 验证集R²: {val_r2_mlp:.6f}, 测试集R²: {test_r2_mlp:.6f}")

# ==================== 模型性能汇总 ====================
print("\n📊 模型性能汇总:")
models_performance = {
    'Ridge回归': {'train_r2': train_r2_ridge, 'val_r2': val_r2_ridge, 'test_r2': test_r2_ridge},
    '随机森林': {'train_r2': train_r2_rf, 'val_r2': val_r2_rf, 'test_r2': test_r2_rf},
    'LightGBM': {'train_r2': train_r2_lgb, 'val_r2': val_r2_lgb, 'test_r2': test_r2_lgb},
    '优化MLP': {'train_r2': train_r2_mlp, 'val_r2': val_r2_mlp, 'test_r2': test_r2_mlp}
}

performance_df = pd.DataFrame(models_performance).T
print(performance_df)

# ==================== 改进的集成权重优化 ====================
print("\n⚖️ 优化集成权重...")

# 使用验证集预测进行权重优化
val_predictions = np.column_stack([
    y_pred_val_ridge, y_pred_val_rf, y_pred_val_lgb, y_pred_val_mlp
])

# 基于验证集表现确定权重
val_r2_scores = np.array([val_r2_ridge, val_r2_rf, val_r2_lgb, val_r2_mlp])

# 如果所有R²都很低，使用等权重
if np.max(val_r2_scores) < 0.1:
    print("⚠️ 所有模型R²较低，使用等权重策略")
    ensemble_weights = np.array([0.25, 0.25, 0.25, 0.25])
else:
    # 基于R²分数的软权重
    val_r2_scores = np.maximum(val_r2_scores, 0)  # 确保非负
    if np.sum(val_r2_scores) > 0:
        ensemble_weights = val_r2_scores / np.sum(val_r2_scores)
    else:
        ensemble_weights = np.array([0.25, 0.25, 0.25, 0.25])

print(f"集成权重: Ridge({ensemble_weights[0]:.3f}), RF({ensemble_weights[1]:.3f}), LGB({ensemble_weights[2]:.3f}), MLP({ensemble_weights[3]:.3f})")

# 计算集成预测
test_predictions = np.column_stack([
    y_pred_test_ridge, y_pred_test_rf, y_pred_test_lgb, y_pred_test_mlp
])
ensemble_pred = test_predictions @ ensemble_weights
ensemble_r2 = r2_score(y_test, ensemble_pred)

print(f"集成模型测试集R²: {ensemble_r2:.6f}")

# ==================== 改进的交易策略 ====================
print("\n💼 构建改进的交易策略...")

class OptimizedEnsembleStrategy(bt.Strategy):
    params = (
        ('models', None),
        ('weights', None),
        ('scaler', None),
        ('features', None),
        ('signal_threshold', 0.01),  # 增加信号阈值
        ('stop_loss', 0.05),         # 5%止损
        ('take_profit', 0.15),       # 15%止盈
        ('max_position', 0.95),      # 最大仓位95%
    )

    def __init__(self):
        self.models = self.params.models
        self.weights = self.params.weights
        self.scaler = self.params.scaler
        self.features = self.params.features
        
        # 技术指标计算
        self.momentum_5 = bt.indicators.PercentChange(self.data.close, period=5)
        self.momentum_10 = bt.indicators.PercentChange(self.data.close, period=10)
        self.momentum_20 = bt.indicators.PercentChange(self.data.close, period=20)
        
        self.volume_sma_5 = bt.indicators.SMA(self.data.volume, period=5)
        self.volume_sma_20 = bt.indicators.SMA(self.data.volume, period=20)
        
        self.rsi_14 = bt.indicators.RSI(self.data.close, period=14)
        self.bb = bt.indicators.BollingerBands(self.data.close, period=20)
        
        self.volatility_10 = bt.indicators.StandardDeviation(self.data.close, period=10)
        self.volatility_20 = bt.indicators.StandardDeviation(self.data.close, period=20)
        
        self.macd = bt.indicators.MACD(self.data.close)
        
        # 价格排名需要手动计算
        self.highest_20 = bt.indicators.Highest(self.data.close, period=20)
        self.lowest_20 = bt.indicators.Lowest(self.data.close, period=20)
        
        # 交易统计
        self.entry_price = None
        self.trade_count = 0
        
    def next(self):
        # 确保有足够的历史数据
        if len(self.data) < 30:
            return
            
        # 计算特征
        vol_ratio = self.volume_sma_5[0] / self.volume_sma_20[0] if self.volume_sma_20[0] != 0 else 1
        rsi_signal = (self.rsi_14[0] - 50) / 50
        
        bb_range = self.bb.top[0] - self.bb.bot[0]
        bb_position = (self.data.close[0] - self.bb.bot[0]) / bb_range if bb_range != 0 else 0.5
        bb_width = bb_range / self.bb.mid[0] if self.bb.mid[0] != 0 else 0
        
        macd_signal = self.macd.macd[0] - self.macd.signal[0]
        
        # 价格排名计算
        price_range = self.highest_20[0] - self.lowest_20[0]
        price_rank_20 = (self.data.close[0] - self.lowest_20[0]) / price_range if price_range != 0 else 0.5
        
        reversal_5 = -self.momentum_5[0]
        
        # 构建特征向量
        X_current = np.array([[
            self.momentum_5[0], self.momentum_10[0], self.momentum_20[0],
            vol_ratio, rsi_signal, bb_position, bb_width,
            self.volatility_10[0], self.volatility_20[0], macd_signal,
            price_rank_20, reversal_5
        ]])
        
        # 模型预测
        try:
            # 对需要标准化的模型进行预测
            X_scaled = self.scaler.transform(X_current)
            pred_ridge = self.models[0].predict(X_scaled)[0]
            pred_mlp = self.models[3].predict(X_scaled)[0]
            
            # 对不需要标准化的模型进行预测
            pred_rf = self.models[1].predict(X_current)[0]
            pred_lgb = self.models[2].predict(X_current)[0]
            
            # 集成预测
            predictions = np.array([pred_ridge, pred_rf, pred_lgb, pred_mlp])
            ensemble_pred = np.sum(predictions * self.weights)
            
        except Exception as e:
            print(f"预测错误: {e}")
            return
        
        current_position = self.getposition().size
        current_value = self.broker.getvalue()
        
        # 风险控制：检查止损和止盈
        if current_position > 0 and self.entry_price is not None:
            current_return = (self.data.close[0] - self.entry_price) / self.entry_price
            
            # 止损
            if current_return <= -self.params.stop_loss:
                self.close()
                print(f"{self.data.datetime.date(0)} [止损] 触发止损: {current_return:.2%}")
                return
                
            # 止盈
            if current_return >= self.params.take_profit:
                self.close()
                print(f"{self.data.datetime.date(0)} [止盈] 触发止盈: {current_return:.2%}")
                return
        
        # 交易信号判断（增加阈值）
        if ensemble_pred > self.params.signal_threshold and current_position == 0:
            # 买入信号
            self.order_target_percent(target=self.params.max_position)
            self.entry_price = self.data.close[0]
            self.trade_count += 1
            print(f"{self.data.datetime.date(0)} [买入] 预测收益: {ensemble_pred:.4f}, 交易#{self.trade_count}")
            
        elif ensemble_pred < -self.params.signal_threshold and current_position > 0:
            # 卖出信号
            self.close()
            print(f"{self.data.datetime.date(0)} [卖出] 预测收益: {ensemble_pred:.4f}")

# ==================== 策略回测 ====================
print("\n📈 运行优化集成策略回测...")

optimized_result, optimized_cerebro = run_backtest(
    ticker=ticker,
    df=test_data,
    start_date=start_date,
    end_date=end_date,
    strategy=OptimizedEnsembleStrategy,
    initial_cash=100000,
    strategy_params={
        'models': [ridge_model, rf_model, lgb_model, mlp_model],
        'weights': ensemble_weights,
        'scaler': scaler_ridge,
        'features': features,
        'signal_threshold': 0.01,
        'stop_loss': 0.05,
        'take_profit': 0.15,
        'max_position': 0.95,
    },
    print_log=True,
    timeframe=bt.TimeFrame.Days,
    compression=1
)

# ==================== 结果分析 ====================
print("\n📊 回测结果对比分析:")
print("="*60)

# 获取策略结果
optimized_results = optimized_cerebro.run()
optimized_strategy = optimized_results[0]

bh_results = bh_cerebro.run()
bh_strategy = bh_results[0]

print("策略性能对比:")
print(f"Buy & Hold最终资金: {bh_strategy.broker.getvalue():.2f}")
print(f"优化集成模型最终资金: {optimized_strategy.broker.getvalue():.2f}")

improvement = (optimized_strategy.broker.getvalue() - bh_strategy.broker.getvalue()) / bh_strategy.broker.getvalue() * 100
print(f"相对Buy & Hold改进: {improvement:.2f}%")

print("\n📈 模型改进总结:")
print("✅ 使用精选的12个高效技术指标")
print("✅ 采用Ridge回归替代普通线性回归")
print("✅ 使用LightGBM替代XGBoost，提升训练效率")
print("✅ 增加交易信号阈值(1%)减少噪音交易")
print("✅ 添加止损(5%)和止盈(15%)风险控制")
print("✅ 使用RobustScaler提升对异常值的抗性")
print("✅ 实施时间序列划分防止数据泄露")

print(f"\n🎯 优化完成! 最终模型性能:")
print(f"集成模型R²: {ensemble_r2:.6f}")
print(f"交易策略收益: {optimized_strategy.broker.getvalue():.2f}") 