'''
Ensemble 模型策略预测股票第二天收益率

1+2. 数据处理和特征工程
- 数据流正确做法
- 特征工程/标准化：在Features_Engineering3_Full_Factors_ReadyForUse.py中已经完成，输出TSLA_5min_interday_merged_factors_winsorized_v2.xlsx。
- v3主流程：直接读取这个文件，不再做任何特征标准化/归一化。
- 回测时的价格：必须用原始价格，而不是特征工程后的标准化价格。

3. 数据集划分
- 训练集: 60%（时间序列前部分）
- 验证集: 20%（时间序列中部分）
- 测试集: 20%（时间序列后部分）
- 时间序列持续性保证

4. 模型集成
## 4.1 线性回归
- 超参数: fit_intercept
- 使用sklearn Pipeline构建
- 系数解释

## 4.2 随机森林
- 超参数: n_estimators, max_depth, min_samples_split, min_samples_leaf
- 特征重要性分析
- 过拟合控制

## 4.3 XGBoost
- 超参数: n_estimators, learning_rate, max_depth
- 梯度提升算法
- 特征重要性分析

## 4.4 MLP神经网络
- 超参数: hidden_layer_sizes, alpha, learning_rate_init
- 标准化预处理
- 非线性特征关系捕捉

5. 模型权重优化
- 凸优化方法(cvxpy)
- 约束条件: 权重和为1, 非负权重
- 验证集上性能最大化(R²)
- 可选L1/L2正则化

6. 策略回测与评估
- 基于预测的交易信号生成
- 与Buy & Hold策略对比
- 性能指标分析:
  - 年化收益率
  - 最大回撤
  - 夏普比率
  - 胜率
  - 月度/滚动收益分析
'''


# ——————————————————————————————————————————————————————————————————————————————

# 0. 导入依赖包（这些是程序需要使用的外部库）
import numpy as np  # 导入numpy库，用于数值计算，别名为np
import pandas as pd  # 导入pandas库，用于数据处理和分析，别名为pd
from sklearn.linear_model import LinearRegression  # 从sklearn库导入线性回归模型
from sklearn.metrics import mean_squared_error, r2_score  # 导入评估模型性能的指标
import matplotlib.pyplot as plt  # 导入matplotlib的pyplot模块，用于数据可视化，别名为plt
import seaborn as sns  # 导入seaborn库，用于高级数据可视化，别名为sns
from datetime import datetime, timedelta  # 从datetime模块导入日期时间相关的类
import os  # 导入os模块，用于与操作系统交互
import talib  # 导入talib库，用于计算技术指标
import sys  # 导入sys模块，用于访问Python解释器相关的变量和函数
import copy  # 导入copy模块，用于创建对象的副本
from sklearn.preprocessing import StandardScaler  # 导入数据标准化工具
from sklearn.model_selection import ParameterGrid  # 导入超参数网格搜索工具
from sklearn.pipeline import Pipeline  # 导入管道工具，用于串联多个数据处理和模型步骤
from sklearn.ensemble import RandomForestRegressor  # 导入随机森林回归模型
import xgboost as xgb  # 导入XGBoost库，用于梯度提升决策树模型
from sklearn.neural_network import MLPRegressor  # 导入多层感知器回归模型

from dotenv import load_dotenv, find_dotenv  # 从dotenv模块导入加载环境变量的函数

# 查找.env文件的路径
dotenv_path = find_dotenv("../../.env")  # 在上上级目录中查找.env文件
# 显式加载.env文件中的环境变量
load_dotenv(dotenv_path)  # 加载.env文件中的环境变量

# 将父目录添加到系统路径中，以便导入自定义模块
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))  # 将当前工作目录的父目录添加到系统路径

# 从自定义模块导入函数
from data_processing import load_data_year, flatten_yf_columns, standardize_columns  # 导入数据处理函数
from plotting import plot_results  # 导入绘图函数
from strategy.buy_and_hold import BuyAndHoldStrategy  # 导入买入持有策略
from back_test import run_backtest  # 导入回测函数
import backtrader as bt  # 导入backtrader库，用于策略回测，别名为bt

# 设置显示选项
pd.set_option('display.float_format', lambda x: '%.4f' % x)  # 设置pandas浮点数显示格式为4位小数
# 绘图风格（可选）
plt.style.use('seaborn-v0_8-bright')  # 设置matplotlib的绘图样式为seaborn-bright
# 设置中文显示
plt.rcParams['font.sans-serif'] = ['PingFang HK']  # 设置sans-serif字体为PingFang HK，以支持中文显示
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号

import random  # 导入random模块，用于生成随机数

# 固定全局随机种子，使随机结果可重现
np.random.seed(42)  # 设置numpy随机数生成器的种子为42
random.seed(42)  # 设置Python内置随机数生成器的种子为42

# ——————————————————————————————————————————————————————————————————————————————

# 1. 数据获取与预处理
end_date = datetime.now()  # 获取当前日期和时间作为结束日期
start_date = end_date - timedelta(days=5*365)  # 设置开始日期为当前日期向前5年

# 打印数据时间范围
print(f"获取数据时间范围：{start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")  # 打印数据的时间范围，格式为年-月-日

# 从Excel文件读取数据而不是yfinance
excel_path = '/MLQuant/Quant_ML_Struc/cache/TSLA_day.xlsx'  # 设置Excel文件路径
print(f"正在从Excel文件加载数据: {excel_path}")  # 打印正在加载的Excel文件路径

try:  # 尝试执行以下代码块
    df = pd.read_excel(excel_path)  # 从Excel文件读取数据到DataFrame对象df
    print("Excel数据加载成功")  # 打印加载成功的消息
except Exception as e:  # 如果try代码块出现异常，则执行except代码块
    print(f"Excel数据加载失败: {e}")  # 打印加载失败的消息和异常信息
    raise  # 重新抛出异常，终止程序执行

# 适配日期和列名
if 'trade_date' in df.columns:  # 如果DataFrame中有'trade_date'列
    df['trade_date'] = pd.to_datetime(df['trade_date'])  # 将'trade_date'列转换为日期时间类型
    df = df.sort_values('trade_date')  # 按'trade_date'列排序
    df = df.set_index('trade_date')  # 将'trade_date'列设置为索引
elif 'date' in df.columns:  # 如果DataFrame中有'date'列
    df['date'] = pd.to_datetime(df['date'])  # 将'date'列转换为日期时间类型
    df = df.sort_values('date')  # 按'date'列排序
    df = df.set_index('date')  # 将'date'列设置为索引
elif 'datetime' in df.columns:  # 如果DataFrame中有'datetime'列
    df['datetime'] = pd.to_datetime(df['datetime'])  # 将'datetime'列转换为日期时间类型
    df = df.sort_values('datetime')  # 按'datetime'列排序
    df = df.set_index('datetime')  # 将'datetime'列设置为索引

ticker = 'TSLA'  # 设置股票代码为TSLA（特斯拉）
data = flatten_yf_columns(df)  # 使用自定义函数处理DataFrame的列名
data = standardize_columns(data)  # 使用自定义函数标准化列名

print("数据预处理完成")  # 打印数据预处理完成的消息
print(data.info())  # 打印DataFrame的基本信息，如列名、非空值数量、数据类型等
print(data.head(10))  # 打印DataFrame的前10行
print(data.tail(10))  # 打印DataFrame的后10行
print(data.index.min())  # 打印索引的最小值（最早日期）
print(data.index.max())  # 打印索引的最大值（最晚日期）

print("数据框形状:", data.shape)  # 打印DataFrame的形状（行数和列数）
print("索引示例:", data.index[:5])  # 打印前5个索引值

# ——————————————————————————————————————————————————————————————————————————————

# 2. 加入技术指标
df = data.copy()  # 创建数据的副本，以便在不修改原始数据的情况下进行操作

# 动量因子: 过去5日涨跌幅
df['momentum_5'] = df['close'] / df['close'].shift(5) - 1  # 计算当前收盘价相对于5天前收盘价的变化百分比

# 成交量因子: (最近5日平均成交量) / (最近10日平均成交量) - 1
df['vol_ratio'] = df['volume'].rolling(5).mean() / df['volume'].rolling(10).mean() - 1  # 计算5日平均成交量与10日平均成交量的比值减1

# RSI指标，14日周期
df['RSI_14'] = talib.RSI(df['close'], timeperiod=14)  # 使用talib库计算14日相对强弱指标(RSI)

# 布林带
upper, middle, lower = talib.BBANDS(  # 使用talib库计算布林带，返回上轨、中轨和下轨
    df['close'],  # 使用收盘价作为输入
    timeperiod=20,  # 设置计算周期为20天
    nbdevup=2,  # 上轨为中轨上方2个标准差
    nbdevdn=2,  # 下轨为中轨下方2个标准差
    matype=0  # 移动平均类型为简单移动平均
)
df['BB_upper'] = upper  # 将布林带上轨保存到DataFrame中
df['BB_middle'] = middle  # 将布林带中轨保存到DataFrame中
df['BB_lower'] = lower  # 将布林带下轨保存到DataFrame中

# 删除含有NaN的行
df.dropna(inplace=True)  # 删除包含缺失值的行，并直接在原DataFrame上修改

# 将指标列表存储为变量
factors = ['momentum_5', 'vol_ratio', 'RSI_14', 'BB_upper', 'BB_lower']  # 创建包含所有特征名称的列表

# 查看技术指标
print("技术指标预览:")  # 打印标题
print(df[['close'] + factors].tail(5))  # 打印收盘价和所有特征的后5行数据

# ——————————————————————————————————————————————————————————————————————————————

# 3. 目标变量的定义
# 定义下期1日收益率作为目标变量
df['future_ret_1d'] = df['close'].pct_change().shift(-1)  # 计算下一天的收益率作为目标变量（用shift(-1)向前移动一天）

# 去掉NaN值
df.dropna(inplace=True)  # 删除包含缺失值的行，并直接在原DataFrame上修改

print("添加目标变量后的数据预览：")  # 打印标题
print(df[['close'] + factors].head(10))  # 打印收盘价和所有特征的前10行数据

# 绘制目标变量分布
plt.figure(figsize=(10, 5))  # 创建一个宽10英寸、高5英寸的图形
sns.histplot(df['future_ret_1d'], bins=50)  # 使用seaborn绘制目标变量的直方图，设置50个柱子
plt.title('下期收益率分布')  # 设置图表标题
plt.xlabel('收益率')  # 设置x轴标签
plt.show()  # 显示图形

# 计算因子与目标变量的相关性
corr = df[factors + ['future_ret_1d']].corr()  # 计算特征和目标变量之间的相关系数矩阵

plt.figure(figsize=(8, 6))  # 创建一个宽8英寸、高6英寸的图形
sns.heatmap(corr, annot=True, cmap='coolwarm', center=0)  # 绘制相关系数热图，显示具体数值，使用coolwarm配色，以0为中心
plt.title('因子与目标变量相关性')  # 设置图表标题
plt.show()  # 显示图形

print(f"目标变量的均值={np.mean(df['future_ret_1d'])}")  # 打印目标变量的平均值
print(f"目标变量的方差={np.std(df['future_ret_1d'])}")  # 打印目标变量的标准差（注意这里实际上是标准差而非方差）

# ——————————————————————————————————————————————————————————————————————————————
# 4. 划分训练集与测试集
# 按照时间顺序，使用前60%的数据作为训练集，中20%作为验证集，后20%作为测试集。

train_idx = int(len(df) * 0.6)  # 计算训练集结束的索引位置（60%的数据）
valid_idx = int(len(df) * 0.8)  # 计算验证集结束的索引位置（80%的数据）

split_date_1 = df.index[train_idx]  # 获取训练集和验证集的分割日期
split_date_2 = df.index[valid_idx]  # 获取验证集和测试集的分割日期

train_data = df.iloc[:train_idx].copy()  # 提取训练集数据
val_data = df.iloc[train_idx:valid_idx].copy()  # 提取验证集数据
test_data = df.iloc[valid_idx:].copy()  # 提取测试集数据

print("训练集范围:", train_data.index.min(), "→", train_data.index.max())  # 打印训练集的日期范围
print("验证集范围:", val_data.index.min(), "→", val_data.index.max())  # 打印验证集的日期范围
print("测试集范围:", test_data.index.min(), "→", test_data.index.max())  # 打印测试集的日期范围
print("\n训练集样本数:", len(train_data))  # 打印训练集的样本数量
print("验证集样本数:", len(val_data))  # 打印验证集的样本数量
print("测试集样本数:", len(test_data))  # 打印测试集的样本数量

# 可视化训练集和测试集的划分
plt.figure(figsize=(15, 6))  # 创建一个宽15英寸、高6英寸的图形
plt.plot(train_data.index, train_data['future_ret_1d'], label='训练集', color='blue')  # 绘制训练集的目标变量
plt.plot(val_data.index, val_data['future_ret_1d'], label='验证集', color='green')  # 绘制验证集的目标变量
plt.plot(test_data.index, test_data['future_ret_1d'], label='测试集', color='red')  # 绘制测试集的目标变量
plt.axvline(split_date_1, color='black', linestyle='--', label='划分点')  # 在第一个分割点绘制垂直线
plt.axvline(split_date_2, color='black', linestyle='--', label='划分点')  # 在第二个分割点绘制垂直线
plt.title('训练集、验证集、测试集划分')  # 设置图表标题
plt.xlabel('日期')  # 设置x轴标签
plt.ylabel('收益率')  # 设置y轴标签
plt.legend()  # 显示图例
plt.grid(True)  # 显示网格线
plt.show()  # 显示图形

# ——————————————————————————————————————————————————————————————————————————————
# 5. Buy & Hold策略
try:  # 尝试执行以下代码块
    bh_result, bh_cerebro = run_backtest(  # 运行买入持有策略的回测，返回结果和cerebro对象
        ticker=ticker,  # 设置股票代码
        df=test_data,  # 使用测试集数据
        start_date=test_data.index[0].to_pydatetime(),  # 设置回测开始日期为测试集第一天，转换为Python datetime对象
        end_date=test_data.index[-1].to_pydatetime(),  # 设置回测结束日期为测试集最后一天，转换为Python datetime对象
        strategy=BuyAndHoldStrategy,  # 使用买入持有策略
        initial_cash=100000,  # 设置初始资金为10万
        print_log=True,  # 打印回测日志
        timeframe=bt.TimeFrame.Days,  # 设置回测时间框架为天
        compression=1  # 设置时间压缩比为1（不压缩）
    )
    print("Buy & Hold策略回测完成")  # 打印回测完成消息
except Exception as e:  # 如果try代码块出现异常，则执行except代码块
    print(f"Buy & Hold策略回测出错: {e}")  # 打印错误消息和异常信息

# ===== 猴子补丁：为 numpy 添加缺少的属性 =====
if not hasattr(np, 'bool8'):
    np.bool8 = np.bool_
if not hasattr(np, 'object'):
    np.object = object

try:  # 尝试执行以下代码块
    plot_results(bh_cerebro)  # 绘制买入持有策略的回测结果
    print("Buy & Hold策略结果绘图完成")  # 打印绘图完成消息
except Exception as e:  # 如果try代码块出现异常，则执行except代码块
    print(f"Buy & Hold策略绘图出错: {e}")  # 打印错误消息和异常信息

# ——————————————————————————————————————————————————————————————————————————————
# 6. 模型训练与超参数优化
features = factors  # 将特征列表赋值给features变量
X_train = train_data[features].values  # 提取训练集特征，转换为numpy数组
y_train = train_data['future_ret_1d'].values  # 提取训练集目标变量，转换为numpy数组
X_val = val_data[features].values  # 提取验证集特征，转换为numpy数组
y_val = val_data['future_ret_1d'].values  # 提取验证集目标变量，转换为numpy数组
X_test = test_data[features].values  # 提取测试集特征，转换为numpy数组
y_test = test_data['future_ret_1d'].values  # 提取测试集目标变量，转换为numpy数组

print("训练集特征形状:", X_train.shape)  # 打印训练集特征的形状（样本数和特征数）
print("训练集目标形状:", y_train.shape)  # 打印训练集目标变量的形状（样本数）
print("特征列:", features)  # 打印特征列的名称

# 6.1 训练线性模型
print("\n开始训练线性回归模型...")  # 打印开始训练线性模型的消息

pipeline_lr = Pipeline([  # 创建一个Pipeline对象，用于串联数据处理和模型步骤
    ('lr', LinearRegression())  # 添加线性回归模型步骤，并命名为'lr'
])

param_grid_lr = {  # 创建线性回归模型的参数网格
    'lr__fit_intercept': [True, False]  # 设置是否拟合截距，尝试True和False两种情况
}

best_score_lr = float('-inf')  # 初始化最佳得分为负无穷
best_params_lr = None  # 初始化最佳参数为None
best_pipeline_lr = None  # 初始化最佳Pipeline对象为None

for params in ParameterGrid(param_grid_lr):  # 遍历参数网格中的每一组参数
    pipeline_lr.set_params(**params)  # 设置Pipeline的参数
    pipeline_lr.fit(X_train, y_train)  # 使用训练集拟合模型

    # 在验证集上进行预测和评估
    valid_pred_lr = pipeline_lr.predict(X_val)  # 在验证集上进行预测
    valid_r2_lr = r2_score(y_val, valid_pred_lr)  # 计算R²分数（决定系数）

    if valid_r2_lr > best_score_lr:  # 如果当前分数比最佳分数高
        best_score_lr = valid_r2_lr  # 更新最佳分数
        best_params_lr = params  # 更新最佳参数
        best_pipeline_lr = copy.deepcopy(pipeline_lr)  # 创建当前Pipeline的深拷贝，保存为最佳Pipeline
        print("更新：", best_score_lr, best_params_lr)  # 打印更新消息

print("最佳参数：", best_params_lr)  # 打印最佳参数

# 使用最佳模型在训练集和测试集上评估
y_pred_train_lr = best_pipeline_lr.predict(X_train)  # 在训练集上进行预测
y_pred_test_lr = best_pipeline_lr.predict(X_test)  # 在测试集上进行预测

train_mse_lr = mean_squared_error(y_train, y_pred_train_lr)  # 计算训练集上的均方误差
test_mse_lr = mean_squared_error(y_test, y_pred_test_lr)  # 计算测试集上的均方误差
train_r2_lr = r2_score(y_train, y_pred_train_lr)  # 计算训练集上的R²分数
test_r2_lr = r2_score(y_test, y_pred_test_lr)  # 计算测试集上的R²分数

print("==== 线性模型 - 训练集 ====")  # 打印训练集评估结果标题
print("MSE:", train_mse_lr)  # 打印训练集均方误差
print("R2: ", train_r2_lr)  # 打印训练集R²分数

print("==== 线性模型 - 测试集 ====")  # 打印测试集评估结果标题
print("MSE:", test_mse_lr)  # 打印测试集均方误差
print("R2: ", test_r2_lr)  # 打印测试集R²分数

# 查看训练后的回归系数和截距
print("Coefficients:", best_pipeline_lr.named_steps['lr'].coef_)  # 打印最佳线性模型的系数
print("Intercept:", best_pipeline_lr.named_steps['lr'].intercept_)  # 打印最佳线性模型的截距

# 6.2 训练随机森林
print("\n开始训练随机森林模型...")  # 打印开始训练随机森林模型的消息

pipeline_rf = Pipeline([  # 创建一个Pipeline对象，用于串联数据处理和模型步骤
    ('rf', RandomForestRegressor(random_state=42))  # 添加随机森林回归模型步骤，并命名为'rf'，设置随机种子为42
])

# 简化后的参数网格 (只保留最优参数)
param_grid_rf = {
    'rf__n_estimators': [100],
    'rf__max_depth': [3],
    'rf__min_samples_split': [2],
    'rf__min_samples_leaf': [2]
}

best_score_rf = float('-inf')  # 初始化最佳得分为负无穷
best_params_rf = None  # 初始化最佳参数为None
best_pipeline_rf = None  # 初始化最佳Pipeline对象为None

for params in ParameterGrid(param_grid_rf):  # 遍历参数网格中的每一组参数
    pipeline_rf.set_params(**params)  # 设置Pipeline的参数
    pipeline_rf.fit(X_train, y_train)  # 使用训练集拟合模型

    # 在验证集上进行预测和评估
    valid_pred_rf = pipeline_rf.predict(X_val)  # 在验证集上进行预测
    valid_r2_rf = r2_score(y_val, valid_pred_rf)  # 计算R²分数

    if valid_r2_rf > best_score_rf:  # 如果当前分数比最佳分数高
        best_score_rf = valid_r2_rf  # 更新最佳分数
        best_params_rf = params  # 更新最佳参数
        best_pipeline_rf = copy.deepcopy(pipeline_rf)  # 创建当前Pipeline的深拷贝，保存为最佳Pipeline
        print("更新：", best_score_rf, best_params_rf)  # 打印更新消息

print("最佳参数：", best_params_rf)  # 打印最佳参数

# 使用最佳模型在训练集和测试集上评估
y_pred_train_rf = best_pipeline_rf.predict(X_train)  # 在训练集上进行预测
y_pred_test_rf = best_pipeline_rf.predict(X_test)  # 在测试集上进行预测

train_mse_rf = mean_squared_error(y_train, y_pred_train_rf)  # 计算训练集上的均方误差
test_mse_rf = mean_squared_error(y_test, y_pred_test_rf)  # 计算测试集上的均方误差
train_r2_rf = r2_score(y_train, y_pred_train_rf)  # 计算训练集上的R²分数
test_r2_rf = r2_score(y_test, y_pred_test_rf)  # 计算测试集上的R²分数

print("==== 随机森林 - 训练集 ====")  # 打印训练集评估结果标题
print("MSE:", train_mse_rf)  # 打印训练集均方误差
print("R2 :", train_r2_rf)  # 打印训练集R²分数

print("==== 随机森林 - 测试集 ====")  # 打印测试集评估结果标题
print("MSE:", test_mse_rf)  # 打印测试集均方误差
print("R2 :", test_r2_rf)  # 打印测试集R²分数

# 查看特征重要性
feature_importances = best_pipeline_rf.named_steps['rf'].feature_importances_  # 获取最佳随机森林模型的特征重要性
for f, imp in zip(features, feature_importances):  # 遍历每个特征及其重要性
    print(f"Feature: {f}, Importance: {imp:.4f}")  # 打印特征名称和重要性（保留4位小数）

# 按重要性排序输出
sorted_idx = np.argsort(feature_importances)[::-1]  # 对特征重要性进行降序排序，获取排序后的索引
print("\nSorted Feature Importances:")  # 打印排序后的特征重要性标题
for idx in sorted_idx:  # 遍历排序后的索引
    print(f"{features[idx]} -> {feature_importances[idx]:.4f}")  # 按重要性降序打印特征名称和重要性

# 6.3 训练XGBoost
print("\n开始训练XGBoost模型...")  # 打印开始训练XGBoost模型的消息

pipeline_xgb = Pipeline([  # 创建一个Pipeline对象，用于串联数据处理和模型步骤
    ('xgb', xgb.XGBRegressor(random_state=42, verbosity=0))  # 添加XGBoost回归模型步骤，并命名为'xgb'，设置随机种子为42，关闭详细输出
])

# 简化后的参数网格 (只保留最优参数)
param_grid_xgb = {
    'xgb__n_estimators': [100],
    'xgb__learning_rate': [0.01],
    'xgb__max_depth': [3]
}

best_score_xgb = float('-inf')  # 初始化最佳得分为负无穷
best_params_xgb = None  # 初始化最佳参数为None
best_pipeline_xgb = None  # 初始化最佳Pipeline对象为None

for params in ParameterGrid(param_grid_xgb):  # 遍历参数网格中的每一组参数
    pipeline_xgb.set_params(**params)  # 设置Pipeline的参数
    pipeline_xgb.fit(X_train, y_train)  # 使用训练集拟合模型

    # 在验证集上进行预测和评估
    valid_pred_xgb = pipeline_xgb.predict(X_val)  # 在验证集上进行预测
    valid_r2_xgb = r2_score(y_val, valid_pred_xgb)  # 计算R²分数

    if valid_r2_xgb > best_score_xgb:  # 如果当前分数比最佳分数高
        best_score_xgb = valid_r2_xgb  # 更新最佳分数
        best_params_xgb = params  # 更新最佳参数
        best_pipeline_xgb = copy.deepcopy(pipeline_xgb)  # 创建当前Pipeline的深拷贝，保存为最佳Pipeline
        print("更新：", best_score_xgb, best_params_xgb)  # 打印更新消息

print("最佳参数：", best_params_xgb)  # 打印最佳参数

# 使用最佳模型在训练集和测试集上评估
y_pred_train_xgb = best_pipeline_xgb.predict(X_train)  # 在训练集上进行预测
y_pred_test_xgb = best_pipeline_xgb.predict(X_test)  # 在测试集上进行预测

train_mse_xgb = mean_squared_error(y_train, y_pred_train_xgb)  # 计算训练集上的均方误差
test_mse_xgb = mean_squared_error(y_test, y_pred_test_xgb)  # 计算测试集上的均方误差
train_r2_xgb = r2_score(y_train, y_pred_train_xgb)  # 计算训练集上的R²分数
test_r2_xgb = r2_score(y_test, y_pred_test_xgb)  # 计算测试集上的R²分数

print("==== XGBoost - 训练集 ====")  # 打印训练集评估结果标题
print("MSE:", train_mse_xgb)  # 打印训练集均方误差
print("R2 :", train_r2_xgb)  # 打印训练集R²分数

print("==== XGBoost - 测试集 ====")  # 打印测试集评估结果标题
print("MSE:", test_mse_xgb)  # 打印测试集均方误差
print("R2 :", test_r2_xgb)  # 打印测试集R²分数

# 查看特征重要性
feature_importances_xgb = best_pipeline_xgb.named_steps['xgb'].feature_importances_  # 获取最佳XGBoost模型的特征重要性
for f, imp in zip(features, feature_importances_xgb):  # 遍历每个特征及其重要性
    print(f"Feature: {f}, Importance: {imp:.4f}")  # 打印特征名称和重要性（保留4位小数）

# 按重要性排序输出
sorted_idx_xgb = np.argsort(feature_importances_xgb)[::-1]  # 对特征重要性进行降序排序，获取排序后的索引
print("\nSorted Feature Importances (XGBoost):")  # 打印排序后的特征重要性标题
for idx in sorted_idx_xgb:  # 遍历排序后的索引
    print(f"{features[idx]} -> {feature_importances_xgb[idx]:.4f}")  # 按重要性降序打印特征名称和重要性

# 6.4 训练MLP
print("\n开始训练MLP模型...")  # 打印开始训练MLP模型的消息

pipeline = Pipeline([  # 创建一个Pipeline对象，用于串联数据处理和模型步骤
    ('scaler', StandardScaler()),  # 添加标准化步骤，并命名为'scaler'
    ('mlp', MLPRegressor(random_state=42, max_iter=1000))  # 添加MLP回归模型步骤，并命名为'mlp'，设置随机种子为42，最大迭代次数为1000
])

# 简化后的参数网格 (只保留最优参数)
param_grid_mlp = {
    'mlp__hidden_layer_sizes': [(64, 32)],
    'mlp__alpha': [0.01],
    'mlp__learning_rate_init': [0.01]
}

best_score = float('-inf')  # 初始化最佳得分为负无穷
best_params = None  # 初始化最佳参数为None
best_pipeline_mlp = None  # 初始化最佳Pipeline对象为None

for params in ParameterGrid(param_grid_mlp):  # 遍历参数网格中的每一组参数
    pipeline.set_params(**params)  # 设置Pipeline的参数
    try:  # 尝试执行以下代码块
        pipeline.fit(X_train, y_train)  # 使用训练集拟合模型
        
        # 在验证集上进行预测和评估
        valid_pred = pipeline.predict(X_val)  # 在验证集上进行预测
        valid_r2 = r2_score(y_val, valid_pred)  # 计算R²分数
        
        if valid_r2 > best_score:  # 如果当前分数比最佳分数高
            best_score = valid_r2  # 更新最佳分数
            best_params = params  # 更新最佳参数
            best_pipeline_mlp = copy.deepcopy(pipeline)  # 创建当前Pipeline的深拷贝，保存为最佳Pipeline
            print("更新：", best_score, best_params)  # 打印更新消息
    except Exception as e:  # 如果try代码块出现异常，则执行except代码块
        print(f"MLP训练出错: {e}, 参数: {params}")  # 打印错误消息、异常信息和当前参数
        continue  # 继续循环的下一次迭代

if best_pipeline_mlp is not None:  # 如果找到了有效的MLP模型
    print("最佳参数:", best_params)  # 打印最佳参数
    
    # 使用最优模型在训练集和测试集上评估
    y_pred_train_mlp = best_pipeline_mlp.predict(X_train)  # 在训练集上进行预测
    y_pred_test_mlp = best_pipeline_mlp.predict(X_test)  # 在测试集上进行预测
    
    train_mse_mlp = mean_squared_error(y_train, y_pred_train_mlp)  # 计算训练集上的均方误差
    test_mse_mlp = mean_squared_error(y_test, y_pred_test_mlp)  # 计算测试集上的均方误差
    train_r2_mlp = r2_score(y_train, y_pred_train_mlp)  # 计算训练集上的R²分数
    test_r2_mlp = r2_score(y_test, y_pred_test_mlp)  # 计算测试集上的R²分数
    
    print("==== MLP - 训练集 ====")  # 打印训练集评估结果标题
    print("MSE:", train_mse_mlp)  # 打印训练集均方误差
    print("R2: ", train_r2_mlp)  # 打印训练集R²分数
    
    print("==== MLP - 测试集 ====")  # 打印测试集评估结果标题
    print("MSE:", test_mse_mlp)  # 打印测试集均方误差
    print("R2: ", test_r2_mlp)  # 打印测试集R²分数
else:  # 如果没有找到有效的MLP模型
    print("MLP模型训练失败，尝试使用默认参数")  # 打印训练失败消息
    
    best_pipeline_mlp = Pipeline([  # 创建一个使用默认参数的Pipeline对象
        ('scaler', StandardScaler()),  # 添加标准化步骤，并命名为'scaler'
        ('mlp', MLPRegressor(random_state=42, max_iter=1000))  # 添加MLP回归模型步骤，并命名为'mlp'，使用默认参数
    ])
    
    try:  # 尝试执行以下代码块
        best_pipeline_mlp.fit(X_train, y_train)  # 使用训练集拟合模型
        y_pred_train_mlp = best_pipeline_mlp.predict(X_train)  # 在训练集上进行预测
        y_pred_test_mlp = best_pipeline_mlp.predict(X_test)  # 在测试集上进行预测
        
        train_mse_mlp = mean_squared_error(y_train, y_pred_train_mlp)  # 计算训练集上的均方误差
        test_mse_mlp = mean_squared_error(y_test, y_pred_test_mlp)  # 计算测试集上的均方误差
        train_r2_mlp = r2_score(y_train, y_pred_train_mlp)  # 计算训练集上的R²分数
        test_r2_mlp = r2_score(y_test, y_pred_test_mlp)  # 计算测试集上的R²分数
        
        print("==== 默认MLP - 训练集 ====")  # 打印训练集评估结果标题
        print("MSE:", train_mse_mlp)  # 打印训练集均方误差
        print("R2: ", train_r2_mlp)  # 打印训练集R²分数
        
        print("==== 默认MLP - 测试集 ====")  # 打印测试集评估结果标题
        print("MSE:", test_mse_mlp)  # 打印测试集均方误差
        print("R2: ", test_r2_mlp)  # 打印测试集R²分数
    except Exception as e:  # 如果try代码块出现异常，则执行except代码块
        print(f"默认MLP模型训练也失败: {e}")  # 打印错误消息和异常信息

# ——————————————————————————————————————————————————————————————————————————————
# 7. 模型集成与权重优化

print("\n开始进行模型集成与权重优化...")  # 打印开始模型集成与权重优化的消息
try:  # 尝试执行以下代码块
    # 导入cvxpy
    import cvxpy as cp  # 导入cvxpy库，用于解决凸优化问题，别名为cp

    def optimize_weights_constrained(  # 定义一个函数，用于优化模型权重
            models,  # 模型列表参数
            X_val,  # 验证集特征参数
            y_val,  # 验证集目标参数
            sum_to_1=True,  # 是否约束权重和为1的参数，默认为True
            nonnegative=True,  # 是否约束权重非负的参数，默认为True
            alpha_l1=0.0,  # L1正则化系数参数，默认为0
            alpha_l2=0.0,  # L2正则化系数参数，默认为0
            verbose=True  # 是否打印详细信息的参数，默认为True
    ):
        """
        用凸优化方式在验证集上寻找一组最优权重，使得加权后的预测最小化 MSE
        （或等效地最大化 R²），并可选地加入 L1/L2 正则，还可选地约束权重和=1、权重>=0。
        
        参数：
        - models: 传入已训练好的各个模型列表
        - X_val, y_val: 验证集特征和目标
        - sum_to_1: Boolean, 若为 True，则加上 sum(w) == 1 的约束
        - nonnegative: Boolean, 若为 True，则加上 w >= 0 的约束
        - alpha_l1, alpha_l2: L1、L2 正则化系数
        - verbose: 是否打印约束求解的一些信息
        
        返回：
        - w_opt: 优化得到的权重
        """
        # 1) 先得到在验证集上的预测矩阵 predictions: shape (N, M)  # 获取每个模型在验证集上的预测结果，并将它们按列堆叠成一个矩阵
        predictions = np.column_stack([model.predict(X_val) for model in models])  # 每一列对应一个模型的预测结果
        N, M = predictions.shape  # 获取矩阵的形状：N是样本数量，M是模型数量

        # 2) 定义优化变量 w: 大小 M  # 定义权重变量，是要优化的目标
        if nonnegative:  # 如果需要非负约束
            w = cp.Variable(M, nonneg=True)  # 创建一个大小为M的非负变量
        else:  # 如果不需要非负约束
            w = cp.Variable(M)  # 创建一个大小为M的变量，没有非负约束

        # 3) 定义约束列表 constraints  # 设置优化问题的约束条件
        constraints = []  # 初始化一个空的约束列表
        if sum_to_1:  # 如果需要权重和为1的约束
            constraints.append(cp.sum(w) == 1)  # 添加权重和为1的约束

        # 4) 定义目标函数（最小化 MSE + 正则项）  # 设置优化的目标函数
        residual = y_val - predictions @ w  # 计算残差：真实值减去预测值的加权和
        obj_mse = cp.sum_squares(residual)  # 计算均方误差（MSE）

        # 若要加 L1 正则：alpha_l1 * ||w||_1  # L1正则化可以促使权重稀疏
        # 若要加 L2 正则：alpha_l2 * ||w||_2^2  # L2正则化可以防止权重过大
        obj_reg = 0  # 初始化正则化项为0
        if alpha_l1 > 0:  # 如果L1正则化系数大于0
            obj_reg += alpha_l1 * cp.norm1(w)  # 添加L1正则化项
        if alpha_l2 > 0:  # 如果L2正则化系数大于0
            obj_reg += alpha_l2 * cp.norm2(w) ** 2  # 添加L2正则化项

        # 最终目标：Minimize(MSE + 正则)  # 定义最终的优化目标
        objective = cp.Minimize(obj_mse + obj_reg)  # 最小化MSE和正则化项的和

        # 5) 构建并求解凸优化问题  # 创建优化问题并求解
        problem = cp.Problem(objective, constraints)  # 用目标函数和约束创建优化问题
        result = problem.solve(verbose=verbose)  # 求解优化问题，verbose控制是否输出详细信息

        # 6) 拿到最优权重 w_opt  # 获取优化结果
        w_opt = w.value  # 获取最优权重值
        # 计算该组合在验证集上的 r2_score  # 评估权重的性能
        y_val_pred = predictions @ w_opt  # 计算加权后的预测值
        score_r2 = r2_score(y_val, y_val_pred)  # 计算R²分数

        if verbose:  # 如果需要输出详细信息
            print(f"Optimal objective (MSE + reg) = {problem.value:.6f}")  # 打印最优目标函数值
            print("Optimized weights:", w_opt)  # 打印最优权重
            print(f"sum of weights = {w_opt.sum():.4f}")  # 打印权重之和
            print(f"R2 on validation set = {score_r2:.4f}")  # 打印验证集上的R²分数

        return w_opt, score_r2  # 返回最优权重和对应的R²分数

    # 执行权重优化  # 调用上面定义的函数进行优化
    w_constrained, r2_constrained = optimize_weights_constrained(  # 存储返回的权重和R²分数
        models=[best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb, best_pipeline_mlp],  # 传入训练好的各个模型
        X_val=X_val,  # 传入验证集特征
        y_val=y_val,  # 传入验证集目标
        sum_to_1=True,  # 约束权重和为1
        nonnegative=True,  # 约束权重非负
        alpha_l1=0.0,  # 不使用L1正则化
        alpha_l2=1e-4,  # 使用小量的L2正则化
        verbose=True  # 输出详细信息
    )

    print("得到的约束权重 =", [f"{num:0.2f}" for num in w_constrained])  # 打印优化得到的权重（格式化为两位小数）
    print("验证集 R² =", r2_constrained)  # 打印验证集上的R²分数

    # 测试集上的集成模型性能  # 在测试集上评估模型集成的性能
    predictions_test = np.column_stack([model.predict(X_test) for model in  # 获取各模型在测试集上的预测结果
                                      [best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb, best_pipeline_mlp]])
    
    y_test_pred = predictions_test @ w_constrained  # 使用优化得到的权重计算集成模型的预测值
    r2_test = r2_score(y_test, y_test_pred)  # 计算测试集上的R²分数
    print("测试集 R² =", r2_test)  # 打印测试集上的R²分数

except Exception as e:  # 捕获可能发生的异常
    print(f"模型集成与权重优化出错: {e}")  # 打印错误信息
    # 如果出错，使用简单的平均权重  # 提供备选方案
    print("使用平均权重作为备选方案")  # 打印备选方案信息
    w_constrained = np.array([0.25, 0.25, 0.25, 0.25])  # 设置均等权重
    
    # 测试集上的集成模型性能  # 使用均等权重在测试集上评估模型集成性能
    predictions_test = np.column_stack([model.predict(X_test) for model in  # 获取各模型在测试集上的预测结果
                                      [best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb, best_pipeline_mlp]])
    
    y_test_pred = predictions_test @ w_constrained  # 使用均等权重计算集成模型的预测值
    r2_test = r2_score(y_test, y_test_pred)  # 计算测试集上的R²分数
    print("测试集平均权重 R² =", r2_test)  # 打印测试集上使用均等权重的R²分数

# ——————————————————————————————————————————————————————————————————————————————
# 8. Ensemble策略实现与回测  # 开始实现集成策略并进行回测

# 自定义成交量指标，把成交量数据单独显示在子图中  # 定义一个自定义指标类，用于在回测图表中显示成交量
class MyVolumeIndicator(bt.Indicator):  # 继承自backtrader的Indicator类
    """
    简单示例，把data的volume包装成一个单独的子图指标。  # 类文档字符串，说明类的用途
    """
    lines = ('vol',)  # 定义一个名为'vol'的线
    plotinfo = dict(subplot=True, plotname='Volume')  # 设置绘图信息：在单独的子图中显示，名称为'Volume'

    def __init__(self):  # 初始化方法
        self.lines.vol = self.data.volume  # 将数据源的volume赋值给自定义的vol线

class MLEnsembleStrategy(bt.Strategy):  # 定义一个机器学习集成策略类，继承自backtrader的Strategy类
    params = (  # 设置策略参数
        ('target_percent', 0.95),  # 目标仓位百分比，默认为95%
    )

    def __init__(self, models, weights):  # 初始化方法，接收模型列表和权重
        self.models = models  # 存储模型列表
        self.weights = weights  # 存储权重

        # 关闭主图中Data自带的Volume绘制  # 控制图表显示
        self.data.plotinfo.plotvolume = False  # 关闭默认的成交量显示

        # 自定义成交量指标以及其SMA指标  # 添加自定义的成交量指标和均线
        self.myvol = MyVolumeIndicator(self.data)  # 创建自定义成交量指标
        self.vol_5 = bt.indicators.SMA(self.myvol.vol, period=5)  # 创建5日成交量均线
        self.vol_5.plotinfo.subplot = True  # 设置5日均线在子图中显示
        self.vol_10 = bt.indicators.SMA(self.myvol.vol, period=10)  # 创建10日成交量均线
        self.vol_10.plotinfo.subplot = True  # 设置10日均线在子图中显示

        # 添加其它因子指标  # 计算策略所需的技术指标
        # 价格动量指标：计算5日价格百分比变化  # 计算价格动量
        self.momentum_5 = bt.indicators.PercentChange(self.data.close, period=5)  # 创建5日价格变化百分比指标

        # RSI指标，14日周期  # 计算相对强弱指数
        self.rsi_14 = bt.indicators.RSI(self.data.close, period=14)  # 创建14日RSI指标

        # 布林带指标，默认20日均线和2倍标准差，返回上轨、均线和下轨  # 计算布林带
        self.bb = bt.indicators.BollingerBands(self.data.close)  # 创建布林带指标

        self.last_trade_type = None  # 记录上一次交易类型（buy/sell）  # 初始化最近交易类型为空

        self.value_history_dates = []  # 初始化日期记录列表
        self.value_history_values = []  # 初始化账户价值记录列表

    def next(self):  # 策略的核心方法，每个时间步都会被调用
        # 计算各个因子的当前值  # 获取当前时间步的各个因子值
        momentum = self.momentum_5[0]  # 获取当前的动量值
        vol_ratio = (self.vol_5[0] / self.vol_10[0] - 1) if self.vol_10[0] != 0 else 0  # 计算成交量比率，避免除以零
        rsi = self.rsi_14[0]  # 获取当前的RSI值
        bb_upper = self.bb.top[0]  # 获取布林带上轨
        bb_lower = self.bb.bot[0]  # 获取布林带下轨

        # 构建特征向量：注意顺序需要与模型训练时一致  # 准备输入特征
        X = [[momentum, vol_ratio, rsi, bb_upper, bb_lower]]  # 创建特征向量，顺序必须与训练时一致

        try:  # 使用try-except处理可能的错误
            # 获取各模型的预测值  # 使用模型进行预测
            predictions = np.array([model.predict(X)[0] for model in self.models])  # 获取每个模型的预测值
            
            # 加权平均得到集成预测  # 计算加权平均预测
            pred_ret = np.sum(predictions * self.weights)  # 使用优化得到的权重计算加权预测值
            
            # 获取当前持仓状态  # 检查当前持仓
            current_position = self.getposition().size  # 获取当前持仓量
            
            if pred_ret > 0 and current_position == 0:  # 如果预测收益为正且当前没有持仓
                # 只有当当前没有仓位时，才执行买入  # 执行买入操作
                self.order_target_percent(target=self.p.target_percent)  # 按照目标百分比买入
                self.last_trade_type = "BUY"  # 记录交易类型为买入
                print(f"{self.datas[0].datetime.date(0)} => BUY signal, pred_ret={pred_ret:.6f}")  # 打印买入信号
            
            elif pred_ret <= 0 and current_position > 0:  # 如果预测收益为负且当前有持仓
                # 只有当当前有仓位时，才执行卖出  # 执行卖出操作
                self.order_target_percent(target=0.0)  # 清空仓位
                self.last_trade_type = "SELL"  # 记录交易类型为卖出
                print(f"{self.datas[0].datetime.date(0)} => SELL signal, pred_ret={pred_ret:.6f}")  # 打印卖出信号
            
            # 只在交易执行时打印仓位信息  # 仅在有交易时显示信息
            if self.last_trade_type:  # 如果有交易类型记录
                print(f"Current position size: {self.getposition().size}, Value: {self.broker.getvalue()}")  # 打印持仓量和账户价值
        
        except Exception as e:  # 捕获可能发生的异常
            print(f"策略执行出错: {e}")  # 打印错误信息

        # 记录历史价值  # 记录每个时间步的账户价值
        dt = self.data.datetime.date(0)  # 获取当前日期
        self.value_history_dates.append(dt)  # 添加日期到记录列表
        self.value_history_values.append(self.broker.getvalue())  # 添加账户价值到记录列表

print("\n开始执行集成策略回测...")  # 打印开始回测的消息
try:  # 使用try-except处理可能的错误
    ml_ensemble_result, ml_ensemble_cerebro = run_backtest(  # 调用回测函数并保存结果
        ticker=ticker,  # 设置股票代码
        df=test_data,  # 设置测试数据
        start_date=test_data.index[0].to_pydatetime(),  # 设置回测开始日期
        end_date=test_data.index[-1].to_pydatetime(),  # 设置回测结束日期
        strategy=MLEnsembleStrategy,  # 设置回测策略
        initial_cash=100000,  # 设置初始资金
        strategy_params={  # 设置策略参数
            'models': [best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb, best_pipeline_mlp],  # 传入模型列表
            'weights': w_constrained,  # 传入优化得到的权重
            'target_percent': 0.95,  # 设置目标仓位百分比
        },
        print_log=True,  # 打印日志
    )
    print("集成策略回测完成")  # 打印回测完成消息
except Exception as e:  # 捕获可能发生的异常
    print(f"集成策略回测出错: {e}")  # 打印错误信息
    raise  # 重新抛出异常，中断程序

# ===== 猴子补丁：为 numpy 添加 bool8 和 object 属性 =====  # 为兼容性添加补丁
if not hasattr(np, 'bool8'):  # 如果numpy没有bool8属性
    np.bool8 = np.bool_  # 添加bool8属性，指向bool_

try:
    plot_results(ml_ensemble_cerebro)
    print("集成策略结果绘图完成")
except Exception as e:
    print(f"集成策略绘图出错: {e}")

# 打印回测结果包含的键
print("回测结果包含的键:", ml_ensemble_result.keys())  # 打印回测结果对象中包含的所有键名，帮助我们了解可访问的数据项

# 计算策略性能指标
print("\n计算策略性能指标...")  # 打印标题，表示开始计算性能指标

# 根据实际键名获取数据
# 可能需要根据实际打印出的键名调整下面的代码
initial_value = ml_ensemble_result.get('initial_value', ml_ensemble_cerebro.broker.startingcash)  # 获取初始资金，如果键不存在则使用cerebro对象中的初始资金
final_value = ml_ensemble_result.get('final_value', ml_ensemble_cerebro.broker.getvalue())  # 获取最终资金，如果键不存在则使用cerebro对象中当前的资金价值

# 比较策略和Buy&Hold
try:  # 使用try-except结构捕获可能的错误
    results = ml_ensemble_cerebro.run()  # 运行集成策略的cerebro对象并获取结果
    ml_strategy_instance = results[0]  # 获取第一个（通常是唯一的）策略实例
    
    results_bh = bh_cerebro.run()  # 运行Buy&Hold策略的cerebro对象并获取结果
    bh_strategy_instance = results_bh[0]  # 获取第一个（通常是唯一的）策略实例
    
    plt.figure(figsize=(12, 6))  # 创建一个宽12英寸、高6英寸的图形
    plt.plot(ml_strategy_instance.value_history_dates, ml_strategy_instance.value_history_values, label='集成模型')  # 绘制集成模型策略的资产价值曲线
    plt.plot(bh_strategy_instance.value_history_dates, bh_strategy_instance.value_history_values, label='买入并持有')  # 绘制Buy&Hold策略的资产价值曲线
    plt.xlabel('时间')  # 设置x轴标签
    plt.ylabel('资产净值')  # 设置y轴标签
    plt.title('回报曲线对比')  # 设置图表标题
    plt.legend()  # 显示图例
    plt.show()  # 显示图形

    # 年化收益率
    def annualized_return(start_value, end_value, days):  # 定义计算年化收益率的函数
        """计算年化收益率"""  # 函数文档字符串，说明函数用途
        years = days / 365  # 将天数转换为年数
        return (end_value / start_value) ** (1/years) - 1  # 计算并返回年化收益率

    # 最大回撤
    def max_drawdown(values):  # 定义计算最大回撤的函数
        """计算最大回撤"""  # 函数文档字符串，说明函数用途
        # 转换为pandas Series以便使用cummax
        if not isinstance(values, pd.Series):  # 检查输入是否为pandas Series类型
            values = pd.Series(values)  # 如果不是，则转换为pandas Series类型
        # 计算历史新高
        roll_max = values.cummax()  # 计算截至每个时点的历史最高价值
        # 计算回撤
        drawdown = (values / roll_max - 1)  # 计算每个时点的回撤百分比（当前价值与历史最高价值的百分比差）
        # 返回最大回撤
        return drawdown.min()  # 返回最大回撤（最小的回撤值）
    
    # 计算集成策略的性能指标
    ml_start_value = ml_strategy_instance.value_history_values[0]  # 获取集成策略的起始资金
    ml_end_value = ml_strategy_instance.value_history_values[-1]  # 获取集成策略的最终资金
    ml_days = (ml_strategy_instance.value_history_dates[-1] - ml_strategy_instance.value_history_dates[0]).days  # 计算策略运行的天数
    ml_annual_return = annualized_return(ml_start_value, ml_end_value, ml_days)  # 计算集成策略的年化收益率
    ml_max_drawdown = max_drawdown(ml_strategy_instance.value_history_values)  # 计算集成策略的最大回撤
    
    # 计算买入持有策略的性能指标
    bh_start_value = bh_strategy_instance.value_history_values[0]  # 获取Buy&Hold策略的起始资金
    bh_end_value = bh_strategy_instance.value_history_values[-1]  # 获取Buy&Hold策略的最终资金
    bh_days = (bh_strategy_instance.value_history_dates[-1] - bh_strategy_instance.value_history_dates[0]).days  # 计算策略运行的天数
    bh_annual_return = annualized_return(bh_start_value, bh_end_value, bh_days)  # 计算Buy&Hold策略的年化收益率
    bh_max_drawdown = max_drawdown(bh_strategy_instance.value_history_values)  # 计算Buy&Hold策略的最大回撤
    
    # 打印性能指标对比
    print("====== 性能指标对比 ======")  # 打印标题
    print(f"{'指标':<15}{'集成策略':>15}{'买入持有':>15}")  # 打印表头，左对齐指标名，右对齐数值列
    print(f"{'-'*45}")  # 打印分隔线
    print(f"{'起始资金':<15}{ml_start_value:>15.2f}{bh_start_value:>15.2f}")  # 打印起始资金，保留2位小数
    print(f"{'最终资金':<15}{ml_end_value:>15.2f}{bh_end_value:>15.2f}")  # 打印最终资金，保留2位小数
    print(f"{'总收益率':<15}{(ml_end_value/ml_start_value-1)*100:>14.2f}%{(bh_end_value/bh_start_value-1)*100:>14.2f}%")  # 打印总收益率，转换为百分比并保留2位小数
    print(f"{'年化收益率':<15}{ml_annual_return*100:>14.2f}%{bh_annual_return*100:>14.2f}%")  # 打印年化收益率，转换为百分比并保留2位小数
    print(f"{'最大回撤':<15}{ml_max_drawdown*100:>14.2f}%{bh_max_drawdown*100:>14.2f}%")  # 打印最大回撤，转换为百分比并保留2位小数
    
    # 计算夏普比率（假设无风险利率为0，简化计算）
    ml_returns = pd.Series(ml_strategy_instance.value_history_values).pct_change().dropna()  # 计算集成策略的每日收益率序列，并删除缺失值
    bh_returns = pd.Series(bh_strategy_instance.value_history_values).pct_change().dropna()  # 计算Buy&Hold策略的每日收益率序列，并删除缺失值
    
    # 计算夏普比率
    risk_free_rate = 0.02  # 年化无风险利率2%
    daily_rf = risk_free_rate / 252
    excess_returns = ml_returns - daily_rf
    ml_sharpe = np.sqrt(252) * excess_returns.mean() / excess_returns.std() # 计算集成策略的夏普比率，年化参数为252个交易日，理论上应该减去无风险利率（如美债年化2%/252），但对日内/短线策略影响很小。
    bh_sharpe = np.sqrt(252) * bh_returns.mean() / bh_returns.std() if bh_returns.std() > 0 else 0  # 计算Buy&Hold策略的夏普比率，年化参数为252个交易日，如果标准差为0则返回0
    
    print(f"{'夏普比率':<15}{ml_sharpe:>15.2f}{bh_sharpe:>15.2f}")  # 打印夏普比率，保留2位小数
    
    # 计算胜率
    ml_returns_pos = (ml_returns > 0).sum()  # 计算集成策略中正收益天数
    ml_returns_neg = (ml_returns < 0).sum()  # 计算集成策略中负收益天数
    ml_win_rate = ml_returns_pos / (ml_returns_pos + ml_returns_neg) if (ml_returns_pos + ml_returns_neg) > 0 else 0  # 计算胜率，如果总天数为0则返回0
    
    bh_returns_pos = (bh_returns > 0).sum()  # 计算Buy&Hold策略中正收益天数
    bh_returns_neg = (bh_returns < 0).sum()  # 计算Buy&Hold策略中负收益天数
    bh_win_rate = bh_returns_pos / (bh_returns_pos + bh_returns_neg) if (bh_returns_pos + bh_returns_neg) > 0 else 0  # 计算胜率，如果总天数为0则返回0
    
    print(f"{'胜率':<15}{ml_win_rate*100:>14.2f}%{bh_win_rate*100:>14.2f}%")  # 打印胜率，转换为百分比并保留2位小数
    
    # 计算月度收益
    # 转换日期为datetime格式
    ml_dates = [pd.Timestamp(date) if not isinstance(date, pd.Timestamp) else date for date in ml_strategy_instance.value_history_dates]  # 确保所有日期都是pandas Timestamp类型
    bh_dates = [pd.Timestamp(date) if not isinstance(date, pd.Timestamp) else date for date in bh_strategy_instance.value_history_dates]  # 确保所有日期都是pandas Timestamp类型
    
    ml_monthly = pd.DataFrame({'date': ml_dates, 'value': ml_strategy_instance.value_history_values})  # 创建包含日期和价值的DataFrame
    ml_monthly['year_month'] = ml_monthly['date'].dt.strftime('%Y-%m')  # 添加年月列，格式为YYYY-MM
    ml_monthly = ml_monthly.groupby('year_month')['value'].last() / ml_monthly.groupby('year_month')['value'].first() - 1  # 计算每月收益率：月末价值/月初价值-1
    
    bh_monthly = pd.DataFrame({'date': bh_dates, 'value': bh_strategy_instance.value_history_values})  # 创建包含日期和价值的DataFrame
    bh_monthly['year_month'] = bh_monthly['date'].dt.strftime('%Y-%m')  # 添加年月列，格式为YYYY-MM
    bh_monthly = bh_monthly.groupby('year_month')['value'].last() / bh_monthly.groupby('year_month')['value'].first() - 1  # 计算每月收益率：月末价值/月初价值-1
    
    # 绘制滚动收益曲线
    try:  # 使用try-except结构捕获可能的错误
        # 创建每日收益率序列
        ml_daily_returns = pd.Series(data=ml_returns.values, index=ml_dates[1:])  # 创建集成策略的每日收益率序列，使用日期作为索引
        bh_daily_returns = pd.Series(data=bh_returns.values, index=bh_dates[1:])  # 创建Buy&Hold策略的每日收益率序列，使用日期作为索引
        
        # 计算滚动60天收益
        rolling_window = 60  # 设置滚动窗口为60天
        ml_rolling = ml_daily_returns.rolling(rolling_window).sum()  # 计算集成策略的60天滚动总收益
        bh_rolling = bh_daily_returns.rolling(rolling_window).sum()  # 计算Buy&Hold策略的60天滚动总收益
        
        plt.figure(figsize=(12, 6))  # 创建一个宽12英寸、高6英寸的图形
        plt.plot(ml_rolling.index, ml_rolling, label=f'集成策略 ({rolling_window}日滚动收益)')  # 绘制集成策略的滚动收益曲线
        plt.plot(bh_rolling.index, bh_rolling, label=f'买入持有 ({rolling_window}日滚动收益)')  # 绘制Buy&Hold策略的滚动收益曲线
        plt.axhline(y=0, color='r', linestyle='-', alpha=0.3)  # 添加一条y=0的水平线，红色，半透明
        plt.title(f'{rolling_window}日滚动收益率对比')  # 设置图表标题
        plt.xlabel('日期')  # 设置x轴标签
        plt.ylabel('滚动收益率')  # 设置y轴标签
        plt.legend()  # 显示图例
        plt.grid(True)  # 显示网格线
        plt.tight_layout()  # 调整布局，使所有元素都能正确显示
        plt.show()  # 显示图形
    except Exception as e:  # 捕获可能发生的异常
        print(f"绘制滚动收益曲线出错: {e}")  # 打印错误信息
    
    # 分析模型预测准确率
    try:  # 使用try-except结构捕获可能的错误
        # 使用集成模型对测试集进行预测
        ensemble_predictions = y_test_pred  # 获取之前计算的测试集预测值
        # 预测方向是否正确
        correct_direction = (ensemble_predictions > 0) == (y_test > 0)  # 比较预测收益率和真实收益率的符号是否相同
        accuracy = correct_direction.mean()  # 计算方向预测的准确率
        
        print(f"\n预测方向准确率: {accuracy*100:.2f}%")  # 打印方向预测准确率，转换为百分比并保留2位小数
        
        # 绘制预测值与实际值对比散点图
        plt.figure(figsize=(10, 6))  # 创建一个宽10英寸、高6英寸的图形
        plt.scatter(y_test, ensemble_predictions, alpha=0.5)  # 绘制散点图，x轴为实际值，y轴为预测值，半透明
        plt.plot([-0.1, 0.1], [-0.1, 0.1], 'r--')  # 添加一条45度线（y=x），红色虚线
        plt.xlabel('实际收益率')  # 设置x轴标签
        plt.ylabel('预测收益率')  # 设置y轴标签
        plt.title('集成模型预测 vs 实际收益率')  # 设置图表标题
        plt.grid(True)  # 显示网格线
        plt.tight_layout()  # 调整布局，使所有元素都能正确显示
        plt.show()  # 显示图形
    except Exception as e:  # 捕获可能发生的异常
        print(f"分析模型预测准确率出错: {e}")  # 打印错误信息
    
    # 分析各个模型的贡献
    print("\n各模型对集成的贡献:")  # 打印标题
    for i, model_name in enumerate(['线性回归', '随机森林', 'XGBoost', 'MLP']):  # 遍历模型名称和对应的索引
        print(f"{model_name}: {w_constrained[i]:.4f} ({w_constrained[i]*100:.2f}%)")  # 打印每个模型的权重和百分比
    
    # 总结与结论
    print("\n====== 总结与结论 ======")  # 打印标题
    if ml_end_value > bh_end_value:  # 如果集成策略的最终资金大于Buy&Hold策略
        performance = "优于"  # 设置性能比较结果为"优于"
        margin = (ml_end_value / bh_end_value - 1) * 100  # 计算优势幅度（百分比）
    else:  # 如果集成策略的最终资金小于等于Buy&Hold策略
        performance = "弱于"  # 设置性能比较结果为"弱于"
        margin = (bh_end_value / ml_end_value - 1) * 100  # 计算劣势幅度（百分比）
    
    print(f"在测试期间，集成模型策略{performance}买入持有策略 {margin:.2f}%。")  # 打印性能比较结果和幅度
    
    if ml_max_drawdown > bh_max_drawdown:  # 如果集成策略的最大回撤大于Buy&Hold策略
        print(f"集成模型策略的最大回撤({ml_max_drawdown*100:.2f}%)大于买入持有策略({bh_max_drawdown*100:.2f}%)，波动性更高。")  # 打印回撤比较结果
    else:  # 如果集成策略的最大回撤小于等于Buy&Hold策略
        print(f"集成模型策略的最大回撤({ml_max_drawdown*100:.2f}%)小于买入持有策略({bh_max_drawdown*100:.2f}%)，波动性更低。")  # 打印回撤比较结果
    
    if ml_sharpe > bh_sharpe:  # 如果集成策略的夏普比率大于Buy&Hold策略
        print(f"集成模型策略的夏普比率({ml_sharpe:.2f})高于买入持有策略({bh_sharpe:.2f})，风险调整后收益更好。")  # 打印夏普比率比较结果
    else:  # 如果集成策略的夏普比率小于等于Buy&Hold策略
        print(f"集成模型策略的夏普比率({ml_sharpe:.2f})低于买入持有策略({bh_sharpe:.2f})，风险调整后收益较差。")  # 打印夏普比率比较结果
    
    print("\n主要贡献模型:", end=" ")  # 打印标题，不换行
    max_weight_index = np.argmax(w_constrained)  # 找出权重最大的模型的索引
    model_names = ['线性回归', '随机森林', 'XGBoost', 'MLP']  # 模型名称列表
    print(f"{model_names[max_weight_index]} (权重: {w_constrained[max_weight_index]:.4f})")  # 打印权重最大的模型及其权重
    
    print("\n模型可能的改进方向:")  # 打印标题
    print("1. 增加更多技术指标和因子")  # 打印改进建议1
    print("2. 尝试不同的特征工程方法")  # 打印改进建议2
    print("3. 增加更复杂的机器学习模型")  # 打印改进建议3
    print("4. 优化超参数搜索空间")  # 打印改进建议4
    print("5. 尝试不同的交易策略阈值")  # 打印改进建议5

except Exception as e:  # 捕获可能发生的异常
    print(f"结果分析与绘图过程出错: {e}")  # 打印错误信息

print("\n====== 程序执行完成 ======")  # 打印程序执行完成的消息
