# Ensemble 模型策略
# 本notebook将整合之前实现的各种模型，构建一个基于ensemble模型的策略。主要步骤包括：

# 1. 数据获取与预处理
# 2. 特征工程（技术指标构建）
# 3. 数据集划分（训练集、验证集、测试集）
# 4. 模型集成：
#    4.1 线性回归（Day1）
#    4.2 随机森林（Day2）
#    4.3 XGBoost（Day3）
#    4.4 MLP（Day4）
# 5. 模型权重优化
# 6. 策略回测与评估

# ——————————————————————————————————————————————————————————————————————————————

# 0. 导入依赖包
import numpy as np 
import pandas as pd  
from sklearn.linear_model import LinearRegression 
from sklearn.metrics import mean_squared_error  
import matplotlib.pyplot as plt  
import seaborn as sns  
from datetime import datetime, timedelta  
import os  
import talib  # 如果报错找不到ta-lib，需先安装并确认本地编译环境 
import sys  # 

from dotenv import load_dotenv, find_dotenv  

# Find the .env file in the parent directory
dotenv_path = find_dotenv("../../.env") 
# Load it explicitly
load_dotenv(dotenv_path)  

# Add the parent directory to the sys.path list
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))  

from data_processing import load_data_year, flatten_yf_columns, standardize_columns  
from plotting import plot_results  
from strategy.buy_and_hold import BuyAndHoldStrategy 
from back_test import run_backtest 
import backtrader as bt  

# 设置显示选项
pd.set_option('display.float_format', lambda x: '%.4f' % x)  
# 绘图风格（可选）
plt.style.use('seaborn-v0_8-bright')  
# 设置中文显示
plt.rcParams['font.sans-serif'] = ['PingFang HK']  
plt.rcParams['axes.unicode_minus'] = False 

import random  

# 固定全局随机种子
np.random.seed(42)  
random.seed(42) 

# ——————————————————————————————————————————————————————————————————————————————

# 1. 数据获取与预处理
# 直接从本地Excel读取数据
excel_path = '../cache/TSLA_day.xlsx'  # 路径相对于当前脚本
data = pd.read_excel(excel_path)

# 确保列名统一
data.columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']

# 设置索引为datetime，并转为pandas的DatetimeIndex
data['datetime'] = pd.to_datetime(data['datetime'])
data.set_index('datetime', inplace=True)

ticker = 'TSLA'

# 设定时间范围变量
start_date = data.index.min()
end_date = data.index.max()

print(f"获取数据时间范围：{start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")

# 如果 flatten_yf_columns 和 standardize_columns 只是做列名标准化，可以注释掉
# 如果后续有依赖，可以保留
# data = flatten_yf_columns(data)
# data = standardize_columns(data)

# 确保数据不为空
if data.empty:
    raise ValueError("数据加载失败，请检查Excel文件路径或内容")

print(data.info())  # 看看总共有多少行、列，各字段数据类型
print(data.head(10))  # 查看前10行，确认最早日期
print(data.tail(10))  # 查看后10行，确认最晚日期
print(data.index.min())  # DataFrame中最早的日期
print(data.index.max())  # DataFrame中最晚的日期

print("数据框形状:", data.shape)  # 检查是否为空
print("索引示例:", data.index[:5])  # 检查时间索引

# ——————————————————————————————————————————————————————————————————————————————

# 2. 加入技术指标
# 构建两个简单的因子：
# 动量因子：过去5日涨跌幅
# 成交量比值：最近5日均量vs最近10日均量
# 先举几个常用指标的例子：RSI, MACD, 布林带。

df = data.copy()  # 

# 动量因子: 过去5日涨跌幅
df['momentum_5'] = df['close'] / df['close'].shift(5) - 1 

# 成交量因子: (最近5日平均成交量) / (最近10日平均成交量) - 1
df['vol_ratio'] = (df['volume'].rolling(5).mean()) / (df['volume'].rolling(10).mean()) - 1 

# 计算RSI (默认周期14)
df['RSI_14'] = talib.RSI(df['close'], timeperiod=14)  

# 布林带
upper, middle, lower = talib.BBANDS( 
    df['close'],  
    timeperiod=20, 
    nbdevup=2,  
    nbdevdn=2,  
    matype=0 
)  
df['BB_upper'] = upper 
df['BB_middle'] = middle  
df['BB_lower'] = lower  

# 也可以增加其他指标，比如ATR, CCI等，根据需要添加
df.dropna(inplace=True)  # 丢掉因子无法计算的前几行 

factors = ['momentum_5', 'vol_ratio', 'RSI_14', 'BB_upper', 'BB_lower']  
# 看看加上技术指标后的DataFrame
print(df[['close'] + factors].tail(5))  



# ——————————————————————————————————————————————————————————————————————————————

# 3. 目标变量的定义
# 定义下期1日收益率作为目标变量。
df['future_ret_1d'] = df['close'].pct_change().shift(-1)  

# 去掉NaN值
df.dropna(inplace=True) 

print("添加目标变量后的数据预览：")  
print(df[['close'] + factors].head(10)) 

# 绘制目标变量分布
plt.figure(figsize=(10, 5)) 
sns.histplot(df['future_ret_1d'], bins=50)  
plt.title('下期收益率分布')  
plt.xlabel('收益率') 
plt.show()  

# 计算因子与目标变量的相关性
corr = df[['close'] + factors].corr() 

plt.figure(figsize=(8, 6))  
sns.heatmap(corr, annot=True, cmap='coolwarm', center=0)  
plt.title('因子与目标变量相关性')  
plt.show()  

print(f"目标变量的均值={np.mean(df['future_ret_1d'])}")  
print(f"目标变量的方差={np.std(df['future_ret_1d'])}")  

# ——————————————————————————————————————————————————————————————————————————————
# 4. 划分训练集与测试集
# 按照时间顺序，使用前60%的数据作为训练集，中20%作为验证集，后20%作为测试集。

train_idx = int(len(df) * 0.6)  
valid_idx = int(len(df) * 0.8)  

split_date_1 = df.index[train_idx]  
split_date_2 = df.index[valid_idx]  

train_data = df.iloc[:train_idx].copy()  
val_data = df.iloc[train_idx:valid_idx].copy()  
test_data = df.iloc[valid_idx:].copy()  

print("训练集范围:", train_data.index.min(), "→", train_data.index.max()) 
print("验证集范围:", val_data.index.min(), "→", val_data.index.max())  
print("测试集范围:", test_data.index.min(), "→", test_data.index.max())  
print("\n训练集样本数:", len(train_data))  
print("验证集样本数:", len(val_data))  
print("测试集样本数:", len(test_data))  

# 可视化训练集和测试集的划分
plt.figure(figsize=(15, 6))  
plt.plot(train_data.index, train_data['future_ret_1d'], label='训练集', color='blue')  
plt.plot(val_data.index, val_data['future_ret_1d'], label='验证集', color='green')  
plt.plot(test_data.index, test_data['future_ret_1d'], label='测试集', color='red')  
plt.axvline(split_date_1, color='black', linestyle='--', label='划分点') 
plt.axvline(split_date_2, color='black', linestyle='--', label='划分点')  
plt.title('训练集、验证集、测试集划分')  
plt.xlabel('日期')  
plt.ylabel('收益率')  # 
plt.legend()  
plt.grid(True)  
plt.show() 

print(f"训练接大小: (len(train_data))")
print(f"验证集大小: (len(val_data))")
print(f"测试集大小: (len(test_data))")


# ——————————————————————————————————————————————————————————————————————————————

# 3. Buy & Hold策略
bh_result, bh_cerebro = run_backtest(  
    ticker=ticker, 
    df=test_data,  # 
    start_date=start_date, 
    end_date=end_date,  # 
    strategy=BuyAndHoldStrategy,  
    initial_cash=100000,  
    print_log=True,  # 这次打开日志  
    timeframe=bt.TimeFrame.Days,  
    compression=1  # 
)  


# ===== 猴子补丁：为 numpy 添加 bool8 和 object 属性 =====
import numpy as np  

if not hasattr(np, 'bool8'):  
    np.bool8 = np.bool_  # 使用 numpy 自带的 bool_ 类型  # 
if not hasattr(np, 'object'): 
    np.object = object  # 兼容 backtrader_plotting 的引用  

plot_results(bh_cerebro)

# ——————————————————————————————————————————————————————————————————————————————

# 4. 模型训练与超参数优化
features = factors  
X_train = train_data[features].values  
y_train = train_data['future_ret_1d'].values  
X_val = val_data[features].values  
y_val = val_data['future_ret_1d'].values 
X_test = test_data[features].values  
y_test = test_data['future_ret_1d'].values  

# 4.1 训练线性模型

import copy  
import numpy as np  
from sklearn.linear_model import LinearRegression  # 
from sklearn.preprocessing import StandardScaler  
from sklearn.metrics import mean_squared_error, r2_score  
from sklearn.model_selection import ParameterGrid 
from sklearn.pipeline import Pipeline  

# 假设 X_train, y_train, X_valid, y_valid, X_test, y_test 已经定义

######################################
# 1. 建立 Pipeline（先缩放，再线性回归）
######################################
pipeline_lr = Pipeline([ 
    # ('scaler', StandardScaler()),
    ('lr', LinearRegression()) 
]) 

######################################
# 2. 定义线性模型的超参数搜索范围
#    这里只调节 fit_intercept 参数，你可以根据需要添加其他模型或参数
######################################
param_grid_lr = {  
    'lr__fit_intercept': [True, False]  # 
}  

######################################
# 3. 遍历所有参数组合，寻找最佳线性模型（在验证集上评估）
######################################
best_score_lr = float('-inf') 
best_params_lr = None  
best_pipeline_lr = None  

for params in ParameterGrid(param_grid_lr):  
    pipeline_lr.set_params(**params)  
    pipeline_lr.fit(X_train, y_train)  

    # 在验证集上进行预测和评估
    valid_pred_lr = pipeline_lr.predict(X_val) 
    valid_r2_lr = r2_score(y_val, valid_pred_lr)  

    if valid_r2_lr > best_score_lr:  
        best_score_lr = valid_r2_lr  
        best_params_lr = params  
        # 复制当前 pipeline，保存最佳模型
        best_pipeline_lr = copy.deepcopy(pipeline_lr)  
        print("更新：", best_score_lr, best_params_lr)  

print("最佳参数：", best_params_lr)  

######################################
# 4. 使用最佳模型在训练集和测试集上评估
######################################
y_pred_train_lr = best_pipeline_lr.predict(X_train)  
y_pred_test_lr = best_pipeline_lr.predict(X_test)  

train_mse_lr = mean_squared_error(y_train, y_pred_train_lr)  
test_mse_lr = mean_squared_error(y_test, y_pred_test_lr) 
train_r2_lr = r2_score(y_train, y_pred_train_lr)  
test_r2_lr = r2_score(y_test, y_pred_test_lr)  

print("==== 线性模型 - 训练集 ====")  
print("MSE:", train_mse_lr)  
print("R2: ", train_r2_lr)  

print("==== 线性模型 - 测试集 ====")  
print("MSE:", test_mse_lr) 
print("R2: ", test_r2_lr)  

# 查看训练后的回归系数和截距
print("Coefficients:", best_pipeline_lr.named_steps['lr'].coef_) 
print("Intercept:", best_pipeline_lr.named_steps['lr'].intercept_)  



# 4.2 训练随机森林

import copy 
import numpy as np  
from sklearn.ensemble import RandomForestRegressor  # 
from sklearn.preprocessing import StandardScaler  
from sklearn.metrics import mean_squared_error, r2_score  
from sklearn.model_selection import ParameterGrid  
from sklearn.pipeline import Pipeline  

# 假设 X_train, y_train, X_valid, y_valid, X_test, y_test 已经定义
# 同时 features 变量也定义了各特征名称

######################################
# 1. 建立 Pipeline（可选标准化，对 RF 来说不一定必要，但便于与其他模型比较）
######################################
pipeline_rf = Pipeline([ 
    # ('scaler', StandardScaler()),
    ('rf', RandomForestRegressor(random_state=42))  
])  

######################################
# 2. 定义 RF 的超参数搜索范围
######################################
param_grid_rf = {  
    'rf__n_estimators': [1000],  
    'rf__max_depth': [3, 5, 10, 20],  
    'rf__min_samples_split': [2, 5, 10, 20],  
    'rf__min_samples_leaf': [1, 2, 4, 8],  
    'rf__max_features': [0.1, 0.3, 'sqrt']  
}  

######################################
# 3. 遍历所有参数组合，在验证集上寻找最佳 RF 模型
######################################
best_score_rf = float('-inf') 
best_params_rf = None  # 
best_pipeline_rf = None  

for params in ParameterGrid(param_grid_rf): 
    # 设置参数并训练模型
    pipeline_rf.set_params(**params)  
    pipeline_rf.fit(X_train, y_train)  

    # 在验证集上进行预测并计算 R2 得分
    valid_pred_rf = pipeline_rf.predict(X_val)  
    valid_r2_rf = r2_score(y_val, valid_pred_rf) 

    if valid_r2_rf > best_score_rf:  
        best_score_rf = valid_r2_rf  
        best_params_rf = params  
        best_pipeline_rf = copy.deepcopy(pipeline_rf)  
        print("更新：", best_score_rf, best_params_rf)  

print("最佳参数：", best_params_rf)  

######################################
# 4. 使用最佳模型在训练集和测试集上评估
######################################
y_pred_train_rf = best_pipeline_rf.predict(X_train)  
y_pred_test_rf = best_pipeline_rf.predict(X_test)  

train_mse_rf = mean_squared_error(y_train, y_pred_train_rf)  
test_mse_rf = mean_squared_error(y_test, y_pred_test_rf)  # 
train_r2_rf = r2_score(y_train, y_pred_train_rf)  
test_r2_rf = r2_score(y_test, y_pred_test_rf)  

print("==== 随机森林 - 训练集 ====") 
print("MSE:", train_mse_rf)  
print("R2 :", train_r2_rf)  

print("==== 随机森林 - 测试集 ====")  
print("MSE:", test_mse_rf)  
print("R2 :", test_r2_rf)  

######################################
# 5. 查看特征重要性
######################################
feature_importances = best_pipeline_rf.named_steps['rf'].feature_importances_  
for f, imp in zip(features, feature_importances):  
    print(f"Feature: {f}, Importance: {imp:.4f}")  

# 按重要性排序输出
sorted_idx = np.argsort(feature_importances)[::-1]  
print("\nSorted Feature Importances:")  
for idx in sorted_idx:  
    print(f"{features[idx]} -> {feature_importances[idx]:.4f}")  


# 3.3 训练XGBoost
import copy  
import numpy as np  
from xgboost import XGBRegressor  
from sklearn.metrics import mean_squared_error, r2_score  
from sklearn.model_selection import ParameterGrid 
from sklearn.pipeline import Pipeline  
from sklearn.preprocessing import StandardScaler 

# 假设 X_train, y_train, X_valid, y_valid, X_test, y_test 已经定义
# 同时 features 列表也定义了各特征名称

######################################
# 1. 建立 Pipeline（XGBoost 通常不需要标准化）
######################################
pipeline_xgb = Pipeline([  
    # 如果需要标准化，可取消注释下面这一行：
    # ('scaler', StandardScaler()),
    ('xgb', XGBRegressor(random_state=42, verbosity=0))  
])  

######################################
# 2. 定义 XGBoost 的超参数搜索范围
######################################
param_grid_xgb = { 
    'xgb__n_estimators': [100, 500, 1000], 
    'xgb__learning_rate': [0.01, 0.05, 0.1],  
    'xgb__max_depth': [3, 5, 20],  
    'xgb__subsample': [1.0, 0.8]  
} 

######################################
# 3. 遍历所有参数组合，在验证集上寻找最佳 XGBoost 模型
######################################
best_score_xgb = float('-inf')  
best_params_xgb = None  
best_pipeline_xgb = None  

for params in ParameterGrid(param_grid_xgb):  
    pipeline_xgb.set_params(**params) 
    pipeline_xgb.fit(X_train, y_train) 

    # 在验证集上进行预测并计算 R² 得分
    valid_pred_xgb = pipeline_xgb.predict(X_val) 
    valid_r2_xgb = r2_score(y_val, valid_pred_xgb)  

    if valid_r2_xgb > best_score_xgb:  
        best_score_xgb = valid_r2_xgb 
        best_params_xgb = params  
        best_pipeline_xgb = copy.deepcopy(pipeline_xgb)  
        print("更新：", best_score_xgb, best_params_xgb)  

print("最佳参数：", best_params_xgb)  

######################################
# 4. 使用最佳模型在训练集和测试集上评估
######################################
y_pred_train_xgb = best_pipeline_xgb.predict(X_train)  
y_pred_test_xgb = best_pipeline_xgb.predict(X_test)  

train_mse_xgb = mean_squared_error(y_train, y_pred_train_xgb)  
test_mse_xgb = mean_squared_error(y_test, y_pred_test_xgb)  
train_r2_xgb = r2_score(y_train, y_pred_train_xgb)  
test_r2_xgb = r2_score(y_test, y_pred_test_xgb)  

print("==== XGBoost - 训练集 ====")  # 
print("MSE:", train_mse_xgb)  
print("R2: ", train_r2_xgb) 

print("==== XGBoost - 测试集 ====")  
print("MSE:", test_mse_xgb)  
print("R2: ", test_r2_xgb) 

######################################
# 5. 查看特征重要性
######################################
feature_importances_xgb = best_pipeline_xgb.named_steps['xgb'].feature_importances_  
for f, imp in zip(features, feature_importances_xgb):  
    print(f"Feature: {f}, Importance: {imp:.4f}") 

# 按重要性排序输出
sorted_idx_xgb = np.argsort(feature_importances_xgb)[::-1]  
print("\nSorted Feature Importances (XGBoost):")  
for idx in sorted_idx_xgb:  
    print(f"{features[idx]} -> {feature_importances_xgb[idx]:.4f}")  


# 3.4 训练MLP
import copy  
import numpy as np 
from sklearn.preprocessing import StandardScaler 
from sklearn.neural_network import MLPRegressor  
from sklearn.metrics import mean_squared_error, r2_score  
from sklearn.model_selection import ParameterGrid  
from sklearn.pipeline import Pipeline  

# 假设 X_train, y_train, X_valid, y_valid, X_test, y_test 已经定义

######################################
# 1. 建立 Pipeline（先缩放，再 MLP 回归）
######################################
pipeline = Pipeline([ 
    ('scaler', StandardScaler()),  # 
    ('mlp', MLPRegressor(random_state=42, max_iter=1000)) 
])  

######################################
# 2. 定义 MLP 的超参数搜索范围
######################################
param_grid_mlp = {  
    'mlp__hidden_layer_sizes': [(64, 64), (128, 128), (256, 256)],  
    'mlp__alpha': [1e-4, 1e-3, 1e-2],  
    'mlp__learning_rate_init': [1e-4, 1e-3, 1e-2],  
    'mlp__solver': ['adam', 'sgd']  
}  

######################################
# 3. 遍历所有参数组合，寻找最优 MLP 模型（在验证集上评估）
######################################
best_score = float('-inf')  
best_params = None  
best_pipeline_mlp = None  

for params in ParameterGrid(param_grid_mlp):  
    # 设置 Pipeline 的参数
    pipeline.set_params(**params) 
    pipeline.fit(X_train, y_train)  

    # 在验证集上进行预测和评估
    valid_pred = pipeline.predict(X_val) 
    valid_r2 = r2_score(y_val, valid_pred) 

    if valid_r2 > best_score:  
        best_score = valid_r2  # 
        best_params = params  # 
        # 复制当前 pipeline，保存最佳模型
        best_pipeline_mlp = copy.deepcopy(pipeline)  
        print('updated', best_score, best_params)  

print("Best Params:", best_params)  

######################################
# 4. 使用最优模型在训练集和测试集上评估
######################################
y_pred_train_mlp = best_pipeline_mlp.predict(X_train) 
y_pred_test_mlp = best_pipeline_mlp.predict(X_test) 

train_mse_mlp = mean_squared_error(y_train, y_pred_train_mlp)  
test_mse_mlp = mean_squared_error(y_test, y_pred_test_mlp)  
train_r2_mlp = r2_score(y_train, y_pred_train_mlp)  
test_r2_mlp = r2_score(y_test, y_pred_test_mlp)  # 

print("==== MLP - 训练集 ====")  
print("MSE:", train_mse_mlp)  
print("R2: ", train_r2_mlp)  

print("==== MLP - 测试集 ====")  
print("MSE:", test_mse_mlp)  
print("R2: ", test_r2_mlp)  

# 4. 模型集成与权重优化（用凸优化）
import numpy as np 
import cvxpy as cp  
from sklearn.metrics import mean_squared_error, r2_score  


def optimize_weights_constrained(  
        models, 
        X_val, 
        y_val,  
        sum_to_1=True,  # 是否约束权重和=1 
        nonnegative=True,  # 是否要求所有权重>=0  
        alpha_l1=0.0,  # L1正则系数  
        alpha_l2=0.0,  # L2正则系数 
        verbose=True  
): 
    """  
    用凸优化方式在验证集上寻找一组最优权重，使得加权后的预测最小化 MSE   
    （或等效地最大化 R²），并可选地加入 L1/L2 正则，还可选地约束权重和=1、权重>=0。  

    参数：  
    - models: 传入已训练好的各个模型列表  
    - X_val, y_val: 验证集特征和目标 
    - sum_to_1: Boolean, 若为 True，则加上 sum(w) == 1 的约束  # 
    - nonnegative: Boolean, 若为 True，则加上 w >= 0 的约束  
    - alpha_l1, alpha_l2: L1、L2 正则化系数  
    - verbose: 是否打印约束求解的一些信息  

    返回：  
    - w_opt: 优化得到的权重向量 (numpy array)  
    - score_r2: 用该权重在验证集上得到的 R² 分数  
    """  
    # 1) 先得到在验证集上的预测矩阵 predictions: shape (N, M)
    predictions = np.column_stack([model.predict(X_val) for model in models])  
    N, M = predictions.shape  

    # 2) 定义优化变量 w: 大小 M
    #    如果 nonnegative=True，则需要 w >= 0
    if nonnegative:  
        w = cp.Variable(M, nonneg=True)  
    else:  
        w = cp.Variable(M) 

    # 3) 定义约束列表 constraints
    constraints = []  
    if sum_to_1:  
        # sum(w) == 1
        constraints.append(cp.sum(w) == 1)  

    # 4) 定义目标函数（最小化 MSE + 正则项）
    #    MSE 可以写成 sum_squares(y_val - predictions @ w)
    residual = y_val - predictions @ w  
    obj_mse = cp.sum_squares(residual) 

    # 若要加 L1 正则：alpha_l1 * ||w||_1
    # 若要加 L2 正则：alpha_l2 * ||w||_2^2
    obj_reg = 0 
    if alpha_l1 > 0:  
        obj_reg += alpha_l1 * cp.norm1(w)  # 
    if alpha_l2 > 0:  
        obj_reg += alpha_l2 * cp.norm2(w) ** 2  

    # 最终目标：Minimize(MSE + 正则)
    objective = cp.Minimize(obj_mse + obj_reg) 

    # 5) 构建并求解凸优化问题
    problem = cp.Problem(objective, constraints)  # 
    result = problem.solve(verbose=verbose)  # 如果想看更多solver输出，可设 verbose=True  

    # 6) 拿到最优权重 w_opt
    w_opt = w.value  
    # 计算该组合在验证集上的 r2_score
    y_val_pred = predictions @ w_opt  # 
    score_r2 = r2_score(y_val, y_val_pred)  

    if verbose:  # 
        print(f"Optimal objective (MSE + reg) = {problem.value:.6f}")  
        print("Optimized weights:", w_opt)  
        print(f"sum of weights = {w_opt.sum():.4f}") 
        print(f"R2 on validation set = {score_r2:.4f}")  

    return w_opt, score_r2  


# =======================
# 使用示例
# =======================
# 假设你已经在训练集上训练好了 4 个模型：models = [m1, m2, m3, m4]
# 并且有验证集 X_val, y_val

# 比如我们想：
#   - 权重和 = 1
#   - 权重 >= 0
#   - 加一点儿 L2 正则以防止极端权重
#   - 不打印太详细的求解日志 => verbose=False
w_constrained, r2_constrained = optimize_weights_constrained( 
    models=[best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb, best_pipeline_mlp],  
    X_val=X_val,  
    y_val=y_val,  # 
    sum_to_1=True,  
    nonnegative=True,  # 
    alpha_l1=0.0,  
    alpha_l2=1e-3,  
    verbose=False  
) 

print("得到的约束权重 =", [f"{num:0.2f}" for num in w_constrained])  
print("验证集 R² =", r2_constrained)  


# 1. 得到测试集上各模型的预测矩阵
predictions_test = np.column_stack([model.predict(X_test) for model in
                                    [best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb,
                                     best_pipeline_mlp]])  

# 2. 利用之前优化得到的权重 w_constrained 对测试集预测进行加权组合
y_test_pred = predictions_test @ w_constrained 

# 3. 计算测试集上的 R² 分数
r2_test = r2_score(y_test, y_test_pred) 
print("测试集 R² =", r2_test)  



# 5. Emsemble策略实现与回测
import backtrader as bt  


# 自定义成交量指标，把成交量数据单独显示在子图中
class MyVolumeIndicator(bt.Indicator):  
    """ 
    简单示例，把data的volume包装成一个单独的子图指标。  
    """ 
    lines = ('vol',)  
    plotinfo = dict(subplot=True, plotname='Volume')  # 让它单独开子图  

    def __init__(self):  
        self.lines.vol = self.data.volume  


class MLEnsembleStrategy(bt.Strategy): 
    params = ( 
        ('target_percent', 0.98),  # 目标仓位百分比  
    )  # 

    def __init__(self, models, weights):  
        self.models = models  # 
        self.weights = weights  

        # 关闭主图中Data自带的Volume绘制
        self.data.plotinfo.plotvolume = False  

        # 自定义成交量指标以及其SMA指标
        self.myvol = MyVolumeIndicator(self.data)  # 
        self.vol_5 = bt.indicators.SMA(self.myvol.vol, period=5)  
        self.vol_5.plotinfo.subplot = True  
        self.vol_10 = bt.indicators.SMA(self.myvol.vol, period=10)  
        self.vol_10.plotinfo.subplot = True  

        # 添加其它因子指标

        # 价格动量指标：计算5日价格百分比变化
        self.momentum_5 = bt.indicators.PercentChange(self.data.close, period=5)  # 

        # RSI指标，14日周期
        self.rsi_14 = bt.indicators.RSI(self.data.close, period=14)  

        # 布林带指标，默认20日均线和2倍标准差，返回上轨、均线和下轨
        self.bb = bt.indicators.BollingerBands(self.data.close) 

        self.last_trade_type = None  # 记录上一次交易类型（buy/sell）  

        self.value_history_dates = []  
        self.value_history_values = []  

    def next(self):  
        # 计算各个因子的当前值
        momentum = self.momentum_5[0]  
        vol_ratio = (self.vol_5[0] / self.vol_10[0] - 1) if self.vol_10[0] != 0 else 0  
        rsi = self.rsi_14[0] 
        bb_upper = self.bb.top[0]  # 布林带上轨 
        bb_lower = self.bb.bot[0]  # 布林带下轨  

        # 构建特征向量：注意顺序需要与模型训练时一致
        X = [[momentum, vol_ratio, rsi, bb_upper, bb_lower]] 

        # 获取各模型的预测值，假设每个模型输出一个标量预测
        predictions = np.array([model.predict(X)[0] for model in self.models])  
        # print("Predictions:", predictions)

        # 加权平均得到集成预测
        pred_ret = np.sum(predictions * self.weights)  # 
        # print("Ensemble Prediction:", pred_ret)

        # 获取当前持仓状态
        current_position = self.getposition().size  

        if pred_ret > 0 and current_position == 0:  
            # 只有当当前没有仓位时，才执行买入
            self.order_target_percent(target=self.p.target_percent)  
            self.last_trade_type = "BUY"  
            print(f"{self.datas[0].datetime.date(0)} => BUY signal, pred_ret={pred_ret:.6f}")  

        elif pred_ret <= 0 and current_position > 0:  
            # 只有当当前有仓位时，才执行卖出
            self.order_target_percent(target=0.0)  
            self.last_trade_type = "SELL"  
            print(f"{self.datas[0].datetime.date(0)} => SELL signal, pred_ret={pred_ret:.6f}")  

        # 只在交易执行时打印仓位信息
        if self.last_trade_type:  
            print(f"Current position size: {self.getposition().size}, Value: {self.broker.getvalue()}")  

        dt = self.data.datetime.date(0)  
        self.value_history_dates.append(dt)  
        self.value_history_values.append(self.broker.getvalue())  



# 若想看多模型集成策略的详细回测日志，可调用如下回测函数:
ml_ensemble_result, ml_ensemble_cerebro = run_backtest(  
    ticker=ticker,  
    df=test_data,  
    start_date=start_date,  
    end_date=end_date,  
    strategy=MLEnsembleStrategy, 
    initial_cash=100000,  
    strategy_params={  
        'models': [best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb, best_pipeline_mlp],  
        # 'weights': optimal_weights,   # 归一化后的各模型权重
        'weights': w_constrained,  # 归一化后的各模型权重  
        'target_percent': 0.98,  # 目标仓位百分比  
    },  
    print_log=True,  # 打开详细回测日志  
)  

# 如需进一步查看回测结果或可视化结果，可进一步操作 ml_ensemble_cerebro 或 ml_ensemble_result



# ===== 猴子补丁：为 numpy 添加 bool8 和 object 属性 =====
import numpy as np  

if not hasattr(np, 'bool8'):  
    np.bool8 = np.bool_  # 使用 numpy 自带的 bool_ 类型 
if not hasattr(np, 'object'): 
    np.object = object  # 兼容 backtrader_plotting 的引用  

plot_results(ml_ensemble_cerebro)  

# 6. 比较策略和Buy&Hold
results = ml_ensemble_cerebro.run()  # cerebro.run() 返回一个列表，每个元素是一个策略实例  
ml_strategy_instance = results[0]  # 如果你只有一个策略，就取第一个  

results = bh_cerebro.run()  
bh_strategy_instance = results[0]  

import matplotlib.pyplot as plt  

plt.figure(figsize=(12, 6)) 
plt.plot(ml_strategy_instance.value_history_dates, ml_strategy_instance.value_history_values,
         label='集成模型') 
plt.plot(bh_strategy_instance.value_history_dates, bh_strategy_instance.value_history_values,
         label='买入并持有')  
plt.xlabel('时间') 
plt.ylabel('资产净值')  
plt.title('回报曲线对比')  
plt.legend()  # 
plt.show()  
