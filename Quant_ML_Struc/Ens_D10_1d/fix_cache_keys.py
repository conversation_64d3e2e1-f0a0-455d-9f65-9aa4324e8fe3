#!/usr/bin/env python3
"""
修复缓存键生成函数，为不同标准化方法生成不同的缓存键
"""

import os
import re

# 文件映射：文件名 -> 标准化方法名
file_mappings = {
    '17w_2.2_NormilizedData.py': 'StandardScaler',
    '17w_2.2_PowerTransformer_24.1w.py': 'PowerTransformer',
    '17w_2.2_QuantileTransformer_26.2w.py': 'QuantileTransformer'
}

def fix_cache_key_function(file_path, scaler_name):
    """修复缓存键生成函数"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复函数定义
    old_func = r'def generate_cache_key\(X_train, y_train, factors, model_type, params\):\s*\n\s*"""生成缓存键"""\s*\n\s*data_hash = hashlib\.md5\(str\(X_train\.shape\)\.encode\(\) \+ str\(y_train\.shape\)\.encode\(\)\)\.hexdigest\(\)\[:8\]\s*\n\s*factors_hash = hashlib\.md5\(str\(sorted\(factors\)\)\.encode\(\)\)\.hexdigest\(\)\[:8\]\s*\n\s*params_hash = hashlib\.md5\(str\(sorted\(params\.items\(\)\)\)\.encode\(\)\)\.hexdigest\(\)\[:8\]\s*\n\s*return f"{model_type}_{data_hash}_{factors_hash}_{params_hash}\.pkl"'
    
    new_func = f'''def generate_cache_key(X_train, y_train, factors, model_type, params, scaler_type="{scaler_name}"):
    """生成缓存键，包含标准化方法信息"""
    data_hash = hashlib.md5(str(X_train.shape).encode() + str(y_train.shape).encode()).hexdigest()[:8]
    factors_hash = hashlib.md5(str(sorted(factors)).encode()).hexdigest()[:8]
    params_hash = hashlib.md5(str(sorted(params.items())).encode()).hexdigest()[:8]
    scaler_hash = hashlib.md5(scaler_type.encode()).hexdigest()[:8]
    return f"{{model_type}}_{{scaler_type}}_{{data_hash}}_{{factors_hash}}_{{params_hash}}_{{scaler_hash}}.pkl"'''
    
    content = re.sub(old_func, new_func, content, flags=re.MULTILINE | re.DOTALL)
    
    # 修复所有缓存键调用
    cache_calls = [
        (r'cache_key_lr = generate_cache_key\(X_train, y_train, factors, "LinearRegression", param_grid_lr\)', 
         f'cache_key_lr = generate_cache_key(X_train, y_train, factors, "LinearRegression", param_grid_lr, "{scaler_name}")'),
        (r'cache_key_rf = generate_cache_key\(X_train, y_train, factors, "RandomForest", param_grid_rf\)', 
         f'cache_key_rf = generate_cache_key(X_train, y_train, factors, "RandomForest", param_grid_rf, "{scaler_name}")'),
        (r'cache_key_xgb = generate_cache_key\(X_train, y_train, factors, "XGBoost", param_grid_xgb\)', 
         f'cache_key_xgb = generate_cache_key(X_train, y_train, factors, "XGBoost", param_grid_xgb, "{scaler_name}")'),
        (r'cache_key_mlp = generate_cache_key\(X_train, y_train, factors, "MLP", param_grid_mlp\)', 
         f'cache_key_mlp = generate_cache_key(X_train, y_train, factors, "MLP", param_grid_mlp, "{scaler_name}")')
    ]
    
    for old_call, new_call in cache_calls:
        content = re.sub(old_call, new_call, content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ 已修复 {file_path}")

def main():
    base_dir = '/Users/<USER>/MLQuant/Quant_ML_Struc/Ens_D10_1d'
    
    for filename, scaler_name in file_mappings.items():
        file_path = os.path.join(base_dir, filename)
        if os.path.exists(file_path):
            fix_cache_key_function(file_path, scaler_name)
        else:
            print(f"⚠️ 文件不存在: {file_path}")
    
    print("\n🎉 所有文件修复完成！")
    print("\n📝 修复内容:")
    print("1. 缓存键生成函数增加了 scaler_type 参数")
    print("2. 缓存键中包含了标准化方法信息")
    print("3. 所有缓存键调用都传入了对应的标准化方法名")
    print("\n🔄 现在重新运行模型文件，每个标准化方法将生成独立的缓存！")

if __name__ == "__main__":
    main()
