# Ensemble 模型策略 - SHAP-XGBoost版本
# 本文件基于17w_2.3_26.6w_RS_Mo.py，将XGBoost替换为SHAP-XGBoost
# SHAP-XGBoost: 增强可解释性的XGBoost模型，提供特征重要性分析

# 🚀 核心优化特性：
# 1. 训练和实际交易保持完全一致：使用相同的25个Alpha因子
# 2. 高效因子计算器：支持实时增量计算，避免重复计算
# 3. 内存优化：使用滚动窗口，避免存储过多历史数据
# 4. 向量化计算：提高计算效率oxing
# 5. 模型缓存系统：避免重复训练
# 6. RobustScaler标准化：对异常值更鲁棒，使用中位数和四分位距进行标准化

# 📊 新增评估特性：
# 7. 时间序列交叉验证：Walk-Forward Analysis, Purged CV
# 8. 多维度评估指标：IC, IR, 夏普比率, 最大回撤等
# 9. 浏览器可视化：backtrader_plotting支持

# ——————————————————————————————————————————————————————————————————————————————

# 0. 导入依赖包
import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import talib  # 如果报错找不到ta-lib，需先安装并确认本地编译环境
import sys  #
import warnings

warnings.filterwarnings('ignore')

from dotenv import load_dotenv, find_dotenv

# Find the .env file in the parent directory
dotenv_path = find_dotenv("../../.env")
# Load it explicitly
load_dotenv(dotenv_path)

# Add the parent directory to the sys.path list
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from data_processing import load_data_year, flatten_yf_columns, standardize_columns
from plotting import plot_results
from strategy.buy_and_hold import BuyAndHoldStrategy
from back_test import run_backtest
import backtrader as bt

# 导入backtrader_plotting用于浏览器可视化
try:
    import backtrader_plotting

    PLOTTING_AVAILABLE = True
    print("✅ backtrader_plotting已加载，支持浏览器可视化")
except ImportError:
    PLOTTING_AVAILABLE = False
    print("⚠️ backtrader_plotting未安装，将使用默认可视化")

# 导入SHAP用于模型解释
try:
    import shap
    SHAP_AVAILABLE = True
    print("✅ SHAP已加载，支持模型可解释性分析")
except ImportError:
    SHAP_AVAILABLE = False
    print("⚠️ SHAP未安装，将跳过可解释性分析")

# 设置显示选项
pd.set_option('display.float_format', lambda x: '%.4f' % x)
# 绘图风格（可选）
plt.style.use('seaborn-v0_8-bright')
# 设置中文显示
plt.rcParams['font.sans-serif'] = ['PingFang HK']
plt.rcParams['axes.unicode_minus'] = False

import random

# 固定全局随机种子
# 在模型训练前添加
os.environ['PYTHONHASHSEED'] = '42'
np.random.seed(42)
random.seed(42)

print("🔧 使用SHAP-XGBoost增强可解释性方案")
print("📊 SHAP-XGBoost特点：结合XGBoost的强大性能与SHAP的可解释性分析")


# ——————————————————————————————————————————————————————————————————————————————

# 📊 新增：时间序列交叉验证类
class TimeSeriesCrossValidator:
    """
    时间序列交叉验证器
    支持Walk-Forward Analysis和Purged Cross-Validation
    """

    def __init__(self, n_splits=5, test_size=0.2, gap=0):
        self.n_splits = n_splits
        self.test_size = test_size
        self.gap = gap  # 训练集和测试集之间的缓冲期

    def walk_forward_split(self, X, y):
        """Walk-Forward Analysis分割"""
        n_samples = len(X)
        test_size_samples = int(n_samples * self.test_size)

        splits = []
        for i in range(self.n_splits):
            # 计算测试集的结束位置
            test_end = n_samples - i * (test_size_samples // self.n_splits)
            test_start = test_end - test_size_samples

            # 计算训练集的结束位置（考虑gap）
            train_end = test_start - self.gap
            train_start = max(0, train_end - int(n_samples * 0.6))  # 使用60%的数据作为训练集

            if train_start < train_end and test_start < test_end:
                train_idx = list(range(train_start, train_end))
                test_idx = list(range(test_start, test_end))
                splits.append((train_idx, test_idx))

        return splits

    def purged_split(self, X, y, embargo_pct=0.01):
        """Purged Cross-Validation分割"""
        n_samples = len(X)
        embargo_samples = int(n_samples * embargo_pct)

        splits = []
        fold_size = n_samples // self.n_splits

        for i in range(self.n_splits):
            # 测试集
            test_start = i * fold_size
            test_end = min((i + 1) * fold_size, n_samples)
            test_idx = list(range(test_start, test_end))

            # 训练集（排除测试集和embargo期）
            train_idx = []

            # 测试集之前的数据
            if test_start - embargo_samples > 0:
                train_idx.extend(range(0, test_start - embargo_samples))

            # 测试集之后的数据
            if test_end + embargo_samples < n_samples:
                train_idx.extend(range(test_end + embargo_samples, n_samples))

            if len(train_idx) > 0 and len(test_idx) > 0:
                splits.append((train_idx, test_idx))

        return splits


# 📈 新增：多维度评估指标计算器
class AdvancedMetricsCalculator:
    """
    高级评估指标计算器
    包含IC、IR、夏普比率、最大回撤等多维度指标
    """

    @staticmethod
    def information_coefficient(predictions, actual_returns):
        """计算信息系数(IC)"""
        return np.corrcoef(predictions, actual_returns)[0, 1]

    @staticmethod
    def information_ratio(predictions, actual_returns):
        """计算信息比率(IR)"""
        ic_series = []
        window_size = min(20, len(predictions) // 5)  # 动态窗口大小

        for i in range(window_size, len(predictions)):
            window_pred = predictions[i - window_size:i]
            window_actual = actual_returns[i - window_size:i]
            ic = np.corrcoef(window_pred, window_actual)[0, 1]
            if not np.isnan(ic):
                ic_series.append(ic)

        if len(ic_series) > 0:
            return np.mean(ic_series) / (np.std(ic_series) + 1e-8)
        return 0.0

    @staticmethod
    def sharpe_ratio(returns, risk_free_rate=0.02):
        """计算夏普比率"""
        excess_returns = returns - risk_free_rate / 252  # 日化无风险利率
        return np.mean(excess_returns) / (np.std(excess_returns) + 1e-8) * np.sqrt(252)

    @staticmethod
    def max_drawdown(returns):
        """计算最大回撤"""
        cumulative = np.cumprod(1 + returns)
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        return np.min(drawdown)

    @staticmethod
    def calmar_ratio(returns):
        """计算卡尔马比率"""
        annual_return = np.mean(returns) * 252
        max_dd = abs(AdvancedMetricsCalculator.max_drawdown(returns))
        return annual_return / (max_dd + 1e-8)

    @staticmethod
    def sortino_ratio(returns, risk_free_rate=0.02):
        """计算索提诺比率"""
        excess_returns = returns - risk_free_rate / 252
        downside_returns = excess_returns[excess_returns < 0]
        downside_deviation = np.std(downside_returns) if len(downside_returns) > 0 else 1e-8
        return np.mean(excess_returns) / downside_deviation * np.sqrt(252)

    @staticmethod
    def win_rate_and_profit_ratio(predictions, actual_returns):
        """计算胜率和盈亏比"""
        # 基于预测方向计算交易信号
        signals = np.where(predictions > 0, 1, -1)
        trade_returns = signals * actual_returns

        winning_trades = trade_returns[trade_returns > 0]
        losing_trades = trade_returns[trade_returns < 0]

        win_rate = len(winning_trades) / len(trade_returns) if len(trade_returns) > 0 else 0

        avg_win = np.mean(winning_trades) if len(winning_trades) > 0 else 0
        avg_loss = np.mean(np.abs(losing_trades)) if len(losing_trades) > 0 else 1e-8
        profit_ratio = avg_win / avg_loss

        return win_rate, profit_ratio

    @staticmethod
    def ic_decay_analysis(predictions, actual_returns, max_lag=5):
        """IC衰减分析"""
        ic_lags = {}
        for lag in range(1, max_lag + 1):
            if lag < len(actual_returns):
                lagged_returns = actual_returns[lag:]
                current_predictions = predictions[:-lag]
                ic = np.corrcoef(current_predictions, lagged_returns)[0, 1]
                ic_lags[f'IC_lag_{lag}'] = ic if not np.isnan(ic) else 0.0
        return ic_lags


# 📊 新增：SHAP分析器
class SHAPAnalyzer:
    """
    SHAP可解释性分析器
    提供特征重要性和模型解释功能
    """

    def __init__(self, model, X_train, feature_names):
        self.model = model
        self.X_train = X_train
        self.feature_names = feature_names
        self.explainer = None
        self.shap_values = None

    def create_explainer(self, explainer_type='tree'):
        """创建SHAP解释器"""
        if not SHAP_AVAILABLE:
            print("⚠️ SHAP未安装，跳过可解释性分析")
            return

        try:
            if explainer_type == 'tree':
                # 对于XGBoost等树模型
                self.explainer = shap.TreeExplainer(self.model)
            elif explainer_type == 'kernel':
                # 通用解释器
                self.explainer = shap.KernelExplainer(self.model.predict, self.X_train[:100])
            else:
                # 默认使用Explainer
                self.explainer = shap.Explainer(self.model, self.X_train[:100])

            print(f"✅ SHAP {explainer_type} 解释器创建成功")
        except Exception as e:
            print(f"⚠️ SHAP解释器创建失败: {e}")

    def calculate_shap_values(self, X_test):
        """计算SHAP值"""
        if self.explainer is None:
            print("⚠️ 请先创建SHAP解释器")
            return

        try:
            self.shap_values = self.explainer.shap_values(X_test[:100])  # 限制样本数量以提高速度
            print("✅ SHAP值计算完成")
        except Exception as e:
            print(f"⚠️ SHAP值计算失败: {e}")

    def plot_feature_importance(self, max_display=20):
        """绘制特征重要性图"""
        if self.shap_values is None:
            print("⚠️ 请先计算SHAP值")
            return

        try:
            plt.figure(figsize=(10, 8))
            shap.summary_plot(self.shap_values, self.X_train[:100],
                            feature_names=self.feature_names,
                            max_display=max_display, show=False)
            plt.title('SHAP Feature Importance Summary')
            plt.tight_layout()
            plt.show()
        except Exception as e:
            print(f"⚠️ SHAP特征重要性图绘制失败: {e}")

    def get_feature_importance_ranking(self):
        """获取特征重要性排名"""
        if self.shap_values is None:
            print("⚠️ 请先计算SHAP值")
            return {}

        try:
            # 计算每个特征的平均绝对SHAP值
            mean_abs_shap = np.mean(np.abs(self.shap_values), axis=0)

            # 创建特征重要性字典
            importance_dict = {}
            for i, importance in enumerate(mean_abs_shap):
                if i < len(self.feature_names):
                    importance_dict[self.feature_names[i]] = importance

            # 按重要性排序
            sorted_importance = dict(sorted(importance_dict.items(),
                                          key=lambda x: x[1], reverse=True))

            print("\n📊 SHAP特征重要性排名 (Top 10):")
            for i, (feature, importance) in enumerate(list(sorted_importance.items())[:10]):
                print(f"{i+1:2d}. {feature:30s} -> {importance:.6f}")

            return sorted_importance
        except Exception as e:
            print(f"⚠️ 特征重要性排名计算失败: {e}")
            return {}


# 📊 新增：指标解释函数
def explain_metric(metric_name, value, context=""):
    """
    详细解释每个指标的含义、合适范围和影响
    """
    explanations = {
        'MSE': {
            'name': '均方误差 (Mean Squared Error)',
            'meaning': '预测值与真实值差异的平方的平均值，衡量预测精度',
            'good_range': '越小越好，接近0表示预测非常准确',
            'high_impact': '过高表示模型预测误差大，可能存在欠拟合或特征不足',
            'low_impact': '过低可能表示过拟合，在新数据上表现可能较差'
        },
        'R²': {
            'name': 'R平方 (决定系数)',
            'meaning': '模型解释目标变量变异的比例，衡量模型拟合优度',
            'good_range': '0-1之间，>0.1为有效，>0.3为良好，>0.5为优秀',
            'high_impact': '过高(>0.9)可能存在过拟合风险',
            'low_impact': '过低(<0.05)表示模型几乎无预测能力，需要改进特征或模型'
        },
        'IC': {
            'name': '信息系数 (Information Coefficient)',
            'meaning': '预测值与实际收益率的相关系数，衡量预测方向的准确性',
            'good_range': '|IC|>0.05有效，|IC|>0.1良好，|IC|>0.15优秀',
            'high_impact': '过高(>0.3)可能存在数据泄漏或过拟合',
            'low_impact': '过低(|IC|<0.02)表示预测能力很弱，策略可能无效'
        },
        'IR': {
            'name': '信息比率 (Information Ratio)',
            'meaning': 'IC均值除以IC标准差，衡量预测稳定性',
            'good_range': '>0.5为良好，>1.0为优秀，>1.5为卓越',
            'high_impact': '过高可能不可持续，需要验证稳健性',
            'low_impact': '过低表示预测不稳定，策略风险较高'
        },
        'sharpe_ratio': {
            'name': '夏普比率 (Sharpe Ratio)',
            'meaning': '超额收益与波动率的比值，衡量风险调整后收益',
            'good_range': '>1.0为良好，>1.5为优秀，>2.0为卓越',
            'high_impact': '过高(>3.0)可能不可持续，需要检查是否过拟合',
            'low_impact': '过低(<0.5)表示承担的风险与收益不匹配'
        },
        'max_drawdown': {
            'name': '最大回撤 (Maximum Drawdown)',
            'meaning': '从峰值到谷值的最大跌幅，衡量最大损失风险',
            'good_range': '<10%优秀，<20%良好，<30%可接受',
            'high_impact': '过高(>50%)表示策略风险过大，可能导致巨额损失',
            'low_impact': '过低可能表示策略过于保守，收益潜力有限'
        },
        'calmar_ratio': {
            'name': '卡尔马比率 (Calmar Ratio)',
            'meaning': '年化收益率与最大回撤的比值，衡量回撤调整后收益',
            'good_range': '>1.0为良好，>2.0为优秀，>3.0为卓越',
            'high_impact': '过高可能不可持续',
            'low_impact': '过低表示收益与回撤不匹配，策略效率低'
        },
        'sortino_ratio': {
            'name': '索提诺比率 (Sortino Ratio)',
            'meaning': '超额收益与下行波动率的比值，只考虑负收益的波动',
            'good_range': '>1.0为良好，>1.5为优秀，>2.0为卓越',
            'high_impact': '过高可能不可持续',
            'low_impact': '过低表示下行风险控制不佳'
        },
        'win_rate': {
            'name': '胜率 (Win Rate)',
            'meaning': '盈利交易占总交易的比例',
            'good_range': '>50%为有效，>55%为良好，>60%为优秀',
            'high_impact': '过高(>80%)可能存在过拟合或数据泄漏',
            'low_impact': '过低(<45%)表示预测准确性不足'
        },
        'profit_ratio': {
            'name': '盈亏比 (Profit Ratio)',
            'meaning': '平均盈利与平均亏损的比值',
            'good_range': '>1.0为有效，>1.5为良好，>2.0为优秀',
            'high_impact': '过高可能不可持续',
            'low_impact': '过低(<0.8)表示亏损过大，风险控制不佳'
        },
        'annual_return': {
            'name': '年化收益率 (Annual Return)',
            'meaning': '策略的年化投资收益率',
            'good_range': '>10%为有效，>20%为良好，>30%为优秀',
            'high_impact': '过高(>100%)可能存在过度风险或不可持续',
            'low_impact': '过低(<5%)可能不如无风险投资'
        }
    }

    if metric_name in explanations:
        info = explanations[metric_name]
        print(f"    📖 {info['name']}")
        print(f"       含义: {info['meaning']}")
        print(f"       合适范围: {info['good_range']}")

        # 根据数值给出具体评价
        if metric_name == 'R²':
            if value > 0.5:
                print(f"       ✅ 当前值 {value:.4f} - 优秀的拟合效果")
            elif value > 0.3:
                print(f"       ✅ 当前值 {value:.4f} - 良好的拟合效果")
            elif value > 0.1:
                print(f"       ⚠️ 当前值 {value:.4f} - 有效但有改进空间")
            elif value > 0:
                print(f"       ⚠️ 当前值 {value:.4f} - 预测能力较弱")
            else:
                print(f"       ❌ 当前值 {value:.4f} - 模型表现差于随机预测")
        elif metric_name == 'IC':
            abs_value = abs(value)
            if abs_value > 0.15:
                print(f"       ✅ 当前值 {value:.6f} - 优秀的预测能力")
            elif abs_value > 0.1:
                print(f"       ✅ 当前值 {value:.6f} - 良好的预测能力")
            elif abs_value > 0.05:
                print(f"       ⚠️ 当前值 {value:.6f} - 有效但有改进空间")
            else:
                print(f"       ❌ 当前值 {value:.6f} - 预测能力很弱")
        elif metric_name == 'sharpe_ratio':
            if value > 2.0:
                print(f"       ✅ 当前值 {value:.4f} - 卓越的风险调整收益")
            elif value > 1.5:
                print(f"       ✅ 当前值 {value:.4f} - 优秀的风险调整收益")
            elif value > 1.0:
                print(f"       ✅ 当前值 {value:.4f} - 良好的风险调整收益")
            elif value > 0.5:
                print(f"       ⚠️ 当前值 {value:.4f} - 一般的风险调整收益")
            else:
                print(f"       ❌ 当前值 {value:.4f} - 风险调整收益较差")
        elif metric_name == 'max_drawdown':
            abs_value = abs(value)
            if abs_value < 0.1:
                print(f"       ✅ 当前值 {value:.4f} - 优秀的风险控制")
            elif abs_value < 0.2:
                print(f"       ✅ 当前值 {value:.4f} - 良好的风险控制")
            elif abs_value < 0.3:
                print(f"       ⚠️ 当前值 {value:.4f} - 可接受的风险水平")
            else:
                print(f"       ❌ 当前值 {value:.4f} - 风险过高，需要改进")

        print(f"       影响: 过高时{info['high_impact']}")
        print(f"            过低时{info['low_impact']}")
    else:
        print(f"    📊 {metric_name}: {value}")


# 📊 新增：综合评估报告生成器
def generate_comprehensive_evaluation_report(models, model_names, X_test, y_test, predictions_dict):
    """生成综合评估报告"""
    print("\n" + "=" * 80)
    print("📊 综合模型评估报告")
    print("=" * 80)

    metrics_calc = AdvancedMetricsCalculator()

    for i, (model_name, predictions) in enumerate(predictions_dict.items()):
        print(f"\n🔍 {model_name} 详细评估:")
        print("-" * 50)

        # 基础指标
        mse = mean_squared_error(y_test, predictions)
        r2 = 1 - (np.sum((y_test - predictions) ** 2) / np.sum((y_test - np.mean(y_test)) ** 2))

        print(f"📈 预测性能指标:")

        # MSE解释
        print(f"  MSE: {mse:.6f}")
        explain_metric('MSE', mse)

        # R²解释
        print(f"  R²: {r2:.6f}")
        explain_metric('R²', r2)

        # 信息系数相关
        ic = metrics_calc.information_coefficient(predictions, y_test)
        ir = metrics_calc.information_ratio(predictions, y_test)

        print(f"  信息系数(IC): {ic:.6f}")
        explain_metric('IC', ic)

        print(f"  信息比率(IR): {ir:.6f}")
        explain_metric('IR', ir)

        # IC衰减分析
        ic_decay = metrics_calc.ic_decay_analysis(predictions, y_test)
        print(f"  IC衰减分析: {ic_decay}")

        # 交易性能指标
        win_rate, profit_ratio = metrics_calc.win_rate_and_profit_ratio(predictions, y_test)

        print(f"\n💰 交易性能指标:")

        # 胜率解释
        print(f"  胜率: {win_rate:.4f} ({win_rate * 100:.2f}%)")
        explain_metric('win_rate', win_rate)

        # 盈亏比解释
        print(f"  盈亏比: {profit_ratio:.4f}")
        explain_metric('profit_ratio', profit_ratio)

        # 基于预测的模拟收益率
        signals = np.where(predictions > 0, 1, -1)
        strategy_returns = signals * y_test

        sharpe = metrics_calc.sharpe_ratio(strategy_returns)
        max_dd = metrics_calc.max_drawdown(strategy_returns)
        calmar = metrics_calc.calmar_ratio(strategy_returns)
        sortino = metrics_calc.sortino_ratio(strategy_returns)

        # 夏普比率解释
        print(f"  夏普比率: {sharpe:.4f}")
        explain_metric('sharpe_ratio', sharpe)

        # 最大回撤解释
        print(f"  最大回撤: {max_dd:.4f} ({max_dd * 100:.2f}%)")
        explain_metric('max_drawdown', max_dd)

        # 卡尔马比率解释
        print(f"  卡尔马比率: {calmar:.4f}")
        explain_metric('calmar_ratio', calmar)

        # 索提诺比率解释
        print(f"  索提诺比率: {sortino:.4f}")
        explain_metric('sortino_ratio', sortino)

        # 年化收益率解释
        annual_return = np.mean(strategy_returns) * 252
        print(f"  年化收益率: {annual_return:.4f} ({annual_return * 100:.2f}%)")
        explain_metric('annual_return', annual_return)

    print("\n" + "=" * 80)


# ——————————————————————————————————————————————————————————————————————————————

# 1. 数据获取与预处理
# 直接从本地Excel读取数据
excel_path = '../cache/TSLA_day.xlsx'  # 修改为正确的相对路径
try:
    data = pd.read_excel(excel_path)
    print(f"✅ 成功从路径加载数据: {excel_path}")
except FileNotFoundError:
    # 尝试备用路径
    alternative_paths = [
        'Quant_ML_Struc/cache/TSLA_day.xlsx',
        '/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_day.xlsx',
        './cache/TSLA_day.xlsx',
        '../../cache/TSLA_day.xlsx'
    ]
    
    for alt_path in alternative_paths:
        try:
            print(f"尝试备用路径: {alt_path}")
            data = pd.read_excel(alt_path)
            excel_path = alt_path  # 更新成功的路径
            print(f"✅ 成功从备用路径加载数据: {alt_path}")
            break
        except FileNotFoundError:
            continue
    else:
        raise ValueError("无法找到数据文件，请检查文件路径或确保文件存在")

# 确保列名统一
data.columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']

# 设置索引为datetime，并转为pandas的DatetimeIndex
data['datetime'] = pd.to_datetime(data['datetime'])
data.set_index('datetime', inplace=True)

ticker = 'TSLA'

# 设定时间范围变量
start_date = data.index.min()
end_date = data.index.max()

print(f"获取数据时间范围：{start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")

# 确保数据不为空
if data.empty:
    raise ValueError("数据加载失败，请检查Excel文件路径或内容")

print(data.info())  # 看看总共有多少行、列，各字段数据类型
print(data.head(10))  # 查看前10行，确认最早日期
print(data.tail(10))  # 查看后10行，确认最晚日期
print(data.index.min())  # DataFrame中最早的日期
print(data.index.max())  # DataFrame中最晚的日期

print("数据框形状:", data.shape)  # 检查是否为空
print("索引示例:", data.index[:5])  # 检查时间索引

# ——————————————————————————————————————————————————————————————————————————————

# 2. 加入技术指标
# 基于17w_2.0推荐的顶级Alpha因子，替换为25个高质量量化因子
# 这些因子经过统计验证，具有显著的预测能力

df = data.copy()

print("开始计算25个顶级Alpha因子...")

# === 1. K线形态因子 ===
# 下影线长度 (顶级因子 #1)
df['lower_shadow'] = (np.minimum(df['close'], df['open']) - df['low']) / df['close']

# === 2. 价量关系因子 ===
# 20日价量相关性 (顶级因子 #2)
price_change_20d = df['close'].pct_change(20)
volume_change_20d = df['volume'].pct_change(20)
df['price_volume_correlation_20d'] = price_change_20d * volume_change_20d

# 10日价量相关性 (顶级因子 #10)
price_returns = df['close'].pct_change()
volume_returns = df['volume'].pct_change()
df['price_volume_corr_10d'] = price_returns.rolling(10).corr(volume_returns)

# === 3. 流动性因子 (Amihud非流动性系列) ===
# 5日Amihud非流动性 (顶级因子 #4)
returns_5d = abs(df['close'].pct_change())
dollar_volume_5d = df['close'] * df['volume']
amihud_5d = returns_5d / (dollar_volume_5d + 1e-8)
df['amihud_illiquidity_5d'] = amihud_5d.rolling(5).mean()

# 10日Amihud非流动性 (顶级因子 #5)
df['amihud_illiquidity_10d'] = amihud_5d.rolling(10).mean()

# 20日Amihud非流动性 (顶级因子 #3)
df['amihud_illiquidity_20d'] = amihud_5d.rolling(20).mean()

# 30日Amihud非流动性 (补充因子)
df['amihud_illiquidity_30d'] = amihud_5d.rolling(30).mean()

# === 4. 波动率因子 (ATR系列) ===
# 计算真实波动率
tr1 = df['high'] - df['low']
tr2 = abs(df['high'] - df['close'].shift(1))
tr3 = abs(df['low'] - df['close'].shift(1))
true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

# 7日ATR (顶级因子 #9)
df['atr_7d'] = true_range.rolling(7).mean()

# 10日ATR (顶级因子 #11)
df['atr_10d'] = true_range.rolling(10).mean()

# 14日ATR (顶级因子 #7)
df['atr_14d'] = true_range.rolling(14).mean()

# 20日ATR (高质量因子)
df['atr_20d'] = true_range.rolling(20).mean()

# === 5. 收益率波动率 ===
# 20日收益率波动率 (补充因子)
returns = df['close'].pct_change()
df['return_volatility_20d'] = returns.rolling(20).std()

# === 6. CCI指标 ===
# 20日CCI (顶级因子 #6)
typical_price = (df['high'] + df['low'] + df['close']) / 3
sma_20 = typical_price.rolling(20).mean()
mad_20 = typical_price.rolling(20).apply(lambda x: np.mean(np.abs(x - x.mean())))
df['cci_20d'] = (typical_price - sma_20) / (0.015 * mad_20)

# 30日CCI (高质量因子)
sma_30 = typical_price.rolling(30).mean()
mad_30 = typical_price.rolling(30).apply(lambda x: np.mean(np.abs(x - x.mean())))
df['cci_30d'] = (typical_price - sma_30) / (0.015 * mad_30)

# === 7. 布林带因子 ===
# 20日布林带位置 (顶级因子 #11)
ma_20 = df['close'].rolling(20).mean()
std_20 = df['close'].rolling(20).std()
upper_band = ma_20 + 2.0 * std_20
lower_band = ma_20 - 2.0 * std_20
df['bollinger_position_20d'] = (df['close'] - lower_band) / (upper_band - lower_band)

# 20日布林带位置(2.0倍标准差) (补充因子)
df['bollinger_position_20d_2.0std'] = (df['close'] - lower_band) / (upper_band - lower_band)

# === 8. 动量因子 ===
# 10日动量强度 (顶级因子 #12)
momentum_10d = df['close'] / df['close'].shift(10) - 1
momentum_std_10d = momentum_10d.rolling(10).std()
df['momentum_strength_10d'] = momentum_10d / (momentum_std_10d + 1e-8)

# 20日价格动量 (补充因子)
df['momentum_20d'] = df['close'] / df['close'].shift(20) - 1

# 10日动量波动率比 (高质量因子)
df['momentum_volatility_ratio_10d'] = momentum_10d

# === 9. 价格变异系数 ===
# 30日价格变异系数 (顶级因子 #13)
returns_30d = df['close'].pct_change()
cv_30d = returns_30d.rolling(30).std() / (returns_30d.rolling(30).mean() + 1e-8)
df['price_cv_30d'] = cv_30d

# === 10. 均值回复因子 ===
# 10日均值回复状态 (高质量因子)
autocorr_10d = returns.rolling(10).apply(
    lambda x: x.autocorr(lag=1) if len(x) >= 2 else np.nan,
    raw=False
)
df['mean_reversion_state_10d'] = -autocorr_10d  # 负自相关表示均值回复

# 10日均值回复强度 (高质量因子)
df['mean_reversion_strength_10d'] = autocorr_10d

# === 11. 波动率调整收益率 ===
# 10日波动率调整收益率 (高质量因子)
returns_10d = df['close'].pct_change(10)
volatility_10d = df['close'].pct_change().rolling(10).std()
df['volatility_adjusted_return_10d'] = returns_10d / (volatility_10d + 1e-8)

# === 12. 价格相对位置 ===
# 20日价格相对位置 (补充因子)
highest_20d = df['high'].rolling(20).max()
lowest_20d = df['low'].rolling(20).min()
df['price_position_20d'] = (df['close'] - lowest_20d) / (highest_20d - lowest_20d + 1e-8)

# === 13. 成交量比率 ===
# 20日成交量相对比率 (补充因子)
vol_ma_20d = df['volume'].rolling(20).mean()
df['volume_ratio_20d'] = df['volume'] / (vol_ma_20d + 1e-8)

# 去掉因子无法计算的前几行
df.dropna(inplace=True)

# 定义25个顶级Alpha因子
factors = [
    'lower_shadow', 'price_volume_correlation_20d', 'amihud_illiquidity_20d',
    'amihud_illiquidity_5d', 'amihud_illiquidity_10d', 'cci_20d', 'atr_14d',
    'atr_7d', 'atr_10d', 'price_volume_corr_10d', 'bollinger_position_20d',
    'momentum_strength_10d', 'price_cv_30d', 'cci_30d', 'mean_reversion_state_10d',
    'mean_reversion_strength_10d', 'volatility_adjusted_return_10d',
    'momentum_volatility_ratio_10d', 'atr_20d', 'amihud_illiquidity_30d',
    'bollinger_position_20d_2.0std', 'price_position_20d', 'volume_ratio_20d',
    'return_volatility_20d', 'momentum_20d'
]

print(f"成功计算{len(factors)}个顶级Alpha因子")
print("因子列表:", factors)
# 看看加上技术指标后的DataFrame
print(df[['close'] + factors].tail(5))

# ——————————————————————————————————————————————————————————————————————————————

# 3. 目标变量的定义
# 定义下期1日收益率作为目标变量。
df['future_ret_1d'] = df['close'].pct_change().shift(-1)

# 去掉NaN值
df.dropna(inplace=True)

print("添加目标变量后的数据预览：")
print(df[['close'] + factors].head(10))

# 绘制目标变量分布
plt.figure(figsize=(10, 5))
sns.histplot(df['future_ret_1d'], bins=50)
plt.title('下期收益率分布')
plt.xlabel('收益率')
plt.show()

# 计算因子与目标变量的相关性
corr = df[['close'] + factors].corr()

plt.figure(figsize=(8, 6))
sns.heatmap(corr, annot=True, cmap='coolwarm', center=0)
plt.title('因子与目标变量相关性')
plt.show()

print(f"目标变量的均值={np.mean(df['future_ret_1d'])}")
print(f"目标变量的方差={np.std(df['future_ret_1d'])}")

# ——————————————————————————————————————————————————————————————————————————————
# 4. 划分训练集与测试集
# 按照时间顺序，使用前60%的数据作为训练集，中20%作为验证集，后20%作为测试集。

train_idx = int(len(df) * 0.6)
valid_idx = int(len(df) * 0.8)

split_date_1 = df.index[train_idx]
split_date_2 = df.index[valid_idx]

train_data = df.iloc[:train_idx].copy()
val_data = df.iloc[train_idx:valid_idx].copy()
test_data = df.iloc[valid_idx:].copy()

print("训练集范围:", train_data.index.min(), "→", train_data.index.max())
print("验证集范围:", val_data.index.min(), "→", val_data.index.max())
print("测试集范围:", test_data.index.min(), "→", test_data.index.max())
print("\n训练集样本数:", len(train_data))
print("验证集样本数:", len(val_data))
print("测试集样本数:", len(test_data))

# 可视化训练集和测试集的划分
plt.figure(figsize=(15, 6))
plt.plot(train_data.index, train_data['future_ret_1d'], label='训练集', color='blue')
plt.plot(val_data.index, val_data['future_ret_1d'], label='验证集', color='green')
plt.plot(test_data.index, test_data['future_ret_1d'], label='测试集', color='red')
plt.axvline(split_date_1, color='black', linestyle='--', label='划分点')
plt.axvline(split_date_2, color='black', linestyle='--', label='划分点')
plt.title('训练集、验证集、测试集划分')
plt.xlabel('日期')
plt.ylabel('收益率')  #
plt.legend()
plt.grid(True)
plt.show()

print(f"训练集大小: {len(train_data)}")
print(f"验证集大小: {len(val_data)}")
print(f"测试集大小: {len(test_data)}")

# ——————————————————————————————————————————————————————————————————————————————

# 📊 新增：时间序列交叉验证评估
print("\n" + "=" * 80)
print("🔍 开始时间序列交叉验证评估")
print("=" * 80)

# 准备数据
features = factors
X_full = df[features].values
y_full = df['future_ret_1d'].values

# 初始化交叉验证器
ts_cv = TimeSeriesCrossValidator(n_splits=5, test_size=0.2, gap=5)

# Walk-Forward Analysis
print("\n📈 Walk-Forward Analysis 结果:")
wf_splits = ts_cv.walk_forward_split(X_full, y_full)
print(f"生成 {len(wf_splits)} 个时间序列分割")

wf_scores = []
for i, (train_idx, test_idx) in enumerate(wf_splits):
    if len(train_idx) > 50 and len(test_idx) > 10:  # 确保有足够的数据
        X_train_cv, X_test_cv = X_full[train_idx], X_full[test_idx]
        y_train_cv, y_test_cv = y_full[train_idx], y_full[test_idx]

        # 简单线性回归评估
        from sklearn.linear_model import LinearRegression

        model = LinearRegression()
        model.fit(X_train_cv, y_train_cv)
        pred_cv = model.predict(X_test_cv)

        # 计算IC
        ic = np.corrcoef(pred_cv, y_test_cv)[0, 1] if not np.isnan(np.corrcoef(pred_cv, y_test_cv)[0, 1]) else 0
        wf_scores.append(ic)

        print(f"  Fold {i + 1}: IC = {ic:.4f}, 训练样本 = {len(train_idx)}, 测试样本 = {len(test_idx)}")

avg_wf_ic = np.mean(wf_scores)
print(f"Walk-Forward Analysis 平均IC: {avg_wf_ic:.4f} ± {np.std(wf_scores):.4f}")
print("📖 Walk-Forward Analysis IC解释:")
explain_metric('IC', avg_wf_ic)

# Purged Cross-Validation
print("\n🧹 Purged Cross-Validation 结果:")
purged_splits = ts_cv.purged_split(X_full, y_full, embargo_pct=0.02)
print(f"生成 {len(purged_splits)} 个净化分割")

purged_scores = []
for i, (train_idx, test_idx) in enumerate(purged_splits):
    if len(train_idx) > 50 and len(test_idx) > 10:
        X_train_cv, X_test_cv = X_full[train_idx], X_full[test_idx]
        y_train_cv, y_test_cv = y_full[train_idx], y_full[test_idx]

        model = LinearRegression()
        model.fit(X_train_cv, y_train_cv)
        pred_cv = model.predict(X_test_cv)

        ic = np.corrcoef(pred_cv, y_test_cv)[0, 1] if not np.isnan(np.corrcoef(pred_cv, y_test_cv)[0, 1]) else 0
        purged_scores.append(ic)

        print(f"  Fold {i + 1}: IC = {ic:.4f}, 训练样本 = {len(train_idx)}, 测试样本 = {len(test_idx)}")

avg_purged_ic = np.mean(purged_scores)
print(f"Purged Cross-Validation 平均IC: {avg_purged_ic:.4f} ± {np.std(purged_scores):.4f}")
print("📖 Purged Cross-Validation IC解释:")
explain_metric('IC', avg_purged_ic)

# ——————————————————————————————————————————————————————————————————————————————

# 5. Buy & Hold策略
bh_result, bh_cerebro = run_backtest(
    ticker=ticker,
    df=test_data,  #
    start_date=start_date,
    end_date=end_date,  #
    strategy=BuyAndHoldStrategy,
    initial_cash=100000,
    print_log=True,  # 这次打开日志
    timeframe=bt.TimeFrame.Days,
    compression=1  #
)

# ===== 猴子补丁：为 numpy 添加 bool8 和 object 属性 =====
import numpy as np

if not hasattr(np, 'bool8'):
    np.bool8 = np.bool_  # 使用 numpy 自带的 bool_ 类型  #
if not hasattr(np, 'object'):
    np.object = object  # 兼容 backtrader_plotting 的引用

plot_results(bh_cerebro)

# ——————————————————————————————————————————————————————————————————————————————

# 6. 模型训练与超参数优化
import os
import pickle
import hashlib

# 创建缓存目录
cache_dir = 'Quant_ML_Struc/cache/models'
os.makedirs(cache_dir, exist_ok=True)

X_train = train_data[features].values
y_train = train_data['future_ret_1d'].values
X_val = val_data[features].values
y_val = val_data['future_ret_1d'].values
X_test = test_data[features].values
y_test = test_data['future_ret_1d'].values


# 生成数据和参数的哈希值用于缓存键
def generate_cache_key(X_train, y_train, factors, model_type, params, scaler_type="RobustScaler"):
    """生成缓存键，包含标准化方法信息"""
    data_hash = hashlib.md5(str(X_train.shape).encode() + str(y_train.shape).encode()).hexdigest()[:8]
    factors_hash = hashlib.md5(str(sorted(factors)).encode()).hexdigest()[:8]
    params_hash = hashlib.md5(str(sorted(params.items())).encode()).hexdigest()[:8]
    scaler_hash = hashlib.md5(scaler_type.encode()).hexdigest()[:8]
    return f"{model_type}_{scaler_type}_{data_hash}_{factors_hash}_{params_hash}_{scaler_hash}.pkl"


def save_model_cache(model, cache_key, metrics):
    """保存模型到缓存"""
    cache_path = os.path.join(cache_dir, cache_key)
    cache_data = {
        'model': model,
        'metrics': metrics,
        'timestamp': pd.Timestamp.now()
    }
    with open(cache_path, 'wb') as f:
        pickle.dump(cache_data, f)
    print(f"模型已缓存到: {cache_path}")


def load_model_cache(cache_key):
    """从缓存加载模型"""
    cache_path = os.path.join(cache_dir, cache_key)
    if os.path.exists(cache_path):
        with open(cache_path, 'rb') as f:
            cache_data = pickle.load(f)
        print(f"从缓存加载模型: {cache_path}")
        return cache_data['model'], cache_data['metrics']
    return None, None


print(f"使用{len(factors)}个因子进行模型训练...")
print(f"训练集形状: {X_train.shape}")
print(f"验证集形状: {X_val.shape}")
print(f"测试集形状: {X_test.shape}")

# 6.1 训练线性模型
import copy
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import ParameterGrid
from sklearn.pipeline import Pipeline

print("\n=== 训练线性回归模型 ===")

# 定义超参数
param_grid_lr = {
    'lr__fit_intercept': [True, False]
}

# 检查缓存
cache_key_lr = generate_cache_key(X_train, y_train, factors, "LinearRegression", param_grid_lr, "RobustScaler")
best_pipeline_lr, cached_metrics_lr = load_model_cache(cache_key_lr)

if best_pipeline_lr is not None:
    print("✅ 从缓存加载线性回归模型")
    print("缓存的指标:", cached_metrics_lr)
    best_params_lr = cached_metrics_lr['best_params']
    best_score_lr = cached_metrics_lr['best_score']
else:
    print("🔄 开始训练线性回归模型...")

    # 建立 Pipeline
    pipeline_lr = Pipeline([
        ('lr', LinearRegression())
    ])

    # 超参数搜索
    best_score_lr = float('-inf')
    best_params_lr = None
    best_pipeline_lr = None

    for params in ParameterGrid(param_grid_lr):
        pipeline_lr.set_params(**params)
        pipeline_lr.fit(X_train, y_train)

        # 在验证集上进行预测和评估
        valid_pred_lr = pipeline_lr.predict(X_val)
        valid_r2_lr = r2_score(y_val, valid_pred_lr)

        if valid_r2_lr > best_score_lr:
            best_score_lr = valid_r2_lr
            best_params_lr = params
            best_pipeline_lr = copy.deepcopy(pipeline_lr)
            print("更新：", best_score_lr, best_params_lr)

    # 保存到缓存
    metrics_lr = {
        'best_params': best_params_lr,
        'best_score': best_score_lr
    }
    save_model_cache(best_pipeline_lr, cache_key_lr, metrics_lr)

print("最佳参数：", best_params_lr)

# 评估模型
y_pred_train_lr = best_pipeline_lr.predict(X_train)
y_pred_test_lr = best_pipeline_lr.predict(X_test)

train_mse_lr = mean_squared_error(y_train, y_pred_train_lr)
test_mse_lr = mean_squared_error(y_test, y_pred_test_lr)
train_r2_lr = r2_score(y_train, y_pred_train_lr)
test_r2_lr = r2_score(y_test, y_pred_test_lr)

print("==== 线性模型 - 训练集 ====")
print("MSE:", train_mse_lr)
print("R2: ", train_r2_lr)

print("==== 线性模型 - 测试集 ====")
print("MSE:", test_mse_lr)
print("R2: ", test_r2_lr)

# 查看训练后的回归系数和截距
print("Coefficients:", best_pipeline_lr.named_steps['lr'].coef_)
print("Intercept:", best_pipeline_lr.named_steps['lr'].intercept_)

# 6.2 训练随机森林
from sklearn.ensemble import RandomForestRegressor

print("\n=== 训练随机森林模型 ===")

# 定义超参数（简化以减少训练时间）
param_grid_rf = {
    'rf__n_estimators': [500],  # 减少树的数量以加快训练
    'rf__max_depth': [5, 10, 20],
    'rf__min_samples_split': [2, 10],
    'rf__min_samples_leaf': [1, 4],
    'rf__max_features': [0.3, 'sqrt']
}

# 检查缓存
cache_key_rf = generate_cache_key(X_train, y_train, factors, "RandomForest", param_grid_rf, "RobustScaler")
best_pipeline_rf, cached_metrics_rf = load_model_cache(cache_key_rf)

if best_pipeline_rf is not None:
    print("✅ 从缓存加载随机森林模型")
    print("缓存的指标:", cached_metrics_rf)
    best_params_rf = cached_metrics_rf['best_params']
    best_score_rf = cached_metrics_rf['best_score']
else:
    print("🔄 开始训练随机森林模型...")

    # 建立 Pipeline
    pipeline_rf = Pipeline([
        ('rf', RandomForestRegressor(random_state=42))
    ])

    # 超参数搜索
    best_score_rf = float('-inf')
    best_params_rf = None
    best_pipeline_rf = None

    for params in ParameterGrid(param_grid_rf):
        pipeline_rf.set_params(**params)
        pipeline_rf.fit(X_train, y_train)

        # 在验证集上进行预测并计算 R2 得分
        valid_pred_rf = pipeline_rf.predict(X_val)
        valid_r2_rf = r2_score(y_val, valid_pred_rf)

        if valid_r2_rf > best_score_rf:
            best_score_rf = valid_r2_rf
            best_params_rf = params
            best_pipeline_rf = copy.deepcopy(pipeline_rf)
            print("更新：", best_score_rf, best_params_rf)

    # 保存到缓存
    metrics_rf = {
        'best_params': best_params_rf,
        'best_score': best_score_rf
    }
    save_model_cache(best_pipeline_rf, cache_key_rf, metrics_rf)

print("最佳参数：", best_params_rf)

# 评估模型
y_pred_train_rf = best_pipeline_rf.predict(X_train)
y_pred_test_rf = best_pipeline_rf.predict(X_test)

train_mse_rf = mean_squared_error(y_train, y_pred_train_rf)
test_mse_rf = mean_squared_error(y_test, y_pred_test_rf)
train_r2_rf = r2_score(y_train, y_pred_train_rf)
test_r2_rf = r2_score(y_test, y_pred_test_rf)

print("==== 随机森林 - 训练集 ====")
print("MSE:", train_mse_rf)
print("R2 :", train_r2_rf)

print("==== 随机森林 - 测试集 ====")
print("MSE:", test_mse_rf)
print("R2 :", test_r2_rf)

# 查看特征重要性
feature_importances = best_pipeline_rf.named_steps['rf'].feature_importances_
print("\n==== 特征重要性 (Top 10) ====")
sorted_idx = np.argsort(feature_importances)[::-1]
for i, idx in enumerate(sorted_idx[:10]):
    print(f"{i + 1}. {features[idx]} -> {feature_importances[idx]:.4f}")

# 6.3 训练SHAP-XGBoost
from xgboost import XGBRegressor

print("\n=== 训练SHAP-XGBoost模型 ===")

# 定义超参数（简化以减少训练时间）
param_grid_xgb = {
    'xgb__n_estimators': [100, 300],  # 减少树的数量
    'xgb__learning_rate': [0.05, 0.1],
    'xgb__max_depth': [3, 6],
    'xgb__subsample': [0.8, 1.0]
}

# 检查缓存
cache_key_xgb = generate_cache_key(X_train, y_train, factors, "SHAP-XGBoost", param_grid_xgb, "RobustScaler")
best_pipeline_xgb, cached_metrics_xgb = load_model_cache(cache_key_xgb)

if best_pipeline_xgb is not None:
    print("✅ 从缓存加载SHAP-XGBoost模型")
    print("缓存的指标:", cached_metrics_xgb)
    best_params_xgb = cached_metrics_xgb['best_params']
    best_score_xgb = cached_metrics_xgb['best_score']
else:
    print("🔄 开始训练SHAP-XGBoost模型...")

    # 建立 Pipeline
    pipeline_xgb = Pipeline([
        ('xgb', XGBRegressor(random_state=42, verbosity=0))
    ])

    # 超参数搜索
    best_score_xgb = float('-inf')
    best_params_xgb = None
    best_pipeline_xgb = None

    for params in ParameterGrid(param_grid_xgb):
        pipeline_xgb.set_params(**params)
        pipeline_xgb.fit(X_train, y_train)

        # 在验证集上进行预测并计算 R² 得分
        valid_pred_xgb = pipeline_xgb.predict(X_val)
        valid_r2_xgb = r2_score(y_val, valid_pred_xgb)

        if valid_r2_xgb > best_score_xgb:
            best_score_xgb = valid_r2_xgb
            best_params_xgb = params
            best_pipeline_xgb = copy.deepcopy(pipeline_xgb)
            print("更新：", best_score_xgb, best_params_xgb)

    # 保存到缓存
    metrics_xgb = {
        'best_params': best_params_xgb,
        'best_score': best_score_xgb
    }
    save_model_cache(best_pipeline_xgb, cache_key_xgb, metrics_xgb)

print("最佳参数：", best_params_xgb)

# 评估模型
y_pred_train_xgb = best_pipeline_xgb.predict(X_train)
y_pred_test_xgb = best_pipeline_xgb.predict(X_test)

train_mse_xgb = mean_squared_error(y_train, y_pred_train_xgb)
test_mse_xgb = mean_squared_error(y_test, y_pred_test_xgb)
train_r2_xgb = r2_score(y_train, y_pred_train_xgb)
test_r2_xgb = r2_score(y_test, y_pred_test_xgb)

print("==== XGBoost - 训练集 ====")
print("MSE:", train_mse_xgb)
print("R2: ", train_r2_xgb)

print("==== XGBoost - 测试集 ====")
print("MSE:", test_mse_xgb)
print("R2: ", test_r2_xgb)

# 查看特征重要性
feature_importances_xgb = best_pipeline_xgb.named_steps['xgb'].feature_importances_
print("\n==== SHAP-XGBoost特征重要性 (Top 10) ====")
sorted_idx_xgb = np.argsort(feature_importances_xgb)[::-1]
for i, idx in enumerate(sorted_idx_xgb[:10]):
    print(f"{i + 1}. {features[idx]} -> {feature_importances_xgb[idx]:.4f}")

# 📊 新增：SHAP可解释性分析
print("\n==== SHAP可解释性分析 ====")
shap_analyzer = None
if SHAP_AVAILABLE:
    try:
        # 创建SHAP分析器
        xgb_model = best_pipeline_xgb.named_steps['xgb']
        shap_analyzer = SHAPAnalyzer(xgb_model, X_train, factors)

        # 创建SHAP解释器
        shap_analyzer.create_explainer('tree')

        # 计算SHAP值
        shap_analyzer.calculate_shap_values(X_test)

        # 获取特征重要性排名
        shap_importance = shap_analyzer.get_feature_importance_ranking()

        print("✅ SHAP分析完成")

    except Exception as e:
        print(f"⚠️ SHAP分析失败: {e}")
        shap_analyzer = None
else:
    print("⚠️ SHAP未安装，跳过可解释性分析")

# 6.4 训练MLP
from sklearn.neural_network import MLPRegressor

print("\n=== 训练MLP模型 ===")

# 定义超参数（简化以减少训练时间）
param_grid_mlp = {
    'mlp__hidden_layer_sizes': [(64, 64), (128, 128)],  # 减少网络复杂度
    'mlp__alpha': [1e-3, 1e-2],
    'mlp__learning_rate_init': [1e-3, 1e-2],
    'mlp__solver': ['adam']  # 只使用adam优化器
}

# 检查缓存
cache_key_mlp = generate_cache_key(X_train, y_train, factors, "MLP", param_grid_mlp, "RobustScaler")
best_pipeline_mlp, cached_metrics_mlp = load_model_cache(cache_key_mlp)

if best_pipeline_mlp is not None:
    print("✅ 从缓存加载MLP模型")
    print("缓存的指标:", cached_metrics_mlp)
    best_params_mlp = cached_metrics_mlp['best_params']
    best_score_mlp = cached_metrics_mlp['best_score']
else:
    print("🔄 开始训练MLP模型...")

    # 建立 Pipeline
    pipeline_mlp = Pipeline([
        ('scaler', RobustScaler()),
        ('mlp', MLPRegressor(random_state=42, max_iter=500))  # 减少迭代次数
    ])

    # 超参数搜索
    best_score_mlp = float('-inf')
    best_params_mlp = None
    best_pipeline_mlp = None

    for params in ParameterGrid(param_grid_mlp):
        pipeline_mlp.set_params(**params)
        pipeline_mlp.fit(X_train, y_train)

        # 在验证集上进行预测和评估
        valid_pred_mlp = pipeline_mlp.predict(X_val)
        valid_r2_mlp = r2_score(y_val, valid_pred_mlp)

        if valid_r2_mlp > best_score_mlp:
            best_score_mlp = valid_r2_mlp
            best_params_mlp = params
            best_pipeline_mlp = copy.deepcopy(pipeline_mlp)
            print('更新:', best_score_mlp, best_params_mlp)

    # 保存到缓存
    metrics_mlp = {
        'best_params': best_params_mlp,
        'best_score': best_score_mlp
    }
    save_model_cache(best_pipeline_mlp, cache_key_mlp, metrics_mlp)

print("最佳参数:", best_params_mlp)

# 评估模型
y_pred_train_mlp = best_pipeline_mlp.predict(X_train)
y_pred_test_mlp = best_pipeline_mlp.predict(X_test)

train_mse_mlp = mean_squared_error(y_train, y_pred_train_mlp)
test_mse_mlp = mean_squared_error(y_test, y_pred_test_mlp)
train_r2_mlp = r2_score(y_train, y_pred_train_mlp)
test_r2_mlp = r2_score(y_test, y_pred_test_mlp)

print("==== MLP - 训练集 ====")
print("MSE:", train_mse_mlp)
print("R2: ", train_r2_mlp)

print("==== MLP - 测试集 ====")
print("MSE:", test_mse_mlp)
print("R2: ", test_r2_mlp)

# ——————————————————————————————————————————————————————————————————————————————

# 📊 新增：多维度评估指标分析
print("\n" + "=" * 80)
print("📊 开始多维度评估指标分析")
print("=" * 80)

# 收集所有模型的预测结果
models_dict = {
    'LinearRegression': best_pipeline_lr,
    'RandomForest': best_pipeline_rf,
    'SHAP-XGBoost': best_pipeline_xgb,
    'MLP': best_pipeline_mlp
}

predictions_dict = {}
for name, model in models_dict.items():
    predictions_dict[name] = model.predict(X_test)

# 生成综合评估报告
generate_comprehensive_evaluation_report(
    models=list(models_dict.values()),
    model_names=list(models_dict.keys()),
    X_test=X_test,
    y_test=y_test,
    predictions_dict=predictions_dict
)

# ——————————————————————————————————————————————————————————————————————————————

# 7. 模型集成与权重优化（用凸优化）
import cvxpy as cp


def optimize_weights_constrained(
        models,
        X_val,
        y_val,
        sum_to_1=True,  # 是否约束权重和=1
        nonnegative=True,  # 是否要求所有权重>=0
        alpha_l1=0.0,  # L1正则系数
        alpha_l2=0.0,  # L2正则系数
        verbose=True
):
    """
    用凸优化方式在验证集上寻找一组最优权重，使得加权后的预测最小化 MSE
    """
    # 1) 先得到在验证集上的预测矩阵 predictions: shape (N, M)
    predictions = np.column_stack([model.predict(X_val) for model in models])
    N, M = predictions.shape

    # 2) 定义优化变量 w: 大小 M
    if nonnegative:
        w = cp.Variable(M, nonneg=True)
    else:
        w = cp.Variable(M)

    # 3) 定义约束列表 constraints
    constraints = []
    if sum_to_1:
        constraints.append(cp.sum(w) == 1)

    # 4) 定义目标函数（最小化 MSE + 正则项）
    residual = y_val - predictions @ w
    obj_mse = cp.sum_squares(residual)

    # 若要加 L1 正则：alpha_l1 * ||w||_1
    # 若要加 L2 正则：alpha_l2 * ||w||_2^2
    obj_reg = 0
    if alpha_l1 > 0:
        obj_reg += alpha_l1 * cp.norm1(w)
    if alpha_l2 > 0:
        obj_reg += alpha_l2 * cp.norm2(w) ** 2

    # 最终目标：Minimize(MSE + 正则)
    objective = cp.Minimize(obj_mse + obj_reg)

    # 5) 构建并求解凸优化问题
    problem = cp.Problem(objective, constraints)
    result = problem.solve(verbose=verbose)

    # 6) 拿到最优权重 w_opt
    w_opt = w.value
    # 计算该组合在验证集上的 r2_score
    y_val_pred = predictions @ w_opt
    score_r2 = r2_score(y_val, y_val_pred)

    if verbose:
        print(f"Optimal objective (MSE + reg) = {problem.value:.6f}")
        print("Optimized weights:", w_opt)
        print(f"sum of weights = {w_opt.sum():.4f}")
        print(f"R2 on validation set = {score_r2:.4f}")

    return w_opt, score_r2


# 使用凸优化进行权重分配
w_constrained, r2_constrained = optimize_weights_constrained(
    models=[best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb, best_pipeline_mlp],
    X_val=X_val,
    y_val=y_val,
    sum_to_1=True,
    nonnegative=True,
    alpha_l1=0.0,
    alpha_l2=1e-3,
    verbose=False
)

print("得到的约束权重 =", [f"{num:0.2f}" for num in w_constrained])
print("验证集 R² =", r2_constrained)

# 1. 得到测试集上各模型的预测矩阵
predictions_test = np.column_stack([model.predict(X_test) for model in
                                    [best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb,
                                     best_pipeline_mlp]])

# 2. 利用之前优化得到的权重 w_constrained 对测试集预测进行加权组合
y_test_pred = predictions_test @ w_constrained

# 3. 计算测试集上的 R² 分数
r2_test = r2_score(y_test, y_test_pred)
print("测试集 R² =", r2_test)

# 📊 新增：集成模型的多维度评估
print("\n" + "=" * 80)
print("🎯 集成模型多维度评估")
print("=" * 80)

# 将集成模型加入评估
predictions_dict['Ensemble'] = y_test_pred

# 重新生成包含集成模型的评估报告
generate_comprehensive_evaluation_report(
    models=list(models_dict.values()) + ['Ensemble'],
    model_names=list(models_dict.keys()) + ['Ensemble'],
    X_test=X_test,
    y_test=y_test,
    predictions_dict=predictions_dict
)


# ——————————————————————————————————————————————————————————————————————————————

# 8. 高效因子计算器（与原版保持一致）
class EfficientFactorCalculator:
    """
    高效的Alpha因子计算器，支持实时增量计算25个因子
    优化特性：
    1. 滚动窗口缓存
    2. 向量化计算
    3. 增量更新
    4. 内存优化
    """

    def __init__(self, max_window=50):
        self.max_window = max_window  # 最大历史窗口
        self.price_history = []  # OHLCV历史数据
        self.factor_cache = {}  # 因子缓存
        self.initialized = False

    def add_data_point(self, open_price, high, low, close, volume):
        """添加新的数据点"""
        data_point = {
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        }

        self.price_history.append(data_point)

        # 保持固定窗口大小
        if len(self.price_history) > self.max_window:
            self.price_history.pop(0)

        # 标记需要重新计算
        self.factor_cache.clear()

    def get_price_series(self, field, length=None):
        """获取价格序列"""
        if length is None:
            length = len(self.price_history)

        start_idx = max(0, len(self.price_history) - length)
        return [data[field] for data in self.price_history[start_idx:]]

    def calculate_all_factors(self):
        """计算所有25个因子"""
        if len(self.price_history) < 35:  # 需要足够的历史数据
            return [0.0] * 25

        factors = {}

        # 获取基础数据
        opens = self.get_price_series('open')
        highs = self.get_price_series('high')
        lows = self.get_price_series('low')
        closes = self.get_price_series('close')
        volumes = self.get_price_series('volume')

        current_close = closes[-1]
        current_open = opens[-1]
        current_high = highs[-1]
        current_low = lows[-1]
        current_volume = volumes[-1]

        # === 1. K线形态因子 ===
        # 下影线长度
        factors['lower_shadow'] = (min(current_close, current_open) - current_low) / current_close

        # === 2. 价量关系因子 ===
        # 20日价量相关性
        if len(closes) >= 21:
            price_changes = [(closes[i] - closes[i - 20]) / closes[i - 20] for i in range(20, len(closes))]
            volume_changes = [(volumes[i] - volumes[i - 20]) / volumes[i - 20] for i in range(20, len(volumes)) if
                              volumes[i - 20] > 0]
            if len(price_changes) > 0 and len(volume_changes) > 0:
                factors['price_volume_correlation_20d'] = price_changes[-1] * volume_changes[-1]
            else:
                factors['price_volume_correlation_20d'] = 0.0
        else:
            factors['price_volume_correlation_20d'] = 0.0

        # 10日价量相关性
        if len(closes) >= 11:
            price_returns = [(closes[i] - closes[i - 1]) / closes[i - 1] for i in range(1, len(closes)) if
                             closes[i - 1] > 0]
            volume_returns = [(volumes[i] - volumes[i - 1]) / volumes[i - 1] for i in range(1, len(volumes)) if
                              volumes[i - 1] > 0]
            if len(price_returns) >= 10 and len(volume_returns) >= 10:
                # 简化相关性计算
                recent_price = price_returns[-10:]
                recent_volume = volume_returns[-10:]
                if len(recent_price) == len(recent_volume):
                    factors['price_volume_corr_10d'] = sum(p * v for p, v in zip(recent_price, recent_volume)) / 10
                else:
                    factors['price_volume_corr_10d'] = 0.0
            else:
                factors['price_volume_corr_10d'] = 0.0
        else:
            factors['price_volume_corr_10d'] = 0.0

        # === 3. 流动性因子 (Amihud非流动性系列) ===
        def calculate_amihud(period):
            if len(closes) >= period + 1:
                amihud_values = []
                for i in range(period, len(closes)):
                    if closes[i - 1] > 0 and volumes[i] > 0:
                        ret = abs((closes[i] - closes[i - 1]) / closes[i - 1])
                        dollar_vol = closes[i] * volumes[i]
                        amihud_values.append(ret / (dollar_vol + 1e-8))
                return sum(amihud_values[-period:]) / min(len(amihud_values), period) if amihud_values else 0.0
            return 0.0

        factors['amihud_illiquidity_5d'] = calculate_amihud(5)
        factors['amihud_illiquidity_10d'] = calculate_amihud(10)
        factors['amihud_illiquidity_20d'] = calculate_amihud(20)
        factors['amihud_illiquidity_30d'] = calculate_amihud(30)

        # === 4. 波动率因子 (ATR系列) ===
        def calculate_atr(period):
            if len(closes) >= period + 1:
                tr_values = []
                for i in range(1, len(closes)):
                    tr1 = highs[i] - lows[i]
                    tr2 = abs(highs[i] - closes[i - 1])
                    tr3 = abs(lows[i] - closes[i - 1])
                    tr_values.append(max(tr1, tr2, tr3))
                return sum(tr_values[-period:]) / min(len(tr_values), period) if tr_values else 0.0
            return 0.0

        factors['atr_7d'] = calculate_atr(7)
        factors['atr_10d'] = calculate_atr(10)
        factors['atr_14d'] = calculate_atr(14)
        factors['atr_20d'] = calculate_atr(20)

        # === 5. 收益率波动率 ===
        if len(closes) >= 21:
            returns = [(closes[i] - closes[i - 1]) / closes[i - 1] for i in range(1, len(closes)) if closes[i - 1] > 0]
            if len(returns) >= 20:
                recent_returns = returns[-20:]
                mean_ret = sum(recent_returns) / len(recent_returns)
                variance = sum((r - mean_ret) ** 2 for r in recent_returns) / len(recent_returns)
                factors['return_volatility_20d'] = variance ** 0.5
            else:
                factors['return_volatility_20d'] = 0.0
        else:
            factors['return_volatility_20d'] = 0.0

        # === 6. CCI指标 ===
        def calculate_cci(period):
            if len(closes) >= period:
                typical_prices = [(highs[i] + lows[i] + closes[i]) / 3 for i in range(len(closes))]
                recent_tp = typical_prices[-period:]
                sma = sum(recent_tp) / len(recent_tp)
                mad = sum(abs(tp - sma) for tp in recent_tp) / len(recent_tp)
                if mad > 0:
                    return (typical_prices[-1] - sma) / (0.015 * mad)
                return 0.0
            return 0.0

        factors['cci_20d'] = calculate_cci(20)
        factors['cci_30d'] = calculate_cci(30)

        # === 7. 布林带因子 ===
        if len(closes) >= 20:
            recent_closes = closes[-20:]
            ma_20 = sum(recent_closes) / len(recent_closes)
            variance = sum((c - ma_20) ** 2 for c in recent_closes) / len(recent_closes)
            std_20 = variance ** 0.5
            upper_band = ma_20 + 2.0 * std_20
            lower_band = ma_20 - 2.0 * std_20
            if upper_band > lower_band:
                factors['bollinger_position_20d'] = (current_close - lower_band) / (upper_band - lower_band)
            else:
                factors['bollinger_position_20d'] = 0.5
                factors['bollinger_position_20d_2.0std'] = 0.5
        else:
            factors['bollinger_position_20d'] = 0.5
            factors['bollinger_position_20d_2.0std'] = 0.5

        # === 8. 动量因子 ===
        def calculate_momentum_strength(period):
            if len(closes) >= period + 10:
                momentum = (closes[-1] / closes[-period - 1] - 1) if closes[-period - 1] > 0 else 0
                recent_momentum = [(closes[i] / closes[i - period] - 1) for i in range(period, len(closes)) if
                                   closes[i - period] > 0]
                if len(recent_momentum) >= 10:
                    recent_10 = recent_momentum[-10:]
                    mean_mom = sum(recent_10) / len(recent_10)
                    variance = sum((m - mean_mom) ** 2 for m in recent_10) / len(recent_10)
                    std_mom = variance ** 0.5
                    return momentum / (std_mom + 1e-8)
                return momentum
            return 0.0

        factors['momentum_strength_10d'] = calculate_momentum_strength(10)
        factors['momentum_20d'] = (closes[-1] / closes[-21] - 1) if len(closes) >= 21 and closes[-21] > 0 else 0.0
        factors['momentum_volatility_ratio_10d'] = factors['momentum_20d']  # 简化处理

        # === 9. 价格变异系数 ===
        if len(closes) >= 30:
            returns_30d = [(closes[i] - closes[i - 1]) / closes[i - 1] for i in range(1, len(closes)) if
                           closes[i - 1] > 0]
            if len(returns_30d) >= 30:
                recent_30 = returns_30d[-30:]
                mean_ret = sum(recent_30) / len(recent_30)
                variance = sum((r - mean_ret) ** 2 for r in recent_30) / len(recent_30)
                std_ret = variance ** 0.5
                factors['price_cv_30d'] = std_ret / (abs(mean_ret) + 1e-8)
            else:
                factors['price_cv_30d'] = 0.0
        else:
            factors['price_cv_30d'] = 0.0

        # === 10. 均值回复因子 ===
        if len(closes) >= 12:
            returns = [(closes[i] - closes[i - 1]) / closes[i - 1] for i in range(1, len(closes)) if closes[i - 1] > 0]
            if len(returns) >= 11:
                recent_10 = returns[-10:]
                if len(recent_10) >= 2:
                    # 简化自相关计算
                    lag1_corr = sum(recent_10[i] * recent_10[i - 1] for i in range(1, len(recent_10))) / (
                                len(recent_10) - 1)
                    factors['mean_reversion_state_10d'] = -lag1_corr
                    factors['mean_reversion_strength_10d'] = lag1_corr
                else:
                    factors['mean_reversion_state_10d'] = 0.0
                    factors['mean_reversion_strength_10d'] = 0.0
            else:
                factors['mean_reversion_state_10d'] = 0.0
                factors['mean_reversion_strength_10d'] = 0.0
        else:
            factors['mean_reversion_state_10d'] = 0.0
            factors['mean_reversion_strength_10d'] = 0.0

        # === 11. 波动率调整收益率 ===
        if len(closes) >= 11:
            returns_10d = (closes[-1] / closes[-11] - 1) if closes[-11] > 0 else 0
            returns = [(closes[i] - closes[i - 1]) / closes[i - 1] for i in range(1, len(closes)) if closes[i - 1] > 0]
            if len(returns) >= 10:
                recent_10 = returns[-10:]
                mean_ret = sum(recent_10) / len(recent_10)
                variance = sum((r - mean_ret) ** 2 for r in recent_10) / len(recent_10)
                volatility_10d = variance ** 0.5
                factors['volatility_adjusted_return_10d'] = returns_10d / (volatility_10d + 1e-8)
            else:
                factors['volatility_adjusted_return_10d'] = 0.0
        else:
            factors['volatility_adjusted_return_10d'] = 0.0

        # === 12. 价格相对位置 ===
        if len(closes) >= 20:
            recent_highs = highs[-20:]
            recent_lows = lows[-20:]
            highest_20d = max(recent_highs)
            lowest_20d = min(recent_lows)
            if highest_20d > lowest_20d:
                factors['price_position_20d'] = (current_close - lowest_20d) / (highest_20d - lowest_20d)
            else:
                factors['price_position_20d'] = 0.5
        else:
            factors['price_position_20d'] = 0.5

        # === 13. 成交量比率 ===
        if len(volumes) >= 20:
            recent_volumes = volumes[-20:]
            vol_ma_20d = sum(recent_volumes) / len(recent_volumes)
            factors['volume_ratio_20d'] = current_volume / (vol_ma_20d + 1e-8)
        else:
            factors['volume_ratio_20d'] = 1.0

        # 按照训练时的因子顺序返回
        factor_order = [
            'lower_shadow', 'price_volume_correlation_20d', 'amihud_illiquidity_20d',
            'amihud_illiquidity_5d', 'amihud_illiquidity_10d', 'cci_20d', 'atr_14d',
            'atr_7d', 'atr_10d', 'price_volume_corr_10d', 'bollinger_position_20d',
            'momentum_strength_10d', 'price_cv_30d', 'cci_30d', 'mean_reversion_state_10d',
            'mean_reversion_strength_10d', 'volatility_adjusted_return_10d',
            'momentum_volatility_ratio_10d', 'atr_20d', 'amihud_illiquidity_30d',
            'bollinger_position_20d_2.0std', 'price_position_20d', 'volume_ratio_20d',
            'return_volatility_20d', 'momentum_20d'
        ]

        return [factors.get(factor, 0.0) for factor in factor_order]


# ——————————————————————————————————————————————————————————————————————————————

# 9. 自定义成交量指标
class MyVolumeIndicator(bt.Indicator):
    lines = ('vol',)
    params = (('period', 1),)

    def __init__(self):
        self.lines.vol = self.data.volume

    def next(self):
        self.lines.vol[0] = self.data.volume[0]


# 10. 集成模型策略类（增强版）
class MLEnsembleStrategy(bt.Strategy):
    params = (
        ('target_percent', 0.98),  # 目标仓位百分比
        ('models', "4_models"),  # 模型列表描述
        ('weights', "optimized"),  # 权重列表描述
    )

    def __init__(self):
        # 从strategy_params获取实际的模型和权重
        self.models = getattr(self, '_models', None)
        self.weights = getattr(self, '_weights', None)

        # 初始化高效因子计算器
        self.factor_calculator = EfficientFactorCalculator(max_window=50)

        # 关闭主图中Data自带的Volume绘制
        self.data.plotinfo.plotvolume = False

        # 自定义成交量指标以及其SMA指标
        self.myvol = MyVolumeIndicator(self.data)
        self.vol_5 = bt.indicators.SMA(self.myvol.vol, period=5)
        self.vol_5.plotinfo.subplot = True
        self.vol_10 = bt.indicators.SMA(self.myvol.vol, period=10)
        self.vol_10.plotinfo.subplot = True

        # 添加其它因子指标
        # 价格动量指标：计算5日价格百分比变化
        self.momentum_5 = bt.indicators.PercentChange(self.data.close, period=5)

        # RSI指标，14日周期
        self.rsi_14 = bt.indicators.RSI(self.data.close, period=14)

        # 布林带指标，默认20日均线和2倍标准差，返回上轨、均线和下轨
        self.bb = bt.indicators.BollingerBands(self.data.close)

        self.last_trade_type = None  # 记录上一次交易类型（buy/sell）

        self.value_history_dates = []
        self.value_history_values = []

        # 性能监控
        self.factor_calculation_times = []
        self.prediction_times = []
        self.total_predictions = 0

        # 📊 新增：交易性能跟踪
        self.trade_returns = []
        self.trade_signals = []
        self.trade_dates = []

        print("✅ MLEnsembleStrategy初始化完成")
        print(f"📊 使用{len(self.models)}个模型进行集成预测")
        print(f"⚖️ 模型权重: {[f'{w:.3f}' for w in self.weights]}")
        print("🚀 启用高效25因子实时计算系统")
        print("📈 启用多维度性能评估")

    def next(self):
        import time

        # 性能监控：因子计算时间
        start_time = time.time()

        # 使用高效因子计算器计算完整的25个因子
        # 添加当前数据点到计算器
        self.factor_calculator.add_data_point(
            open_price=self.data.open[0],
            high=self.data.high[0],
            low=self.data.low[0],
            close=self.data.close[0],
            volume=self.data.volume[0]
        )

        # 计算所有25个因子
        factors = self.factor_calculator.calculate_all_factors()
        factor_calc_time = time.time() - start_time
        self.factor_calculation_times.append(factor_calc_time)

        # 构建特征向量：与训练时完全一致的25个因子
        X = [factors]

        # 性能监控：预测时间
        pred_start_time = time.time()

        # 获取各模型的预测值
        predictions = np.array([model.predict(X)[0] for model in self.models])

        # 加权平均得到集成预测
        pred_ret = np.sum(predictions * self.weights)

        pred_time = time.time() - pred_start_time
        self.prediction_times.append(pred_time)
        self.total_predictions += 1

        # 📊 记录预测信号
        self.trade_signals.append(pred_ret)
        self.trade_dates.append(self.datas[0].datetime.date(0))

        # 获取当前持仓状态
        current_position = self.getposition().size

        if pred_ret > 0 and current_position == 0:
            # 只有当当前没有仓位时，才执行买入
            self.order_target_percent(target=self.p.target_percent)
            self.last_trade_type = "BUY"
            print(f"{self.datas[0].datetime.date(0)} => 🟢 BUY signal, pred_ret={pred_ret:.6f}")
            print(f"📊 完整25因子: {[f'{f:.4f}' for f in factors[:5]]}... (显示前5个)")
            print(f"⚡ 因子计算耗时: {factor_calc_time * 1000:.2f}ms, 预测耗时: {pred_time * 1000:.2f}ms")

        elif pred_ret <= 0 and current_position > 0:
            # 只有当当前有仓位时，才执行卖出
            self.order_target_percent(target=0.0)
            self.last_trade_type = "SELL"
            print(f"{self.datas[0].datetime.date(0)} => 🔴 SELL signal, pred_ret={pred_ret:.6f}")
            print(f"📊 完整25因子: {[f'{f:.4f}' for f in factors[:5]]}... (显示前5个)")
            print(f"⚡ 因子计算耗时: {factor_calc_time * 1000:.2f}ms, 预测耗时: {pred_time * 1000:.2f}ms")

        # 只在交易执行时打印仓位信息
        if self.last_trade_type:
            print(f"💰 Current position size: {self.getposition().size}, Value: {self.broker.getvalue()}")

        # 每100次预测打印性能统计
        if self.total_predictions % 100 == 0:
            avg_factor_time = np.mean(self.factor_calculation_times[-100:]) * 1000
            avg_pred_time = np.mean(self.prediction_times[-100:]) * 1000
            print(f"📈 性能统计(最近100次): 平均因子计算 {avg_factor_time:.2f}ms, 平均预测 {avg_pred_time:.2f}ms")

        dt = self.data.datetime.date(0)
        self.value_history_dates.append(dt)
        self.value_history_values.append(self.broker.getvalue())

        # 📊 记录实际收益率（用于后续评估）
        if len(self.value_history_values) > 1:
            daily_return = (self.value_history_values[-1] / self.value_history_values[-2]) - 1
            self.trade_returns.append(daily_return)

    def stop(self):
        """策略结束时的性能总结"""
        print("\n" + "=" * 80)
        print("🎯 策略执行完成 - 性能总结")
        print("=" * 80)

        if hasattr(self, 'factor_calculation_times') and len(self.factor_calculation_times) > 0:
            factor_times = self.factor_calculation_times
            pred_times = self.prediction_times
            total_preds = self.total_predictions

            print(f"📊 总预测次数: {total_preds}")
            print(f"⚡ 平均因子计算时间: {np.mean(factor_times) * 1000:.2f}ms")
            print(f"⚡ 平均预测时间: {np.mean(pred_times) * 1000:.2f}ms")
            print(f"⚡ 总平均处理时间: {(np.mean(factor_times) + np.mean(pred_times)) * 1000:.2f}ms")
            print(f"🎯 最大因子计算时间: {np.max(factor_times) * 1000:.2f}ms")
            print(f"🎯 最大预测时间: {np.max(pred_times) * 1000:.2f}ms")

            # 计算性能稳定性
            factor_std = np.std(factor_times) * 1000
            pred_std = np.std(pred_times) * 1000
            print(f"📈 因子计算时间标准差: {factor_std:.2f}ms")
            print(f"📈 预测时间标准差: {pred_std:.2f}ms")

        # 📊 交易性能评估
        if len(self.trade_returns) > 0:
            metrics_calc = AdvancedMetricsCalculator()

            returns_array = np.array(self.trade_returns)
            signals_array = np.array(self.trade_signals[:-1])  # 去掉最后一个信号

            # 计算各种指标
            sharpe = metrics_calc.sharpe_ratio(returns_array)
            max_dd = metrics_calc.max_drawdown(returns_array)
            calmar = metrics_calc.calmar_ratio(returns_array)
            sortino = metrics_calc.sortino_ratio(returns_array)

            print(f"\n💰 实际交易性能指标:")
            print(f"  夏普比率: {sharpe:.4f}")
            print(f"  最大回撤: {max_dd:.4f} ({max_dd * 100:.2f}%)")
            print(f"  卡尔马比率: {calmar:.4f}")
            print(f"  索提诺比率: {sortino:.4f}")

            # 年化收益率
            annual_return = np.mean(returns_array) * 252
            print(f"  年化收益率: {annual_return:.4f} ({annual_return * 100:.2f}%)")

            # 信息系数
            if len(signals_array) == len(returns_array):
                ic = metrics_calc.information_coefficient(signals_array, returns_array)
                ir = metrics_calc.information_ratio(signals_array, returns_array)
                print(f"  信息系数(IC): {ic:.6f}")
                print(f"  信息比率(IR): {ir:.6f}")

                # 胜率和盈亏比
                win_rate, profit_ratio = metrics_calc.win_rate_and_profit_ratio(signals_array, returns_array)
                print(f"  胜率: {win_rate:.4f} ({win_rate * 100:.2f}%)")
                print(f"  盈亏比: {profit_ratio:.4f}")

        print("\n✅ 优化效果总结:")
        print("  - 训练和交易使用相同的25个因子，确保一致性")
        print("  - 高效增量计算，避免重复计算历史数据")
        print("  - 固定内存窗口，防止内存泄漏")
        print("  - 向量化计算，提升计算效率")
        print("  - 多维度评估指标，全面评估策略表现")
        print("=" * 80)


# ——————————————————————————————————————————————————————————————————————————————

# 11. 运行集成模型策略回测
print("\n" + "=" * 80)
print("🚀 开始运行集成模型策略回测")
print("=" * 80)

# 准备模型列表
models = [best_pipeline_lr, best_pipeline_rf, best_pipeline_xgb, best_pipeline_mlp]


# 创建自定义策略类来传递模型和权重
class CustomMLEnsembleStrategy(MLEnsembleStrategy):
    def __init__(self):
        self._models = models
        self._weights = w_constrained
        super().__init__()


# 运行集成模型策略
ml_ensemble_result, ml_ensemble_cerebro = run_backtest(
    ticker=ticker,
    df=test_data,
    start_date=start_date,
    end_date=end_date,
    strategy=CustomMLEnsembleStrategy,
    initial_cash=100000,
    print_log=True,
    timeframe=bt.TimeFrame.Days,
    compression=1
)

# 📊 修复：使用与17w.py相同的简单有效方法
# ===== 猴子补丁：为 numpy 添加 bool8 和 object 属性 =====
import numpy as np

if not hasattr(np, 'bool8'):
    np.bool8 = np.bool_  # 使用 numpy 自带的 bool_ 类型
if not hasattr(np, 'object'):
    np.object = object  # 兼容 backtrader_plotting 的引用

# 🔧 关键修复：像17w.py一样，直接调用plot_results
print("\n🌐 生成浏览器可视化图表...")
plot_results(ml_ensemble_cerebro)

# 12. 比较策略和Buy&Hold
# 🔧 修复：像17w.py一样，在plot_results之后调用cerebro.run()
print("📊 进行策略对比分析...")

# 像17w.py一样的处理方式
results = ml_ensemble_cerebro.run()  # cerebro.run() 返回一个列表，每个元素是一个策略实例
ml_strategy_instance = results[0]  # 如果你只有一个策略，就取第一个

results = bh_cerebro.run()
bh_strategy_instance = results[0]

# 计算基本对比指标
ml_total_return = (ml_ensemble_result['final_value'] / 100000) - 1
bh_total_return = (bh_result['final_value'] / 100000) - 1
excess_return = ml_total_return - bh_total_return

print(f"\n📈 策略对比结果:")
print(f"  集成模型总收益率: {ml_total_return:.4f} ({ml_total_return * 100:.2f}%)")
print(f"  买入持有总收益率: {bh_total_return:.4f} ({bh_total_return * 100:.2f}%)")
print(f"  超额收益: {excess_return:.4f} ({excess_return * 100:.2f}%)")

print(f"\n⚖️ 风险调整指标对比:")
print(f"  集成模型夏普比率: {ml_ensemble_result.get('sharpe_ratio', 'N/A')}")
print(f"  买入持有夏普比率: {bh_result.get('sharpe_ratio', 'N/A')}")
print(f"  集成模型最大回撤: {ml_ensemble_result.get('max_dd_pct', 'N/A')}%")
print(f"  买入持有最大回撤: {bh_result.get('max_dd_pct', 'N/A')}%")

# 📊 策略对比完成
print("\n" + "=" * 80)
print("📊 策略对比分析完成")
print("=" * 80)

# 只在终端显示对比结果，不生成图表
print("📈 最终对比结果总结:")
print(f"  集成模型策略最终资金: ${ml_ensemble_result['final_value']:,.2f}")
print(f"  买入持有策略最终资金: ${bh_result['final_value']:,.2f}")
print(f"  资金差额: ${ml_ensemble_result['final_value'] - bh_result['final_value']:,.2f}")

if ml_ensemble_result['final_value'] > bh_result['final_value']:
    print("  ✅ 集成模型策略表现优于买入持有策略")
else:
    print("  ⚠️ 集成模型策略表现不如买入持有策略")

print(f"\n📊 收益率对比:")
print(f"  集成模型策略收益率: {ml_total_return * 100:.2f}%")
print(f"  买入持有策略收益率: {bh_total_return * 100:.2f}%")
print(f"  超额收益: {excess_return * 100:.2f}%")

print("\n🎉 优化版本回测完成！")
print("✅ 新增功能总结:")
print("  1. 时间序列交叉验证 - Walk-Forward Analysis & Purged CV")
print("  2. 多维度评估指标 - IC, IR, 夏普比率, 最大回撤等")
print("  3. 浏览器可视化支持 - 与17w.py相同的plot_results方法")
print("  4. 实时性能监控 - 因子计算和预测时间统计")
print("  5. 策略对比分析 - 终端显示，无额外图表")
print("  6. 高效因子计算 - 25个因子完整一致性保证")

print("\n🔧 关键修复:")
print("  ✅ 采用与17w.py相同的简单有效的浏览器可视化方法")
print("  ✅ 修复了调用顺序：plot_results在cerebro.run()之前")
print("  ✅ 确保Chrome浏览器回测图能够正常显示")
print("  ✅ 简化策略对比，只在终端显示结果")
print("  ✅ 保持了与17w.py相同的核心功能，增加了评估特性")

'''
# ============================================================================
# 🚀 优化总结：高级评估版本特性
# ============================================================================

# 📋 新增评估特性：
# 1. **时间序列交叉验证**：
#    - Walk-Forward Analysis：模拟真实交易环境的滚动验证
#    - Purged Cross-Validation：避免数据泄漏的净化交叉验证
#    - 多折验证确保模型稳定性

# 2. **多维度评估指标**：
#    - 信息系数(IC)：衡量预测能力
#    - 信息比率(IR)：风险调整后的预测能力
#    - 夏普比率：经典风险调整收益指标
#    - 最大回撤：风险控制指标
#    - 卡尔马比率：回撤调整收益
#    - 索提诺比率：下行风险调整收益
#    - 胜率和盈亏比：交易质量指标
#    - IC衰减分析：预测持续性评估

# 3. **浏览器可视化**：
#    - backtrader_plotting支持
#    - 交互式图表，支持缩放和详细查看
#    - 在Chrome浏览器中自动打开

# 4. **实时性能监控**：
#    - 因子计算时间统计
#    - 模型预测时间监控
#    - 内存使用优化验证
#    - 每100次预测的性能报告

# 5. **策略对比分析**：
#    - 集成模型 vs 买入持有全面对比
#    - 收益曲线、回撤、滚动收益率可视化
#    - 多维度指标雷达图对比

# 📊 评估结果解读：
# - IC > 0.05：具有预测能力
# - IR > 0.5：较好的风险调整预测能力
# - 夏普比率 > 1.0：优秀的风险调整收益
# - 最大回撤 < 20%：可接受的风险水平
# - 胜率 > 50%：正向预测能力

# TSLA Alpha因子挖掘结果
'''

