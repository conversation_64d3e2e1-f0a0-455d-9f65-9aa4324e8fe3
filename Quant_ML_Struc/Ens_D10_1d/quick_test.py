#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试新版本的语法和基本导入
"""

import sys
import os

def test_syntax():
    """测试文件语法是否正确"""
    print("🧪 测试文件语法...")
    
    try:
        # 尝试编译文件
        with open('17w_2.2_26.6w_eva.py', 'r') as f:
            code = f.read()
        
        compile(code, '17w_2.2_26.6w_eva.py', 'exec')
        print("✅ 文件语法检查通过")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_key_imports():
    """测试关键依赖包"""
    print("\n🧪 测试关键依赖包...")
    
    packages = [
        'numpy',
        'pandas', 
        'sklearn',
        'matplotlib',
        'seaborn',
        'backtrader',
        'xgboost',
        'cvxpy',
        'backtrader_plotting'
    ]
    
    success_count = 0
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package}")
            success_count += 1
        except ImportError:
            print(f"❌ {package} - 未安装")
    
    print(f"\n📊 依赖包检查结果: {success_count}/{len(packages)} 成功")
    return success_count == len(packages)

def check_file_structure():
    """检查文件结构"""
    print("\n🧪 检查文件结构...")
    
    required_files = [
        '17w_2.2_26.6w_eva.py',
        '17w_2.0.py',
        'cache/TSLA_day.xlsx'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 文件不存在")

def count_new_features():
    """统计新增功能"""
    print("\n🧪 统计新增功能...")
    
    try:
        with open('17w_2.2_26.6w_eva.py', 'r') as f:
            content = f.read()
        
        features = {
            'TimeSeriesCrossValidator': 'TimeSeriesCrossValidator' in content,
            'AdvancedMetricsCalculator': 'AdvancedMetricsCalculator' in content,
            'EfficientFactorCalculator': 'EfficientFactorCalculator' in content,
            'walk_forward_split': 'walk_forward_split' in content,
            'purged_split': 'purged_split' in content,
            'information_coefficient': 'information_coefficient' in content,
            'sharpe_ratio': 'sharpe_ratio' in content,
            'max_drawdown': 'max_drawdown' in content,
            'backtrader_plotting': 'backtrader_plotting' in content,
            'browser_visualization': 'Bokeh' in content,
            'performance_monitoring': 'factor_calculation_times' in content,
            'comprehensive_evaluation': 'generate_comprehensive_evaluation_report' in content
        }
        
        print("📊 新增功能检查:")
        for feature, exists in features.items():
            status = "✅" if exists else "❌"
            print(f"  {status} {feature}")
        
        success_count = sum(features.values())
        print(f"\n📈 功能完整性: {success_count}/{len(features)} ({success_count/len(features)*100:.1f}%)")
        
        return success_count >= len(features) * 0.8  # 80%以上功能存在
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 快速测试优化评估版本")
    print("="*50)
    
    # 运行测试
    syntax_ok = test_syntax()
    imports_ok = test_key_imports()
    check_file_structure()
    features_ok = count_new_features()
    
    print("\n" + "="*50)
    print("📋 测试总结:")
    print(f"  语法检查: {'✅ 通过' if syntax_ok else '❌ 失败'}")
    print(f"  依赖包检查: {'✅ 通过' if imports_ok else '❌ 部分失败'}")
    print(f"  功能完整性: {'✅ 通过' if features_ok else '❌ 不完整'}")
    
    if syntax_ok and features_ok:
        print("\n🎉 新版本基本功能正常！")
        print("📝 新版本特性:")
        print("  ✅ 时间序列交叉验证 (Walk-Forward & Purged CV)")
        print("  ✅ 多维度评估指标 (IC, IR, 夏普比率等)")
        print("  ✅ 浏览器可视化支持 (backtrader_plotting)")
        print("  ✅ 高效因子计算器 (25个因子实时计算)")
        print("  ✅ 性能监控系统 (计算时间统计)")
        print("  ✅ 策略对比分析 (多维度对比)")
        print("\n🚀 可以运行完整版本:")
        print("   python3 17w_2.2_26.6w_eva.py")
    else:
        print("\n⚠️ 发现问题，请检查上述错误信息")
