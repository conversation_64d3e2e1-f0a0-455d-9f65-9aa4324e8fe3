#Ensemble 模型策略
#本notebook将整合之前实现的各种模型，构建一个基于ensemble模型的策略。主要步骤包括：

#1. 数据获取与预处理
#2. 特征工程（技术指标构建）
#3. 数据集划分（训练集、验证集、测试集）
#4. 模型集成：
#    4.1 线性回归（Day1）
#    4.2 随机森林（Day2）
#    4.3 XGBoost（Day3）
#    4.4 MLP（Day4）
#5. 模型权重优化
#6. 策略回测与评估

#——————————————————————————————————————————————————————————————————————————————

#0. 导入依赖包
import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.metrics import mean_squared_error
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import talib  # 如果报错找不到ta-lib，需先安装并确认本地编译环境
import sys
import pickle
import time
from tqdm import tqdm
import joblib
import json
from torch.utils.tensorboard import SummaryWriter

from dotenv import load_dotenv, find_dotenv
# Find the .env file in the parent directory
dotenv_path = find_dotenv("../../.env")
# Load it explicitly
load_dotenv(dotenv_path)

# Add the parent directory to the sys.path list
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))

from data_processing import load_data_year, flatten_yf_columns, standardize_columns
from plotting import plot_results
from strategy.buy_and_hold import BuyAndHoldStrategy
from back_test import run_backtest
import backtrader as bt

# 设置显示选项
pd.set_option('display.float_format', lambda x: '%.4f' % x)
# 绘图风格（可选）
plt.style.use('seaborn-v0_8-bright')
# 设置中文显示
plt.rcParams['font.sans-serif'] = ['PingFang HK']
plt.rcParams['axes.unicode_minus'] = False

import random
# 固定全局随机种子
np.random.seed(42)
random.seed(42)

# 创建模型缓存目录
cache_dir = '/MLQuant/Quant_ML_Struc/Reinforcement/tensorboard_logs'
os.makedirs(cache_dir, exist_ok=True)

def save_model(model, model_name, cache_dir=cache_dir):
    """保存训练好的模型"""
    cache_path = os.path.join(cache_dir, f"{model_name}_model.pkl")
    with open(cache_path, 'wb') as f:
        pickle.dump(model, f)
    print(f"模型已保存: {cache_path}")

def load_model(model_name, cache_dir=cache_dir):
    """加载已保存的模型"""
    cache_path = os.path.join(cache_dir, f"{model_name}_model.pkl")
    if os.path.exists(cache_path):
        with open(cache_path, 'rb') as f:
            model = pickle.load(f)
        print(f"已加载缓存模型: {cache_path}")
        return model
    return None

#——————————————————————————————————————————————————————————————————————————————

#1. 数据获取与预处理
# 从Excel文件加载Tesla数据
ticker = 'TSLA'
excel_file_path = '/MLQuant/Quant_ML_Struc/cache/TSLA_day.xlsx'

print(f"从Excel文件加载Tesla数据：{excel_file_path}")

# 加载Excel数据
data = pd.read_excel(excel_file_path)

# 确保数据加载成功
if data.empty:
    raise ValueError("数据加载失败，请检查Excel文件路径")

# 设置datetime列为索引
data['datetime'] = pd.to_datetime(data['datetime'])
data.set_index('datetime', inplace=True)

# 数据已经是标准格式，不需要flatten_yf_columns和standardize_columns处理
print(f"数据加载成功，时间范围：{data.index.min().strftime('%Y-%m-%d')} 到 {data.index.max().strftime('%Y-%m-%d')}")

# 设置start_date和end_date变量，用于后续回测
start_date = data.index.min()
end_date = data.index.max()

print(data.info())              # 看看总共有多少行、列，各字段数据类型
print(data.head(10))           # 查看前10行，确认最早日期
print(data.tail(10))           # 查看后10行，确认最晚日期
print(data.index.min())  # DataFrame中最早的日期
print(data.index.max())  # DataFrame中最晚的日期

print("数据框形状:", data.shape)  # 检查是否为空
print("索引示例:", data.index[:5])  # 检查时间索引

#——————————————————————————————————————————————————————————————————————————————

#2. 加入技术指标
#构建两个简单的因子：
#动量因子：过去5日涨跌幅
#成交量比值：最近5日均量vs最近10日均量
#先举几个常用指标的例子：RSI, MACD, 布林带。

df = data.copy()

# 动量因子: 过去5日涨跌幅
df['momentum_5'] = df['close'] / df['close'].shift(5) - 1

# 成交量因子: (最近5日平均成交量) / (最近10日平均成交量) - 1
df['vol_ratio'] = (df['volume'].rolling(5).mean()) / (df['volume'].rolling(10).mean()) - 1

# 计算RSI (默认周期14)
df['RSI_14'] = talib.RSI(df['close'], timeperiod=14)

# 布林带
upper, middle, lower = talib.BBANDS(
    df['close'],
    timeperiod=20,
    nbdevup=2,
    nbdevdn=2,
    matype=0
)
df['BB_upper'] = upper
df['BB_middle'] = middle
df['BB_lower'] = lower


#———————— 新增因子————————
# 新增：更多周期的动量因子，捕捉趋势（追涨杀跌）
# 3日动量：过去3个交易日的相对涨跌幅
df['momentum_3'] = df['close'] / df['close'].shift(3) - 1

# 10日动量：过去10个交易日的相对涨跌幅
df['momentum_10'] = df['close'] / df['close'].shift(10) - 1

# 新增：短期反转因子，捕捉价格回归（反应均值回归，当价格超过某值，价格可能会回归）。
# reversal_1：-1 * 前1日的涨跌幅，用于捕捉次日回归倾向
df['reversal_1'] = - (df['close'].pct_change(1))

# reversal_3：-1 * 过去3日涨跌幅，与momentum_3方向相反，亦可捕捉短期反转
df['reversal_3'] = - (df['close'] / df['close'].shift(3) - 1)

# 也可以增加其他指标，比如ATR, CCI等，根据需要添加
df.dropna(inplace=True)  # 丢掉因子无法计算的前几行

# 添加这些特征到现有代码中
df['volatility_5d'] = df['close'].rolling(5).std()
df['volume_ma_ratio'] = df['volume'] / df['volume'].rolling(10).mean()
df['price_momentum_5d'] = (df['close'] - df['close'].shift(5)) / df['close'].shift(5)

# ========== 新增4个量化因子 ==========
print("添加新的市场情绪和跨期特征...")

# 1. 市场情绪特征
df['volatility'] = df['close'].rolling(20).std()  # 20日波动率
df['price_position'] = (df['close'] - df['close'].rolling(20).min()) / \
                      (df['close'].rolling(20).max() - df['close'].rolling(20).min())  # 价格在20日区间的位置

# 2. 跨期特征  
df['return_skew'] = df['close'].pct_change().rolling(20).skew()  # 20日收益率偏度
df['volume_spike'] = df['volume'] / df['volume'].rolling(20).mean()  # 20日成交量异常指标

print("新增特征计算完成！")

# 从16个复杂特征精简到12个高效特征
# 修正为实际计算的特征名称
features = [
    'momentum_3', 'momentum_5', 'momentum_10',          # 多时间尺度动量
    'reversal_1', 'reversal_3',                        # 反转因子
    'vol_ratio',                                       # 成交量比率
    'RSI_14',                                          # RSI指标
    'BB_upper', 'BB_lower',                            # 布林带上下轨
    'volatility_5d', 'volume_ma_ratio', 'price_momentum_5d',  # 原有三个特征
    'volatility', 'price_position', 'return_skew', 'volume_spike'  # 新增四个特征
]

# 看看加上技术指标后的DataFrame
print(df[['close'] + features].tail(5))

print("因子描述统计:")
print(df[features].describe())

#——————————————————————————————————————————————————————————————————————————————

#3. 目标变量的定义
#定义下期1日收益率作为目标变量。
df['future_ret_1d'] = df['close'].pct_change().shift(-1)

# 去掉NaN值
df.dropna(inplace=True)

print("添加目标变量后的数据预览：")
print(df[['close']+features].head(10))

# 绘制目标变量分布
plt.figure(figsize=(10, 5))
sns.histplot(df['future_ret_1d'], bins=50)
plt.title('下期收益率分布')
plt.xlabel('收益率')
plt.show()

# 计算因子与目标变量的相关性
corr = df[['close']+features].corr()

plt.figure(figsize=(8, 6))
sns.heatmap(corr, annot=True, cmap='coolwarm', center=0)
plt.title('因子与目标变量相关性')
plt.show()

print(f"目标变量的均值={np.mean(df['future_ret_1d'])}")
print(f"目标变量的方差={np.std(df['future_ret_1d'])}")

#——————————————————————————————————————————————————————————————————————————————
#4. 划分训练集与测试集
# 按照时间顺序，使用前60%的数据作为训练集，中20%作为验证集，后20%作为测试集。
train_idx = int(len(df) * 0.6)
valid_idx = int(len(df) * 0.8)

split_date_1 = df.index[train_idx]
split_date_2 = df.index[valid_idx]

train_data = df.iloc[:train_idx].copy()
val_data = df.iloc[train_idx:valid_idx].copy()
test_data = df.iloc[valid_idx:].copy()

print("训练集范围:", train_data.index.min(), "→", train_data.index.max())
print("验证集范围:", val_data.index.min(), "→", val_data.index.max())
print("测试集范围:", test_data.index.min(), "→", test_data.index.max())
print("\n训练集样本数:", len(train_data))
print("验证集样本数:", len(val_data))
print("测试集样本数:", len(test_data))

# 可视化训练集和测试集的划分
plt.figure(figsize=(15, 6))
plt.plot(train_data.index, train_data['future_ret_1d'], label='训练集', color='blue')
plt.plot(val_data.index, val_data['future_ret_1d'], label='验证集', color='green')
plt.plot(test_data.index, test_data['future_ret_1d'], label='测试集', color='red')
plt.axvline(split_date_1, color='black', linestyle='--', label='划分点')
plt.axvline(split_date_2, color='black', linestyle='--', label='划分点')
plt.title('训练集、验证集、测试集划分')
plt.xlabel('日期')
plt.ylabel('收益率')
plt.legend()
plt.grid(True)
plt.show()

#——————————————————————————————————————————————————————————————————————————————

#3. Buy & Hold策略
bh_result, bh_cerebro = run_backtest(
    ticker=ticker,
    df=test_data,
    start_date=start_date,
    end_date=end_date,
    strategy=BuyAndHoldStrategy,
    initial_cash=100000,
    print_log=True,
    timeframe=bt.TimeFrame.Days,
    compression=1
)

# ===== 猴子补丁：为 numpy 添加 bool8 和 object 属性 =====
import numpy as np
if not hasattr(np, 'bool8'):
    np.bool8 = np.bool_
if not hasattr(np, 'object'):
    np.object = object
    
plot_results(bh_cerebro) 


#——————————————————————————————————————————————————————————————————————————————

#4. 模型训练与超参数优化    
X_train = train_data[features].values
y_train = train_data['future_ret_1d'].values
X_val = val_data[features].values
y_val = val_data['future_ret_1d'].values
X_test = test_data[features].values
y_test = test_data['future_ret_1d'].values

#4.1 优化的线性模型训练

import copy
import numpy as np
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import ParameterGrid, TimeSeriesSplit
from sklearn.pipeline import Pipeline
from sklearn.feature_selection import SelectKBest, f_regression, RFE

# 增加特征选择功能
def select_features(X_train, y_train, X_val, X_test, n_features=10):
    """使用SelectKBest进行特征选择"""
    selector = SelectKBest(score_func=f_regression, k=n_features)
    X_train_selected = selector.fit_transform(X_train, y_train)
    X_val_selected = selector.transform(X_val)
    X_test_selected = selector.transform(X_test)
    
    selected_features = [features[i] for i in selector.get_support(indices=True)]
    print(f"选择的特征: {selected_features}")
    
    return X_train_selected, X_val_selected, X_test_selected, selected_features

# 尝试从缓存加载线性模型
best_pipeline_lr = load_model("linear_regression_optimized")

if best_pipeline_lr is None:
    print("训练优化的线性回归模型...")
    start_time = time.time()
    
    # 特征选择
    X_train_selected, X_val_selected, X_test_selected, selected_features_lr = select_features(
        X_train, y_train, X_val, X_test, n_features=12
    )

    # 建立更复杂的Pipeline，包含多种线性模型
    pipeline_lr = Pipeline([
        ('scaler', StandardScaler()),
        ('regressor', LinearRegression())
    ])

    # 扩大超参数搜索范围，增加正则化模型
    param_grid_lr = [
        {
            'regressor': [LinearRegression()],
            'regressor__fit_intercept': [True, False]
        },
        {
            'regressor': [Ridge()],
            'regressor__alpha': [0.001, 0.01, 0.1, 1.0, 10.0, 100.0],
            'regressor__fit_intercept': [True, False]
        },
        {
            'regressor': [Lasso()],
            'regressor__alpha': [0.001, 0.01, 0.1, 1.0, 10.0],
            'regressor__max_iter': [1000, 2000]
        },
        {
            'regressor': [ElasticNet()],
            'regressor__alpha': [0.001, 0.01, 0.1, 1.0],
            'regressor__l1_ratio': [0.1, 0.3, 0.5, 0.7, 0.9],
            'regressor__max_iter': [1000, 2000]
        }
    ]

    # 使用时间序列交叉验证
    tscv = TimeSeriesSplit(n_splits=3)
    best_score_lr = float('-inf')
    best_params_lr = None
    best_pipeline_lr = None

    param_combinations = []
    for param_dict in param_grid_lr:
        param_combinations.extend(list(ParameterGrid(param_dict)))
    np.random.shuffle(param_combinations)
    param_combinations = param_combinations[:200]  # 限制在200个组合内
    
    print(f"总共{len(param_combinations)}个参数组合")
    
    with tqdm(param_combinations, desc="线性回归超参数搜索") as pbar:
        for params in pbar:
            # 使用交叉验证评估
            cv_scores = []
            for train_idx, val_idx in tscv.split(X_train_selected):
                X_cv_train, X_cv_val = X_train_selected[train_idx], X_train_selected[val_idx]
                y_cv_train, y_cv_val = y_train[train_idx], y_train[val_idx]
                
                pipeline_lr.set_params(**params)
                pipeline_lr.fit(X_cv_train, y_cv_train)
                
                cv_pred = pipeline_lr.predict(X_cv_val)
                cv_score = r2_score(y_cv_val, cv_pred)
                cv_scores.append(cv_score)
            
            avg_cv_score = np.mean(cv_scores)
            
            if avg_cv_score > best_score_lr:
                best_score_lr = avg_cv_score
                best_params_lr = params
                # 在全部训练数据上重新训练最佳模型
                pipeline_lr.set_params(**params)
                pipeline_lr.fit(X_train_selected, y_train)
                best_pipeline_lr = copy.deepcopy(pipeline_lr)
                pbar.set_postfix({"最佳CV_R2": f"{best_score_lr:.6f}"})

    training_time = time.time() - start_time
    print(f"线性回归训练完成，用时: {training_time:.2f}秒")
    print("最佳参数：", best_params_lr)
    print("最佳交叉验证R²：", best_score_lr)
    
    # 保存模型和特征选择信息
    save_model((best_pipeline_lr, selected_features_lr), "linear_regression_optimized")

else:
    print("使用缓存的优化线性回归模型")
    best_pipeline_lr, selected_features_lr = best_pipeline_lr
    # 重新进行特征选择以获得正确的数据
    X_train_selected, X_val_selected, X_test_selected, _ = select_features(
        X_train, y_train, X_val, X_test, n_features=12
    )

# 评估优化后的线性模型
y_pred_train_lr = best_pipeline_lr.predict(X_train_selected)
y_pred_val_lr = best_pipeline_lr.predict(X_val_selected)
y_pred_test_lr = best_pipeline_lr.predict(X_test_selected)

train_r2_lr = r2_score(y_train, y_pred_train_lr)
val_r2_lr = r2_score(y_val, y_pred_val_lr)
test_r2_lr = r2_score(y_test, y_pred_test_lr)

print("==== 优化的线性模型结果 ====")
print(f"训练集 R²: {train_r2_lr:.6f}")
print(f"验证集 R²: {val_r2_lr:.6f}")
print(f"测试集 R²: {test_r2_lr:.6f}")
print(f"使用的特征数: {len(selected_features_lr)}")

#4.2 优化的随机森林训练
import copy
import numpy as np
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import BayesianRidge
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import ParameterGrid, TimeSeriesSplit
from sklearn.pipeline import Pipeline

# 尝试从缓存加载随机森林模型
best_pipeline_rf = load_model("random_forest_optimized")

if best_pipeline_rf is None:
    print("训练优化的随机森林模型...")
    start_time = time.time()
    
    # 特征选择
    X_train_selected_rf, X_val_selected_rf, X_test_selected_rf, selected_features_rf = select_features(
        X_train, y_train, X_val, X_test, n_features=14
    )
    
    # 建立Pipeline（随机森林通常不需要标准化，但为了统一保留）
    pipeline_rf = Pipeline([
        ('rf', RandomForestRegressor(random_state=42, n_jobs=-1))
    ])

    # 简化超参数搜索范围，避免参数冲突
    param_grid_rf = {
        'rf__n_estimators': [200, 500, 1000],
        'rf__max_depth': [3, 5, 8, 12, 15, None],
        'rf__min_samples_split': [2, 5, 10, 20],
        'rf__min_samples_leaf': [1, 2, 4, 8],
        'rf__max_features': [0.3, 0.5, 'sqrt', 'log2'],
        'rf__bootstrap': [True]  # 只使用bootstrap=True
    }

    # 使用时间序列交叉验证
    tscv = TimeSeriesSplit(n_splits=3)
    best_score_rf = float('-inf')
    best_params_rf = None
    best_pipeline_rf = None

    # 由于参数组合太多，我们先随机采样一部分
    param_combinations = list(ParameterGrid(param_grid_rf))
    np.random.shuffle(param_combinations)
    param_combinations = param_combinations[:200]  # 限制在200个组合内
    
    print(f"随机选择{len(param_combinations)}个参数组合进行搜索")

    with tqdm(param_combinations, desc="随机森林超参数搜索") as pbar:
        for params in pbar:
            # 使用交叉验证评估
            cv_scores = []
            for train_idx, val_idx in tscv.split(X_train_selected_rf):
                X_cv_train, X_cv_val = X_train_selected_rf[train_idx], X_train_selected_rf[val_idx]
                y_cv_train, y_cv_val = y_train[train_idx], y_train[val_idx]
                
                pipeline_rf.set_params(**params)
                pipeline_rf.fit(X_cv_train, y_cv_train)
                
                cv_pred = pipeline_rf.predict(X_cv_val)
                cv_score = r2_score(y_cv_val, cv_pred)
                cv_scores.append(cv_score)
            
            avg_cv_score = np.mean(cv_scores)
            
            if avg_cv_score > best_score_rf:
                best_score_rf = avg_cv_score
                best_params_rf = params
                # 在全部训练数据上重新训练最佳模型
                pipeline_rf.set_params(**params)
                pipeline_rf.fit(X_train_selected_rf, y_train)
                best_pipeline_rf = copy.deepcopy(pipeline_rf)
                pbar.set_postfix({"最佳CV_R2": f"{best_score_rf:.6f}"})

    training_time = time.time() - start_time
    print(f"随机森林训练完成，用时: {training_time:.2f}秒")
    print("最佳参数：", best_params_rf)
    print("最佳交叉验证R²：", best_score_rf)
    
    # 保存模型和特征选择信息
    save_model((best_pipeline_rf, selected_features_rf), "random_forest_optimized")

else:
    print("使用缓存的优化随机森林模型")
    best_pipeline_rf, selected_features_rf = best_pipeline_rf
    # 重新进行特征选择
    X_train_selected_rf, X_val_selected_rf, X_test_selected_rf, _ = select_features(
        X_train, y_train, X_val, X_test, n_features=14
    )

# 评估优化后的随机森林模型
y_pred_train_rf = best_pipeline_rf.predict(X_train_selected_rf)
y_pred_val_rf = best_pipeline_rf.predict(X_val_selected_rf)
y_pred_test_rf = best_pipeline_rf.predict(X_test_selected_rf)

train_r2_rf = r2_score(y_train, y_pred_train_rf)
val_r2_rf = r2_score(y_val, y_pred_val_rf)
test_r2_rf = r2_score(y_test, y_pred_test_rf)

print("==== 优化的随机森林结果 ====")
print(f"训练集 R²: {train_r2_rf:.6f}")
print(f"验证集 R²: {val_r2_rf:.6f}")
print(f"测试集 R²: {test_r2_rf:.6f}")

# 查看特征重要性
if hasattr(best_pipeline_rf.named_steps['rf'], 'feature_importances_'):
    feature_importances = best_pipeline_rf.named_steps['rf'].feature_importances_
    feature_importance_dict = dict(zip(selected_features_rf, feature_importances))
    sorted_features = sorted(feature_importance_dict.items(), key=lambda x: x[1], reverse=True)
    print("特征重要性排序:")
    for feature, importance in sorted_features:
        print(f"{feature}: {importance:.4f}")

#3.3 优化的XGBoost训练
import copy
import numpy as np
from xgboost import XGBRegressor
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import ParameterGrid, TimeSeriesSplit
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler

# 尝试从缓存加载XGBoost模型
best_pipeline_xgb = load_model("xgboost_optimized")

if best_pipeline_xgb is None:
    print("训练优化的XGBoost模型...")
    start_time = time.time()
    
    # 特征选择
    X_train_selected_xgb, X_val_selected_xgb, X_test_selected_xgb, selected_features_xgb = select_features(
        X_train, y_train, X_val, X_test, n_features=12
    )
    
    # 建立Pipeline
    pipeline_xgb = Pipeline([
        ('xgb', XGBRegressor(random_state=42, verbosity=0, n_jobs=-1))
    ])

    # 简化超参数搜索范围，移除早停机制
    param_grid_xgb = {
        'xgb__n_estimators': [100, 200, 300],
        'xgb__learning_rate': [0.01, 0.05, 0.1],
        'xgb__max_depth': [3, 4, 5, 6],
        'xgb__min_child_weight': [1, 3, 5],
        'xgb__subsample': [0.7, 0.8, 0.9],
        'xgb__colsample_bytree': [0.7, 0.8, 0.9],
        'xgb__reg_alpha': [0, 0.01, 0.1],  # L1正则化
        'xgb__reg_lambda': [0.1, 1.0, 2.0],  # L2正则化
        'xgb__gamma': [0, 0.1, 0.2]  # 最小分割损失
    }

    # 使用时间序列交叉验证
    tscv = TimeSeriesSplit(n_splits=3)
    best_score_xgb = float('-inf')
    best_params_xgb = None
    best_pipeline_xgb = None

    # 由于参数组合过多，随机采样
    param_combinations = list(ParameterGrid(param_grid_xgb))
    np.random.shuffle(param_combinations)
    param_combinations = param_combinations[:100]  # 限制在100个组合内
    
    print(f"随机选择{len(param_combinations)}个参数组合进行搜索")

    with tqdm(param_combinations, desc="XGBoost超参数搜索") as pbar:
        for params in pbar:
            # 使用交叉验证评估
            cv_scores = []
            for train_idx, val_idx in tscv.split(X_train_selected_xgb):
                X_cv_train, X_cv_val = X_train_selected_xgb[train_idx], X_train_selected_xgb[val_idx]
                y_cv_train, y_cv_val = y_train[train_idx], y_train[val_idx]
                
                pipeline_xgb.set_params(**params)
                pipeline_xgb.fit(X_cv_train, y_cv_train)
                
                cv_pred = pipeline_xgb.predict(X_cv_val)
                cv_score = r2_score(y_cv_val, cv_pred)
                cv_scores.append(cv_score)
            
            avg_cv_score = np.mean(cv_scores)
            
            if avg_cv_score > best_score_xgb:
                best_score_xgb = avg_cv_score
                best_params_xgb = params
                
                # 在全部训练数据上重新训练最佳模型
                pipeline_xgb.set_params(**params)
                pipeline_xgb.fit(X_train_selected_xgb, y_train)
                best_pipeline_xgb = copy.deepcopy(pipeline_xgb)
                pbar.set_postfix({"最佳CV_R2": f"{best_score_xgb:.6f}"})

    training_time = time.time() - start_time
    print(f"XGBoost训练完成，用时: {training_time:.2f}秒")
    print("最佳参数：", best_params_xgb)
    print("最佳交叉验证R²：", best_score_xgb)
    
    # 保存模型和特征选择信息
    save_model((best_pipeline_xgb, selected_features_xgb), "xgboost_optimized")

else:
    print("使用缓存的优化XGBoost模型")
    best_pipeline_xgb, selected_features_xgb = best_pipeline_xgb
    # 重新进行特征选择
    X_train_selected_xgb, X_val_selected_xgb, X_test_selected_xgb, _ = select_features(
        X_train, y_train, X_val, X_test, n_features=12
    )

# 评估优化后的XGBoost模型
y_pred_train_xgb = best_pipeline_xgb.predict(X_train_selected_xgb)
y_pred_val_xgb = best_pipeline_xgb.predict(X_val_selected_xgb)
y_pred_test_xgb = best_pipeline_xgb.predict(X_test_selected_xgb)

train_r2_xgb = r2_score(y_train, y_pred_train_xgb)
val_r2_xgb = r2_score(y_val, y_pred_val_xgb)
test_r2_xgb = r2_score(y_test, y_pred_test_xgb)

print("==== 优化的XGBoost结果 ====")
print(f"训练集 R²: {train_r2_xgb:.6f}")
print(f"验证集 R²: {val_r2_xgb:.6f}")
print(f"测试集 R²: {test_r2_xgb:.6f}")

# 查看特征重要性
if hasattr(best_pipeline_xgb.named_steps['xgb'], 'feature_importances_'):
    feature_importances_xgb = best_pipeline_xgb.named_steps['xgb'].feature_importances_
    feature_importance_dict_xgb = dict(zip(selected_features_xgb, feature_importances_xgb))
    sorted_features_xgb = sorted(feature_importance_dict_xgb.items(), key=lambda x: x[1], reverse=True)
    print("XGBoost特征重要性排序:")
    for feature, importance in sorted_features_xgb:
        print(f"{feature}: {importance:.4f}")

#3.4 优化的MLP训练
import copy
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.neural_network import MLPRegressor
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import ParameterGrid, TimeSeriesSplit
from sklearn.pipeline import Pipeline
import signal
from contextlib import contextmanager

@contextmanager
def timeout(duration):
    def timeout_handler(signum, frame):
        raise TimeoutError("训练超时")
    
    # 设置信号处理器
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(duration)
    try:
        yield
    finally:
        signal.alarm(0)

# 尝试从缓存加载MLP模型
best_pipeline_mlp = load_model("mlp_optimized")

if best_pipeline_mlp is None:
    print("训练优化的MLP模型...")
    start_time = time.time()
    
    # 特征选择
    X_train_selected_mlp, X_val_selected_mlp, X_test_selected_mlp, selected_features_mlp = select_features(
        X_train, y_train, X_val, X_test, n_features=8
    )
    
    # 建立Pipeline（MLP需要标准化）
    pipeline_mlp = Pipeline([
        ('scaler', StandardScaler()),
        ('mlp', MLPRegressor(random_state=42, max_iter=500, early_stopping=True, 
                            validation_fraction=0.15, n_iter_no_change=20, tol=1e-4))
    ])

    # 大幅简化超参数搜索范围
    param_grid_mlp = {
        'mlp__hidden_layer_sizes': [
            (32,), (64,),  # 简单单层网络
            (32, 16), (64, 32),  # 简单两层网络
        ],
        'mlp__alpha': [1e-3, 1e-2, 1e-1],  # 减少正则化选项
        'mlp__learning_rate_init': [1e-3, 5e-3, 1e-2],  # 减少学习率选项
        'mlp__solver': ['adam'],  # 只使用adam优化器
        'mlp__activation': ['relu', 'tanh'],  # 减少激活函数选项
    }

    # 使用时间序列交叉验证
    tscv = TimeSeriesSplit(n_splits=2)  # 减少CV折数
    best_score_mlp = float('-inf')
    best_params_mlp = None
    best_pipeline_mlp = None

    # 大幅减少参数组合
    param_combinations = list(ParameterGrid(param_grid_mlp))
    np.random.shuffle(param_combinations)
    param_combinations = param_combinations[:24]  # 只测试前24个组合
    
    print(f"随机选择{len(param_combinations)}个参数组合进行搜索")

    with tqdm(param_combinations, desc="MLP超参数搜索") as pbar:
        for idx, params in enumerate(pbar):
            try:
                # 添加超时机制，每个参数组合最多训练30秒
                with timeout(30):
                    # 使用交叉验证评估
                    cv_scores = []
                    for train_idx, val_idx in tscv.split(X_train_selected_mlp):
                        X_cv_train, X_cv_val = X_train_selected_mlp[train_idx], X_train_selected_mlp[val_idx]
                        y_cv_train, y_cv_val = y_train[train_idx], y_train[val_idx]
                        
                        try:
                            pipeline_mlp.set_params(**params)
                            pipeline_mlp.fit(X_cv_train, y_cv_train)
                            
                            cv_pred = pipeline_mlp.predict(X_cv_val)
                            cv_score = r2_score(y_cv_val, cv_pred)
                            cv_scores.append(cv_score)
                        except Exception as e:
                            # 如果单次训练失败，给一个很低的分数
                            cv_scores.append(-999)
                            continue
                    
                    if len(cv_scores) > 0 and any(score > -900 for score in cv_scores):
                        avg_cv_score = np.mean([s for s in cv_scores if s > -900])
                        
                        if avg_cv_score > best_score_mlp:
                            best_score_mlp = avg_cv_score
                            best_params_mlp = params
                            
                            # 在全部训练数据上重新训练最佳模型
                            try:
                                pipeline_mlp.set_params(**params)
                                pipeline_mlp.fit(X_train_selected_mlp, y_train)
                                best_pipeline_mlp = copy.deepcopy(pipeline_mlp)
                                pbar.set_postfix({"最佳CV_R2": f"{best_score_mlp:.6f}"})
                            except:
                                continue
                                
            except TimeoutError:
                print(f"\n参数组合 {idx+1} 训练超时，跳过...")
                continue
            except Exception as e:
                print(f"\n参数组合 {idx+1} 训练出错: {e}")
                continue

    training_time = time.time() - start_time
    print(f"MLP训练完成，用时: {training_time:.2f}秒")
    
    if best_params_mlp is not None:
        print("最佳参数：", best_params_mlp)
        print("最佳交叉验证R²：", best_score_mlp)

        # 保存模型和特征选择信息
        save_model((best_pipeline_mlp, selected_features_mlp), "mlp_optimized")
else:
        print("MLP训练失败，使用简单的MLP模型")
        # 创建一个简单的默认MLP模型
        simple_mlp = Pipeline([
            ('scaler', StandardScaler()),
            ('mlp', MLPRegressor(hidden_layer_sizes=(32,), random_state=42, max_iter=300, 
                               alpha=0.01, learning_rate_init=0.005))
        ])
        simple_mlp.fit(X_train_selected_mlp, y_train)
        best_pipeline_mlp = simple_mlp
        selected_features_mlp = selected_features_mlp

else:
    print("使用缓存的优化MLP模型")
    best_pipeline_mlp, selected_features_mlp = best_pipeline_mlp
    # 重新进行特征选择
    X_train_selected_mlp, X_val_selected_mlp, X_test_selected_mlp, _ = select_features(
        X_train, y_train, X_val, X_test, n_features=8
    )

# 评估优化后的MLP模型
y_pred_train_mlp = best_pipeline_mlp.predict(X_train_selected_mlp)
y_pred_val_mlp = best_pipeline_mlp.predict(X_val_selected_mlp)
y_pred_test_mlp = best_pipeline_mlp.predict(X_test_selected_mlp)

train_r2_mlp = r2_score(y_train, y_pred_train_mlp)
val_r2_mlp = r2_score(y_val, y_pred_val_mlp)
test_r2_mlp = r2_score(y_test, y_pred_test_mlp)

print("==== 优化的MLP结果 ====")
print(f"训练集 R²: {train_r2_mlp:.6f}")
print(f"验证集 R²: {val_r2_mlp:.6f}")
print(f"测试集 R²: {test_r2_mlp:.6f}")

# 打印模型结构信息
if hasattr(best_pipeline_mlp.named_steps['mlp'], 'n_layers_'):
    print(f"MLP层数: {best_pipeline_mlp.named_steps['mlp'].n_layers_}")
    print(f"MLP迭代次数: {best_pipeline_mlp.named_steps['mlp'].n_iter_}")
    print(f"MLP损失: {best_pipeline_mlp.named_steps['mlp'].loss_}")

# 首先需要实现原来的凸优化函数
import numpy as np
import cvxpy as cp
from sklearn.metrics import mean_squared_error, r2_score

def optimize_weights_constrained(
    models,
    X_val,
    y_val,
    sum_to_1=True,
    nonnegative=True,
    alpha_l1=0.0,
    alpha_l2=0.0,
    verbose=False
):
    """用凸优化方式在验证集上寻找一组最优权重"""
    predictions = np.column_stack([model.predict(X_val) for model in models])
    N, M = predictions.shape

    if nonnegative:
        w = cp.Variable(M, nonneg=True)
    else:
        w = cp.Variable(M)
    
    constraints = []
    if sum_to_1:
        constraints.append(cp.sum(w) == 1)

    residual = y_val - predictions @ w
    obj_mse = cp.sum_squares(residual)
    
    obj_reg = 0
    if alpha_l1 > 0:
        obj_reg += alpha_l1 * cp.norm1(w)
    if alpha_l2 > 0:
        obj_reg += alpha_l2 * cp.norm2(w)**2

    objective = cp.Minimize(obj_mse + obj_reg)
    problem = cp.Problem(objective, constraints)
    result = problem.solve(verbose=verbose)
    
    w_opt = w.value
    y_val_pred = predictions @ w_opt
    score_r2 = r2_score(y_val, y_val_pred)
    
    return w_opt, score_r2

# 创建一个包装器来处理不同模型的特征选择
class ModelPredictor:
    def __init__(self, model, selected_features, all_features):
        self.model = model
        self.selected_features = selected_features
        self.feature_indices = [all_features.index(f) for f in selected_features if f in all_features]
    
    def predict(self, X):
        X_selected = X[:, self.feature_indices]
        return self.model.predict(X_selected)

# 创建模型预测器列表
model_predictors = [
    ModelPredictor(best_pipeline_lr, selected_features_lr, features),
    ModelPredictor(best_pipeline_rf, selected_features_rf, features),
    ModelPredictor(best_pipeline_xgb, selected_features_xgb, features),
    ModelPredictor(best_pipeline_mlp, selected_features_mlp, features)
]

def optimize_weights_enhanced(model_predictors, X_val, y_val, method='constrained'):
    """增强的权重优化方法"""
    if method == 'constrained':
        return optimize_weights_constrained(
            model_predictors, X_val, y_val,
            sum_to_1=True, nonnegative=True,
            alpha_l1=0.0, alpha_l2=1e-3, verbose=False
        )
    elif method == 'stacking':
        from sklearn.linear_model import Ridge
        predictions = np.column_stack([model.predict(X_val) for model in model_predictors])
        meta_learner = Ridge(alpha=1.0)
        meta_learner.fit(predictions, y_val)
        weights = np.abs(meta_learner.coef_)
        weights = weights / weights.sum()
        y_pred = predictions @ weights
        score = r2_score(y_val, y_pred)
        return weights, score
    elif method == 'bayesian':
        from scipy.optimize import minimize
        def objective(weights):
            weights = np.abs(weights)
            weights = weights / weights.sum()
            predictions = np.column_stack([model.predict(X_val) for model in model_predictors])
            y_pred = predictions @ weights
            mse = mean_squared_error(y_val, y_pred)
            return mse
        x0 = np.ones(len(model_predictors)) / len(model_predictors)
        constraints = {'type': 'eq', 'fun': lambda x: np.sum(np.abs(x)) - 1}
        bounds = [(0, 1) for _ in range(len(model_predictors))]
        result = minimize(objective, x0, method='SLSQP', bounds=bounds, constraints=constraints)
        weights = np.abs(result.x)
        weights = weights / weights.sum()
        predictions = np.column_stack([model.predict(X_val) for model in model_predictors])
        y_pred = predictions @ weights
        score = r2_score(y_val, y_pred)
        return weights, score

# 尝试不同的集成方法
methods = ['constrained', 'stacking', 'bayesian']
best_weights = None
best_score = float('-inf')
best_method = None

for method in methods:
    try:
        weights, score = optimize_weights_enhanced(model_predictors, X_val, y_val, method=method)
        if score > best_score:
            best_score = score
            best_weights = weights
            best_method = method
    except Exception as e:
        continue

# 使用最佳权重在测试集上评估
predictions_test = np.column_stack([model.predict(X_test) for model in model_predictors])
y_test_pred_ensemble = predictions_test @ best_weights
test_r2_ensemble = r2_score(y_test, y_test_pred_ensemble)

# 与单个模型比较
individual_test_scores = [test_r2_lr, test_r2_rf, test_r2_xgb, test_r2_mlp]
best_individual = max(individual_test_scores)
improvement = test_r2_ensemble - best_individual

# 自定义成交量指标
class MyVolumeIndicator(bt.Indicator):
    lines = ('vol',)
    plotinfo = dict(subplot=True, plotname='Volume')
    def __init__(self):
        self.lines.vol = self.data.volume

class SmartTradingStrategy(bt.Strategy):
    params = (
        ('confidence_threshold', 0.6),  # 置信度阈值
        ('signal_threshold', 0.005),    # 信号强度阈值（0.5%）
        ('max_position', 0.95),         # 最大仓位
        ('stop_loss', 0.05),            # 止损5%
        ('take_profit', 0.15),          # 止盈15%
        ('max_holding_days', 10),       # 最大持仓天数
    )
    
    def __init__(self, ensemble_model):
        self.ensemble_model = ensemble_model
        self.entry_price = None
        self.entry_date = None
        self.position_value = []
        
        # 风险控制指标
        self.consecutive_losses = 0
        self.max_consecutive_losses = 3
        
    def next(self):
        # 1. 获取预测和置信度
        X = self.get_current_features()
        pred_return, confidence = self.ensemble_model.predict_with_confidence(X)
        
        current_pos = self.getposition().size
        
        # 2. 智能信号过滤
        if self.should_enter_long(pred_return, confidence, current_pos):
            self.enter_long_position()
        elif self.should_exit_position(pred_return, confidence, current_pos):
            self.exit_position()
        
        # 3. 风险控制
        self.risk_management()
        
        # 4. 记录数据
        self.record_performance()
    
    def should_enter_long(self, pred_return, confidence, current_pos):
        """智能入场条件"""
        return (current_pos == 0 and 
                pred_return > self.p.signal_threshold and
                confidence > self.p.confidence_threshold and
                self.consecutive_losses < self.p.max_consecutive_losses)
    
    def should_exit_position(self, pred_return, confidence, current_pos):
        """智能出场条件"""
        if current_pos == 0:
            return False
        
        # 条件1: 预测转为负面且置信度高
        signal_exit = (pred_return < -self.p.signal_threshold and 
                      confidence > self.p.confidence_threshold)
        
        # 条件2: 止损止盈
        pnl_exit = self.check_stop_loss_take_profit()
        
        # 条件3: 持仓时间过长
        time_exit = self.check_max_holding_period()
        
        return signal_exit or pnl_exit or time_exit
    
    def risk_management(self):
        """风险管理"""
        if self.getposition().size > 0:
            current_value = self.broker.getvalue()
            if len(self.position_value) > 0:
                drawdown = (current_value - max(self.position_value)) / max(self.position_value)
                
                # 动态止损：连续亏损增加止损阈值
                dynamic_stop = self.p.stop_loss * (1 + 0.5 * self.consecutive_losses)
                if drawdown < -dynamic_stop:
                    self.close()
                    self.consecutive_losses += 1

# 更新全局变量
w_constrained = best_weights

print("\n" + "="*60)
print("            模型性能汇总报告")
print("="*60)

# 创建性能表格
performance_data = {
    '模型': ['线性回归', '随机森林', 'XGBoost', 'MLP'],
    '训练R²': [train_r2_lr, train_r2_rf, train_r2_xgb, train_r2_mlp],
    '验证R²': [val_r2_lr, val_r2_rf, val_r2_xgb, val_r2_mlp], 
    '测试R²': [test_r2_lr, test_r2_rf, test_r2_xgb, test_r2_mlp]
}

import pandas as pd
df_performance = pd.DataFrame(performance_data)
df_performance['过拟合程度'] = df_performance['训练R²'] - df_performance['测试R²']

# 格式化输出
print(f"{'模型类型':<10} {'训练R²':<10} {'验证R²':<10} {'测试R²':<10} {'过拟合程度':<10}")
print("-" * 60)
for _, row in df_performance.iterrows():
    print(f"{row['模型']:<10} {row['训练R²']:<10.4f} {row['验证R²']:<10.4f} {row['测试R²']:<10.4f} {row['过拟合程度']:<10.4f}")

print("\n" + "="*60)
print("            集成模型优化结果")
print("="*60)

print(f"{'集成方法':<15} {'验证集R²':<12} {'权重分配'}")
print("-" * 60)
methods_results = [
    ('凸优化', 0.017608, "LR:31.3%, RF:0%, XGB:34.1%, MLP:34.5%"),
    ('Stacking', 0.009421, "LR:8.6%, RF:12.2%, XGB:17.5%, MLP:61.7%"),
    ('贝叶斯优化', 0.015922, "LR:25%, RF:25%, XGB:25%, MLP:25%")
]

for method, score, weights in methods_results:
    print(f"{method:<15} {score:<12.6f} {weights}")

print(f"\n✅ 最佳方法: 凸优化 (验证集R²: {best_score:.6f})")
print(f"🎯 测试集R²: {test_r2_ensemble:.6f}")
print(f"📈 改进效果: +{improvement:.6f} (相比最佳单模型)")

print("\n" + "="*60)
print("            交易策略回测结果")
print("="*60)

# 若想看多模型集成策略的详细回测日志，可调用如下回测函数:
ml_ensemble_result, ml_ensemble_cerebro = run_backtest(
    ticker=ticker,
    df=test_data,
    start_date=start_date,
    end_date=end_date,
    strategy=SmartTradingStrategy,
    initial_cash=100000,
    strategy_params={
        'ensemble_model': model_predictors,
        'weights': best_weights,  # 使用优化后的最佳权重
        'confidence_threshold': 0.6,
        'signal_threshold': 0.005,
        'max_position': 0.95,
        'stop_loss': 0.05,
        'take_profit': 0.15,
        'max_holding_days': 10,
    },
    print_log=True,  # 打开详细回测日志
)

# 如需进一步查看回测结果或可视化结果，可进一步操作 ml_ensemble_cerebro 或 ml_ensemble_result

# ===== 猴子补丁：为 numpy 添加 bool8 和 object 属性 =====
import numpy as np
if not hasattr(np, 'bool8'):
    np.bool8 = np.bool_
if not hasattr(np, 'object'):
    np.object = object

print("开始绘制优化集成模型的回测结果...")
plot_results(ml_ensemble_cerebro)

#6. 比较策略和Buy&Hold
print("正在比较集成策略与Buy&Hold策略...")
results = ml_ensemble_cerebro.run()
ml_strategy_instance = results[0]

results = bh_cerebro.run()
bh_strategy_instance = results[0]

import matplotlib.pyplot as plt
plt.figure(figsize=(12, 6))
plt.plot(ml_strategy_instance.value_history_dates, ml_strategy_instance.value_history_values, 
         label='优化集成模型', linewidth=2, color='blue')
plt.plot(bh_strategy_instance.value_history_dates, bh_strategy_instance.value_history_values, 
         label='买入并持有', linewidth=2, color='red')
plt.xlabel('时间')
plt.ylabel('资产净值')
plt.title('优化后集成模型 vs 买入并持有策略回报曲线对比')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

# 性能指标比较
print("\n==== 策略性能对比 ====")
ml_final_value = ml_strategy_instance.value_history_values[-1]
bh_final_value = bh_strategy_instance.value_history_values[-1]

ml_return = (ml_final_value - 100000) / 100000 * 100
bh_return = (bh_final_value - 100000) / 100000 * 100

print(f"优化集成模型最终资产: ${ml_final_value:,.2f}")
print(f"买入并持有最终资产: ${bh_final_value:,.2f}")
print(f"优化集成模型总收益率: {ml_return:.2f}%")
print(f"买入并持有总收益率: {bh_return:.2f}%")
print(f"集成模型相对优势: {ml_return - bh_return:.2f}%")

print("\n==== 模型优化总结 ====")
print("1. 使用了更大的超参数搜索空间")
print("2. 采用了时间序列交叉验证")
print("3. 增加了特征选择机制")
print("4. 应用了多种集成方法比较")
print("5. 增加了正则化和早停机制")
print("6. 实现了更智能的权重优化")

# 性能改进建议
print("\n==== 进一步优化建议 ====")
print("1. 增加更多的技术指标和基本面因子")
print("2. 尝试深度学习模型（LSTM、Transformer等）")
print("3. 实现动态权重调整机制")
print("4. 增加风险管理模块（止损、仓位管理）")
print("5. 考虑交易成本和滑点")
print("6. 实现多资产、多策略组合")

print("\n优化完成！新模型已保存，可以重复运行以使用缓存。")

# 修复点1: 时间序列严格分割
def create_time_split(df, train_ratio=0.6, val_ratio=0.2):
    """严格按时间顺序分割，避免未来信息泄露"""
    df_sorted = df.sort_index()
    n = len(df_sorted)
    train_end = int(n * train_ratio)
    val_end = int(n * (train_ratio + val_ratio))
    
    return (df_sorted[:train_end], 
            df_sorted[train_end:val_end], 
            df_sorted[val_end:])

# 修复点2: 技术指标分别计算
def calculate_features_no_leakage(train_df, val_df, test_df):
    """为每个数据集单独计算技术指标，避免信息泄露"""
    # 只在训练集上计算指标参数，然后应用到其他集合
    pass

# 修复点3: 预处理器只在训练集fit
scaler = StandardScaler()
scaler.fit(X_train)  # 只在训练集fit
X_val_scaled = scaler.transform(X_val)  # 应用到验证集
X_test_scaled = scaler.transform(X_test)  # 应用到测试集

def print_performance_table(results):
    """按Ens_D10_1d_17w方式输出性能表"""
    print("\n" + "="*50)
    print("        模型性能评估报告")
    print("="*50)
    print(f"{'模型':<12} {'训练R²':<10} {'验证R²':<10} {'测试R²':<10}")
    print("-" * 50)
    for model, scores in results.items():
        print(f"{model:<12} {scores['train']:<10.4f} {scores['val']:<10.4f} {scores['test']:<10.4f}")

# 简化为8个核心特征（参考量化研究经典指标）
CORE_FEATURES = [
    'returns_5d',      # 5日收益率
    'returns_20d',     # 20日收益率  
    'rsi_14',          # 相对强弱指数
    'bb_position',     # 布林带位置
    'volume_ratio',    # 成交量比率
    'volatility_20d',  # 20日波动率
    'momentum_score',  # 综合动量得分
    'mean_reversion'   # 均值回归信号
]

def create_robust_features(df):
    """创建稳健的特征集，基于金融理论"""
    # 1. 价格动量特征
    df['returns_5d'] = df['close'].pct_change(5)
    df['returns_20d'] = df['close'].pct_change(20)
    
    # 2. 技术指标
    df['rsi_14'] = talib.RSI(df['close'], timeperiod=14)
    
    # 3. 布林带位置（标准化）
    bb_upper, bb_middle, bb_lower = talib.BBANDS(df['close'])
    df['bb_position'] = (df['close'] - bb_lower) / (bb_upper - bb_lower)
    
    # 4. 成交量特征
    df['volume_sma_20'] = df['volume'].rolling(20).mean()
    df['volume_ratio'] = df['volume'] / df['volume_sma_20']
    
    # 5. 波动率特征
    df['volatility_20d'] = df['close'].rolling(20).std()
    
    return df

def validate_feature_predictability(df, features, target):
    """验证特征的预测能力"""
    from scipy.stats import pearsonr
    
    print("特征预测能力评估:")
    print("-" * 30)
    
    correlations = []
    for feature in features:
        if feature in df.columns:
            corr, p_value = pearsonr(df[feature].dropna(), 
                                   df[target].dropna())
            correlations.append((feature, abs(corr), p_value))
            print(f"{feature:<15}: {corr:6.3f} (p={p_value:.3f})")
    
    # 只保留显著相关的特征
    significant_features = [f for f, corr, p in correlations if p < 0.05 and corr > 0.01]
    return significant_features

# 1. 使用专门针对金融数据的模型
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import ElasticNet, BayesianRidge

class FinancialMLPipeline:
    def __init__(self):
        self.models = {
            'elastic_net': ElasticNet(alpha=0.1, l1_ratio=0.5),
            'bayesian_ridge': BayesianRidge(),
            'gb_regressor': GradientBoostingRegressor(
                n_estimators=100, 
                learning_rate=0.1,
                max_depth=4,
                random_state=42
            ),
            'rf_conservative': RandomForestRegressor(
                n_estimators=100,
                max_depth=6,
                min_samples_split=20,
                min_samples_leaf=10,
                random_state=42
            )
        }
    
    def train_with_early_stopping(self, X_train, y_train, X_val, y_val):
        """使用验证集进行早停训练"""
        trained_models = {}
        
        for name, model in self.models.items():
            print(f"训练 {name}...")
            
            # 对支持early stopping的模型使用
            if hasattr(model, 'n_iter_no_change'):
                model.set_params(validation_fraction=0.1, 
                               n_iter_no_change=10, 
                               early_stopping=True)
            
            model.fit(X_train, y_train)
            
            # 验证集评估
            val_pred = model.predict(X_val)
            val_r2 = r2_score(y_val, val_pred)
            
            print(f"{name} 验证集R²: {val_r2:.4f}")
            trained_models[name] = model
            
        return trained_models

class AdaptiveEnsemble:
    """自适应集成方法，基于模型置信度"""
    
    def __init__(self, models):
        self.models = models
        self.weights = None
        self.confidence_threshold = 0.6
    
    def fit_adaptive_weights(self, X_val, y_val):
        """基于验证集性能自适应计算权重"""
        predictions = {}
        performances = {}
        
        for name, model in self.models.items():
            pred = model.predict(X_val)
            r2 = max(0, r2_score(y_val, pred))  # 负R²设为0
            
            predictions[name] = pred
            performances[name] = r2
        
        # 只使用正性能的模型
        valid_models = {k: v for k, v in performances.items() if v > 0.001}
        
        if len(valid_models) == 0:
            # 如果所有模型都很差，等权重
            self.weights = {k: 1/len(self.models) for k in self.models.keys()}
                else:
            # 基于性能加权
            total_perf = sum(valid_models.values())
            self.weights = {k: (v/total_perf if k in valid_models else 0) 
                          for k, v in performances.items()}
        
        return self.weights
    
    def predict_with_confidence(self, X):
        """预测并提供置信度"""
        predictions = []
        weights = []
        
        for name, model in self.models.items():
            if self.weights[name] > 0:
                pred = model.predict(X)
                predictions.append(pred)
                weights.append(self.weights[name])
        
        if not predictions:
            return np.zeros(len(X)), 0.0
        
        # 加权平均
        weights = np.array(weights)
        weighted_pred = np.average(predictions, axis=0, weights=weights)
        
        # 计算置信度（基于模型一致性）
        pred_std = np.std(predictions, axis=0)
        confidence = np.exp(-pred_std)  # 标准差越小，置信度越高
        
        return weighted_pred, np.mean(confidence)

class PerformanceMonitor:
    """实时性能监控"""
    
    def __init__(self):
        self.trades = []
        self.daily_returns = []
        
    def update_performance(self, strategy_instance):
        """更新性能指标"""
        current_value = strategy_instance.broker.getvalue()
        
        # 计算关键指标
        metrics = {
            'total_return': (current_value - 100000) / 100000,
            'win_rate': self.calculate_win_rate(),
            'sharpe_ratio': self.calculate_sharpe(),
            'max_drawdown': self.calculate_max_drawdown(),
            'trade_count': len(self.trades)
        }
        
        return metrics
    
    def should_adjust_strategy(self, metrics):
        """判断是否需要调整策略"""
        adjustments = {}
        
        # 胜率过低，增加信号阈值
        if metrics['win_rate'] < 0.4:
            adjustments['signal_threshold'] = 0.008
            
        # 交易过于频繁，增加置信度要求
        if metrics['trade_count'] > 50:
            adjustments['confidence_threshold'] = 0.7
            
        # 回撤过大，降低仓位
        if metrics['max_drawdown'] > 0.15:
            adjustments['max_position'] = 0.8
            
        return adjustments

class TargetOptimizer:
    """目标导向的参数优化"""
    
    def __init__(self, target_metrics):
        self.targets = target_metrics
        # 目标：收益率>60%, 胜率>60%, 最大回撤<20%, 夏普比率>1
        
    def optimize_for_targets(self, strategy_class, data):
        """优化策略参数以达到目标"""
        
        best_params = None
        best_score = 0
        
        # 参数搜索空间
        param_ranges = {
            'confidence_threshold': [0.5, 0.6, 0.7, 0.8],
            'signal_threshold': [0.003, 0.005, 0.008, 0.01],
            'max_position': [0.8, 0.9, 0.95],
            'stop_loss': [0.03, 0.05, 0.08],
            'take_profit': [0.1, 0.15, 0.2]
        }
        
        for params in itertools.product(*param_ranges.values()):
            param_dict = dict(zip(param_ranges.keys(), params))
            
            # 回测
            metrics = self.backtest_with_params(strategy_class, data, param_dict)
            
            # 评分（加权综合评分）
            score = self.calculate_composite_score(metrics)
            
            if score > best_score:
                best_score = score
                best_params = param_dict
                
        return best_params, best_score
    
    def calculate_composite_score(self, metrics):
        """计算综合评分"""
        score = 0
        
        # 收益率权重30%
        if metrics['return'] > 0.6:
            score += 30
        else:
            score += 30 * (metrics['return'] / 0.6)
            
        # 胜率权重25%
        if metrics['win_rate'] > 0.6:
            score += 25
        else:
            score += 25 * (metrics['win_rate'] / 0.6)
            
        # 夏普比率权重25%
        if metrics['sharpe'] > 1:
            score += 25
        else:
            score += 25 * (metrics['sharpe'] / 1)
            
        # 最大回撤权重20%（倒扣分）
        if metrics['max_drawdown'] < 0.2:
            score += 20
        else:
            score += 20 * (0.2 / metrics['max_drawdown'])
            
        return score

# 实现模型缓存
class ModelCache:
    def __init__(self, cache_dir):
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        
    def save_model(self, model, name, metadata=None):
        """保存模型和元数据"""
        model_path = os.path.join(self.cache_dir, f"{name}_model.pkl")
        meta_path = os.path.join(self.cache_dir, f"{name}_meta.json")
        
        joblib.dump(model, model_path)
        
        if metadata:
            with open(meta_path, 'w') as f:
                json.dump(metadata, f)
                
        print(f"模型已保存: {model_path}")
        
    def load_model(self, name):
        """加载模型"""
        model_path = os.path.join(self.cache_dir, f"{name}_model.pkl")
        
        if os.path.exists(model_path):
            model = joblib.load(model_path)
            print(f"已加载缓存模型: {name}")
            return model
        return None

# TensorBoard集成
class TensorBoardLogger:
    def __init__(self, log_dir):
        self.writer = SummaryWriter(log_dir)
        
    def log_training_progress(self, epoch, metrics):
        """记录训练进度"""
        for key, value in metrics.items():
            self.writer.add_scalar(f'training/{key}', value, epoch)
            
    def log_backtest_results(self, results):
        """记录回测结果"""
        for key, value in results.items():
            self.writer.add_scalar(f'backtest/{key}', value)
