#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化评估版本的基本功能
验证时间序列交叉验证和多维度评估指标是否正常工作
"""

import sys
import os
import numpy as np
import pandas as pd

# 添加路径
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))

def test_time_series_cv():
    """测试时间序列交叉验证功能"""
    print("🧪 测试时间序列交叉验证...")

    # 直接导入模块
    import importlib.util
    spec = importlib.util.spec_from_file_location("eva_module", "17w_2.2_26.6w_eva.py")
    eva_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(eva_module)

    TimeSeriesCrossValidator = eva_module.TimeSeriesCrossValidator
    
    # 创建模拟数据
    np.random.seed(42)
    n_samples = 100
    n_features = 5
    
    X = np.random.randn(n_samples, n_features)
    y = np.random.randn(n_samples)
    
    # 初始化交叉验证器
    ts_cv = TimeSeriesCrossValidator(n_splits=3, test_size=0.2, gap=2)
    
    # 测试Walk-Forward Analysis
    wf_splits = ts_cv.walk_forward_split(X, y)
    print(f"✅ Walk-Forward Analysis: 生成 {len(wf_splits)} 个分割")
    
    for i, (train_idx, test_idx) in enumerate(wf_splits):
        print(f"  分割 {i+1}: 训练集 {len(train_idx)} 样本, 测试集 {len(test_idx)} 样本")
    
    # 测试Purged Cross-Validation
    purged_splits = ts_cv.purged_split(X, y, embargo_pct=0.02)
    print(f"✅ Purged Cross-Validation: 生成 {len(purged_splits)} 个分割")
    
    for i, (train_idx, test_idx) in enumerate(purged_splits):
        print(f"  分割 {i+1}: 训练集 {len(train_idx)} 样本, 测试集 {len(test_idx)} 样本")

def test_advanced_metrics():
    """测试多维度评估指标"""
    print("\n🧪 测试多维度评估指标...")

    # 直接导入模块
    import importlib.util
    spec = importlib.util.spec_from_file_location("eva_module", "17w_2.2_26.6w_eva.py")
    eva_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(eva_module)

    AdvancedMetricsCalculator = eva_module.AdvancedMetricsCalculator
    
    # 创建模拟数据
    np.random.seed(42)
    n_samples = 100
    
    predictions = np.random.randn(n_samples) * 0.02  # 2%的预测收益率
    actual_returns = predictions + np.random.randn(n_samples) * 0.01  # 添加噪声
    
    metrics_calc = AdvancedMetricsCalculator()
    
    # 测试各种指标
    ic = metrics_calc.information_coefficient(predictions, actual_returns)
    print(f"✅ 信息系数(IC): {ic:.6f}")
    
    ir = metrics_calc.information_ratio(predictions, actual_returns)
    print(f"✅ 信息比率(IR): {ir:.6f}")
    
    sharpe = metrics_calc.sharpe_ratio(actual_returns)
    print(f"✅ 夏普比率: {sharpe:.4f}")
    
    max_dd = metrics_calc.max_drawdown(actual_returns)
    print(f"✅ 最大回撤: {max_dd:.4f}")
    
    calmar = metrics_calc.calmar_ratio(actual_returns)
    print(f"✅ 卡尔马比率: {calmar:.4f}")
    
    sortino = metrics_calc.sortino_ratio(actual_returns)
    print(f"✅ 索提诺比率: {sortino:.4f}")
    
    win_rate, profit_ratio = metrics_calc.win_rate_and_profit_ratio(predictions, actual_returns)
    print(f"✅ 胜率: {win_rate:.4f}, 盈亏比: {profit_ratio:.4f}")
    
    ic_decay = metrics_calc.ic_decay_analysis(predictions, actual_returns, max_lag=3)
    print(f"✅ IC衰减分析: {ic_decay}")

def test_efficient_factor_calculator():
    """测试高效因子计算器"""
    print("\n🧪 测试高效因子计算器...")

    # 直接导入模块
    import importlib.util
    spec = importlib.util.spec_from_file_location("eva_module", "17w_2.2_26.6w_eva.py")
    eva_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(eva_module)

    EfficientFactorCalculator = eva_module.EfficientFactorCalculator
    
    # 创建计算器
    calculator = EfficientFactorCalculator(max_window=50)
    
    # 添加模拟数据
    np.random.seed(42)
    base_price = 100
    
    for i in range(60):  # 添加60天数据
        # 模拟价格随机游走
        change = np.random.normal(0, 0.02)
        base_price *= (1 + change)
        
        # 生成OHLC
        high = base_price * (1 + abs(np.random.normal(0, 0.01)))
        low = base_price * (1 - abs(np.random.normal(0, 0.01)))
        open_price = base_price * (1 + np.random.normal(0, 0.005))
        close = base_price
        volume = np.random.randint(1000000, 10000000)
        
        calculator.add_data_point(open_price, high, low, close, volume)
    
    # 计算因子
    factors = calculator.calculate_all_factors()
    
    print(f"✅ 成功计算 {len(factors)} 个因子")
    print(f"✅ 前5个因子值: {[f'{f:.6f}' for f in factors[:5]]}")
    print(f"✅ 历史数据长度: {len(calculator.price_history)} (最大窗口: {calculator.max_window})")
    
    # 检查异常值
    nan_count = sum(1 for f in factors if np.isnan(f))
    inf_count = sum(1 for f in factors if np.isinf(f))
    
    if nan_count == 0 and inf_count == 0:
        print("✅ 所有因子计算正常，无异常值")
    else:
        print(f"⚠️ 发现异常值: {nan_count} 个NaN, {inf_count} 个Inf")

def test_imports():
    """测试关键模块导入"""
    print("\n🧪 测试关键模块导入...")
    
    try:
        import backtrader_plotting
        print("✅ backtrader_plotting 导入成功")
    except ImportError:
        print("⚠️ backtrader_plotting 导入失败")
    
    try:
        import cvxpy
        print("✅ cvxpy 导入成功")
    except ImportError:
        print("⚠️ cvxpy 导入失败")
    
    try:
        import xgboost
        print("✅ xgboost 导入成功")
    except ImportError:
        print("⚠️ xgboost 导入失败")

if __name__ == "__main__":
    print("🚀 开始测试优化评估版本...")
    print("="*60)
    
    # 运行各项测试
    test_imports()
    test_time_series_cv()
    test_advanced_metrics()
    test_efficient_factor_calculator()
    
    print("\n" + "="*60)
    print("🎉 测试完成！优化评估版本功能正常。")
    print("\n📋 新版本特性总结:")
    print("  ✅ 时间序列交叉验证 - Walk-Forward & Purged CV")
    print("  ✅ 多维度评估指标 - IC, IR, 夏普比率等")
    print("  ✅ 高效因子计算器 - 25个因子实时计算")
    print("  ✅ 浏览器可视化支持 - backtrader_plotting")
    print("  ✅ 性能监控系统 - 计算时间统计")
    print("\n🚀 可以运行完整版本: python 17w_2.2_26.6w_eva.py")
