# 深度分析：为什么两种标准化方法结果相似
import numpy as np 
import pandas as pd  
from sklearn.preprocessing import RobustScaler, StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

print("🔬 深度分析：标准化方法差异原因")
print("="*60)

# 1. 数据加载和预处理
excel_path = 'Quant_ML_Struc/cache/TSLA_day.xlsx'
data = pd.read_excel(excel_path)
data.columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']
data['datetime'] = pd.to_datetime(data['datetime'])
data.set_index('datetime', inplace=True)

df = data.copy()

# 计算因子
df['ma_5'] = df['close'].rolling(5).mean()
df['ma_20'] = df['close'].rolling(20).mean()
df['volatility'] = df['close'].pct_change().rolling(20).std()
df['volume_ratio'] = df['volume'] / df['volume'].rolling(20).mean()
df['price_momentum'] = df['close'] / df['close'].shift(10) - 1
df['price_position'] = (df['close'] - df['low'].rolling(20).min()) / (df['high'].rolling(20).max() - df['low'].rolling(20).min())

tr1 = df['high'] - df['low']
tr2 = abs(df['high'] - df['close'].shift(1))
tr3 = abs(df['low'] - df['close'].shift(1))
true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
df['atr'] = true_range.rolling(14).mean()

ma_20 = df['close'].rolling(20).mean()
std_20 = df['close'].rolling(20).std()
df['bollinger_position'] = (df['close'] - (ma_20 - 2*std_20)) / (4*std_20)

factors = ['ma_5', 'ma_20', 'volatility', 'volume_ratio', 'price_momentum', 
          'price_position', 'atr', 'bollinger_position']

df.dropna(inplace=True)
X = df[factors].values

print(f"分析样本数：{len(X)}")
print(f"特征数量：{len(factors)}")

# 2. 数据分布分析
print("\n📊 原始数据分布分析")
print("-" * 40)

for i, factor in enumerate(factors):
    data_col = X[:, i]
    print(f"\n{factor}:")
    print(f"  均值: {np.mean(data_col):.6f}")
    print(f"  中位数: {np.median(data_col):.6f}")
    print(f"  标准差: {np.std(data_col):.6f}")
    print(f"  四分位距: {np.percentile(data_col, 75) - np.percentile(data_col, 25):.6f}")
    print(f"  偏度: {stats.skew(data_col):.6f}")
    print(f"  峰度: {stats.kurtosis(data_col):.6f}")
    
    # 异常值检测
    Q1 = np.percentile(data_col, 25)
    Q3 = np.percentile(data_col, 75)
    IQR = Q3 - Q1
    outliers = np.sum((data_col < Q1 - 1.5*IQR) | (data_col > Q3 + 1.5*IQR))
    print(f"  异常值数量: {outliers} ({outliers/len(data_col)*100:.2f}%)")

# 3. 标准化效果对比
print("\n🔧 标准化效果对比")
print("-" * 40)

robust_scaler = RobustScaler()
standard_scaler = StandardScaler()

X_robust = robust_scaler.fit_transform(X)
X_standard = standard_scaler.fit_transform(X)

print("\n标准化后统计特征:")
for i, factor in enumerate(factors):
    robust_col = X_robust[:, i]
    standard_col = X_standard[:, i]
    
    print(f"\n{factor}:")
    print(f"  RobustScaler  - 均值: {np.mean(robust_col):.6f}, 标准差: {np.std(robust_col):.6f}")
    print(f"  StandardScaler- 均值: {np.mean(standard_col):.6f}, 标准差: {np.std(standard_col):.6f}")
    
    # 计算相关性
    correlation = np.corrcoef(robust_col, standard_col)[0, 1]
    print(f"  相关系数: {correlation:.8f}")
    
    # 计算差异
    max_diff = np.max(np.abs(robust_col - standard_col))
    mean_diff = np.mean(np.abs(robust_col - standard_col))
    print(f"  最大差异: {max_diff:.6f}, 平均差异: {mean_diff:.6f}")

# 4. 排序保持性分析
print("\n📈 排序保持性分析")
print("-" * 40)

for i, factor in enumerate(factors):
    original_ranks = stats.rankdata(X[:, i])
    robust_ranks = stats.rankdata(X_robust[:, i])
    standard_ranks = stats.rankdata(X_standard[:, i])
    
    # 计算排序相关性
    robust_rank_corr = stats.spearmanr(original_ranks, robust_ranks)[0]
    standard_rank_corr = stats.spearmanr(original_ranks, standard_ranks)[0]
    cross_rank_corr = stats.spearmanr(robust_ranks, standard_ranks)[0]
    
    print(f"{factor}:")
    print(f"  原始 vs RobustScaler排序相关性: {robust_rank_corr:.8f}")
    print(f"  原始 vs StandardScaler排序相关性: {standard_rank_corr:.8f}")
    print(f"  RobustScaler vs StandardScaler排序相关性: {cross_rank_corr:.8f}")

# 5. 异常值影响分析
print("\n⚠️ 异常值影响分析")
print("-" * 40)

# 人工添加异常值进行测试
X_with_outliers = X.copy()
# 在第一个特征中添加极端异常值
outlier_indices = np.random.choice(len(X), size=10, replace=False)
X_with_outliers[outlier_indices, 0] *= 100  # 放大100倍

robust_scaler_outlier = RobustScaler()
standard_scaler_outlier = StandardScaler()

X_robust_outlier = robust_scaler_outlier.fit_transform(X_with_outliers)
X_standard_outlier = standard_scaler_outlier.fit_transform(X_with_outliers)

print("添加异常值后的标准化效果:")
print(f"原始数据第一个特征:")
print(f"  正常数据均值: {np.mean(X[:, 0]):.6f}")
print(f"  含异常值均值: {np.mean(X_with_outliers[:, 0]):.6f}")

print(f"RobustScaler标准化后:")
print(f"  正常数据均值: {np.mean(X_robust[:, 0]):.6f}, 标准差: {np.std(X_robust[:, 0]):.6f}")
print(f"  含异常值均值: {np.mean(X_robust_outlier[:, 0]):.6f}, 标准差: {np.std(X_robust_outlier[:, 0]):.6f}")

print(f"StandardScaler标准化后:")
print(f"  正常数据均值: {np.mean(X_standard[:, 0]):.6f}, 标准差: {np.std(X_standard[:, 0]):.6f}")
print(f"  含异常值均值: {np.mean(X_standard_outlier[:, 0]):.6f}, 标准差: {np.std(X_standard_outlier[:, 0]):.6f}")

# 6. 模型敏感性分析
print("\n🎯 模型敏感性分析")
print("-" * 40)

from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.neural_network import MLPRegressor

# 使用简单的目标变量
df['target'] = df['close'].pct_change().shift(-1)
df_clean = df.dropna()
y = df_clean['target'].values

# 确保X和y长度一致
min_len = min(len(X), len(y))
X = X[:min_len]
X_robust = X_robust[:min_len]
X_standard = X_standard[:min_len]
y = y[:min_len]

# 分割数据
split_idx = int(len(X) * 0.8)
X_train, X_test = X[:split_idx], X[split_idx:]
X_train_robust, X_test_robust = X_robust[:split_idx], X_robust[split_idx:]
X_train_standard, X_test_standard = X_standard[:split_idx], X_standard[split_idx:]
y_train, y_test = y[:split_idx], y[split_idx:]

models = {
    'LinearRegression': LinearRegression(),
    'RandomForest': RandomForestRegressor(n_estimators=50, random_state=42),
    'MLP': MLPRegressor(hidden_layer_sizes=(32,), random_state=42, max_iter=200)
}

print("不同模型对标准化方法的敏感性:")
for model_name, model in models.items():
    print(f"\n{model_name}:")
    
    # 原始数据
    model_orig = model.__class__(**model.get_params())
    model_orig.fit(X_train, y_train)
    score_orig = model_orig.score(X_test, y_test)
    
    # RobustScaler
    model_robust = model.__class__(**model.get_params())
    model_robust.fit(X_train_robust, y_train)
    score_robust = model_robust.score(X_test_robust, y_test)
    
    # StandardScaler
    model_standard = model.__class__(**model.get_params())
    model_standard.fit(X_train_standard, y_train)
    score_standard = model_standard.score(X_test_standard, y_test)
    
    print(f"  原始数据: {score_orig:.6f}")
    print(f"  RobustScaler: {score_robust:.6f}")
    print(f"  StandardScaler: {score_standard:.6f}")
    print(f"  差异: {score_robust - score_standard:.6f}")

# 7. 结论
print("\n" + "="*60)
print("🎯 分析结论")
print("="*60)

print("\n1. 数据质量分析:")
print("   - TSLA数据相对干净，异常值较少")
print("   - 大部分特征分布相对正常")

print("\n2. 标准化效果:")
print("   - 两种方法产生的数据高度相关 (相关系数 > 0.99)")
print("   - 排序关系几乎完全保持")

print("\n3. 模型敏感性:")
print("   - 树模型对标准化不敏感（只关心排序）")
print("   - 神经网络对标准化敏感，但数据质量好时差异小")

print("\n4. 实际建议:")
print("   - 继续使用RobustScaler（更稳健）")
print("   - 加强异常值检测和处理")
print("   - 在更多样化的数据集上测试")

print("\n✅ 深度分析完成")
