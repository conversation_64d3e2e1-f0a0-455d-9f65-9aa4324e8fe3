#!/usr/bin/env python3
"""
快速测试不同标准化方法是否生成不同的缓存
只运行线性回归模型来验证缓存键是否不同
"""

import sys
import os
import hashlib
import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler, PowerTransformer, QuantileTransformer

# 添加路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def generate_cache_key(X_train, y_train, factors, model_type, params, scaler_type):
    """生成缓存键，包含标准化方法信息"""
    data_hash = hashlib.md5(str(X_train.shape).encode() + str(y_train.shape).encode()).hexdigest()[:8]
    factors_hash = hashlib.md5(str(sorted(factors)).encode()).hexdigest()[:8]
    params_hash = hashlib.md5(str(sorted(params.items())).encode()).hexdigest()[:8]
    scaler_hash = hashlib.md5(scaler_type.encode()).hexdigest()[:8]
    return f"{model_type}_{scaler_type}_{data_hash}_{factors_hash}_{params_hash}_{scaler_hash}.pkl"

def test_different_scalers():
    """测试不同标准化方法"""
    print("🧪 快速测试不同标准化方法的缓存键生成")
    print("="*60)
    
    # 模拟数据
    np.random.seed(42)
    X_train = np.random.randn(100, 5)
    y_train = np.random.randn(100)
    factors = ['factor1', 'factor2', 'factor3', 'factor4', 'factor5']
    model_type = "LinearRegression"
    params = {'lr__fit_intercept': [True, False]}
    
    # 测试不同标准化方法
    scalers = {
        'StandardScaler': StandardScaler(),
        'RobustScaler': RobustScaler(), 
        'MinMaxScaler': MinMaxScaler(),
        'PowerTransformer': PowerTransformer(method='yeo-johnson'),
        'QuantileTransformer': QuantileTransformer(output_distribution='uniform')
    }
    
    cache_keys = {}
    transformed_data = {}
    
    for scaler_name, scaler in scalers.items():
        # 生成缓存键
        cache_key = generate_cache_key(X_train, y_train, factors, model_type, params, scaler_name)
        cache_keys[scaler_name] = cache_key
        
        # 应用标准化
        X_scaled = scaler.fit_transform(X_train)
        transformed_data[scaler_name] = X_scaled
        
        print(f"{scaler_name:18}: {cache_key}")
    
    # 验证缓存键都不同
    unique_keys = set(cache_keys.values())
    print(f"\n🔍 验证结果:")
    print(f"   生成了 {len(unique_keys)} 个不同的缓存键（应该是5个）")
    
    if len(unique_keys) == 5:
        print("   ✅ 成功！每个标准化方法都有独立的缓存键")
    else:
        print("   ❌ 失败！缓存键有重复")
        return False
    
    # 验证数据确实不同
    print(f"\n📊 验证标准化后的数据:")
    base_data = transformed_data['StandardScaler']
    
    for scaler_name, data in transformed_data.items():
        if scaler_name == 'StandardScaler':
            continue
        
        # 计算与StandardScaler的差异
        diff = np.mean(np.abs(data - base_data))
        print(f"   {scaler_name:18} vs StandardScaler 平均差异: {diff:.6f}")
        
        if diff > 1e-10:  # 应该有明显差异
            print(f"   ✅ {scaler_name} 产生了不同的数据")
        else:
            print(f"   ⚠️ {scaler_name} 数据与StandardScaler相似")
    
    return True

def check_cache_directory():
    """检查缓存目录"""
    cache_dir = '/Users/<USER>/MLQuant/Quant_ML_Struc/cache/models'
    print(f"\n📁 检查缓存目录: {cache_dir}")
    
    if os.path.exists(cache_dir):
        files = [f for f in os.listdir(cache_dir) if f.endswith('.pkl')]
        print(f"   当前缓存文件数量: {len(files)}")
        
        if len(files) == 0:
            print("   ✅ 缓存目录已清空，准备重新训练")
        else:
            print("   📋 现有缓存文件:")
            for f in files[:10]:  # 只显示前10个
                print(f"      {f}")
            if len(files) > 10:
                print(f"      ... 还有 {len(files) - 10} 个文件")
    else:
        print("   ⚠️ 缓存目录不存在")

def main():
    print("🚀 标准化方法缓存修复验证")
    print("="*60)
    
    # 测试缓存键生成
    if test_different_scalers():
        print("\n🎉 测试通过！")
    else:
        print("\n💥 测试失败！")
        return 1
    
    # 检查缓存目录
    check_cache_directory()
    
    print(f"\n📋 总结:")
    print("="*60)
    print("✅ 问题已修复：不同标准化方法现在生成不同的缓存键")
    print("✅ 每个标准化方法将独立训练模型")
    print("✅ 可以正确比较不同标准化方法的效果")
    
    print(f"\n🔄 现在可以运行以下命令测试:")
    print("cd /Users/<USER>/MLQuant/Quant_ML_Struc/Ens_D10_1d")
    print("python3 17w_2.2_RobustScaler_26.6w.py")
    print("python3 17w_2.2_MinMaxScaler_20.1w.py")
    print("python3 17w_2.2_PowerTransformer_24.1w.py")
    print("python3 17w_2.2_QuantileTransformer_26.2w.py")
    print("python3 17w_2.2_NormilizedData.py")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
