#!/usr/bin/env python3
"""
测试缓存键修复是否成功
验证不同标准化方法是否生成不同的缓存键
"""

import hashlib
import sys
import os

# 模拟数据和参数
X_train_shape = (1000, 25)
y_train_shape = (1000,)
factors = ['factor1', 'factor2', 'factor3']
model_type = "MLP"
params = {'mlp__hidden_layer_sizes': [(64, 64)], 'mlp__alpha': [1e-3]}

def generate_cache_key_old(X_train_shape, y_train_shape, factors, model_type, params):
    """旧版缓存键生成函数（有问题的版本）"""
    data_hash = hashlib.md5(str(X_train_shape).encode() + str(y_train_shape).encode()).hexdigest()[:8]
    factors_hash = hashlib.md5(str(sorted(factors)).encode()).hexdigest()[:8]
    params_hash = hashlib.md5(str(sorted(params.items())).encode()).hexdigest()[:8]
    return f"{model_type}_{data_hash}_{factors_hash}_{params_hash}.pkl"

def generate_cache_key_new(X_train_shape, y_train_shape, factors, model_type, params, scaler_type):
    """新版缓存键生成函数（修复后的版本）"""
    data_hash = hashlib.md5(str(X_train_shape).encode() + str(y_train_shape).encode()).hexdigest()[:8]
    factors_hash = hashlib.md5(str(sorted(factors)).encode()).hexdigest()[:8]
    params_hash = hashlib.md5(str(sorted(params.items())).encode()).hexdigest()[:8]
    scaler_hash = hashlib.md5(scaler_type.encode()).hexdigest()[:8]
    return f"{model_type}_{scaler_type}_{data_hash}_{factors_hash}_{params_hash}_{scaler_hash}.pkl"

def test_cache_keys():
    """测试缓存键生成"""
    print("🔍 测试缓存键生成...")
    print("="*60)
    
    # 测试旧版本（有问题的）
    print("❌ 旧版本缓存键（所有标准化方法相同）:")
    old_key = generate_cache_key_old(X_train_shape, y_train_shape, factors, model_type, params)
    print(f"   所有方法: {old_key}")
    
    print("\n✅ 新版本缓存键（不同标准化方法不同）:")
    
    # 测试新版本（修复后的）
    scalers = ['StandardScaler', 'RobustScaler', 'MinMaxScaler', 'PowerTransformer', 'QuantileTransformer']
    new_keys = {}
    
    for scaler in scalers:
        key = generate_cache_key_new(X_train_shape, y_train_shape, factors, model_type, params, scaler)
        new_keys[scaler] = key
        print(f"   {scaler:18}: {key}")
    
    # 验证所有键都不同
    print(f"\n🔍 验证结果:")
    unique_keys = set(new_keys.values())
    if len(unique_keys) == len(scalers):
        print(f"   ✅ 成功！生成了 {len(unique_keys)} 个不同的缓存键")
        print(f"   ✅ 每个标准化方法都有独立的缓存")
    else:
        print(f"   ❌ 失败！只生成了 {len(unique_keys)} 个不同的缓存键")
        return False
    
    return True

def check_file_modifications():
    """检查文件修改情况"""
    print("\n📁 检查文件修改情况...")
    print("="*60)
    
    files_to_check = [
        '17w_2.2_NormilizedData.py',
        '17w_2.2_RobustScaler_26.6w.py',
        '17w_2.2_MinMaxScaler_20.1w.py',
        '17w_2.2_PowerTransformer_24.1w.py',
        '17w_2.2_QuantileTransformer_26.2w.py'
    ]
    
    base_dir = '/Users/<USER>/MLQuant/Quant_ML_Struc/Ens_D10_1d'
    
    for filename in files_to_check:
        filepath = os.path.join(base_dir, filename)
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否包含新的函数签名
            if 'def generate_cache_key(X_train, y_train, factors, model_type, params, scaler_type=' in content:
                print(f"   ✅ {filename}: 函数签名已更新")
            else:
                print(f"   ❌ {filename}: 函数签名未更新")
                
            # 检查是否包含scaler_type参数的调用
            scaler_calls = content.count('scaler_type=')
            if scaler_calls >= 4:  # 应该有4个模型的调用
                print(f"   ✅ {filename}: 缓存键调用已更新 ({scaler_calls}个)")
            else:
                print(f"   ❌ {filename}: 缓存键调用未完全更新 ({scaler_calls}个)")
        else:
            print(f"   ⚠️ {filename}: 文件不存在")

def main():
    print("🚀 缓存键修复验证测试")
    print("="*60)
    
    # 测试缓存键生成
    if test_cache_keys():
        print("\n🎉 缓存键生成测试通过！")
    else:
        print("\n💥 缓存键生成测试失败！")
        return 1
    
    # 检查文件修改
    check_file_modifications()
    
    print("\n📋 总结:")
    print("="*60)
    print("✅ 问题已修复：每个标准化方法现在都有独立的缓存键")
    print("✅ 不同标准化方法将重新训练模型，不再共享缓存")
    print("✅ 现在可以正确比较不同标准化方法的效果")
    
    print("\n🔄 下一步:")
    print("1. 清理旧缓存文件（已完成）")
    print("2. 重新运行5个模型文件")
    print("3. 观察不同标准化方法的性能差异")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
