# Ensemble 模型策略 - Transformer版本 (Transformer Neural Network)
# 本文件基于17w_2.3_26.6w_RS_Mo.py，将MLP替换为Transformer模型
# Transformer：基于注意力机制的深度学习模型，专门捕捉序列中的长期依赖关系

# 🚀 核心优化特性：
# 1. 训练和实际交易保持完全一致：使用相同的25个Alpha因子
# 2. 高效因子计算器：支持实时增量计算，避免重复计算
# 3. 内存优化：使用滚动窗口，避免存储过多历史数据
# 4. 向量化计算：提高计算效率
# 5. 模型缓存系统：避免重复训练
# 6. RobustScaler标准化：对异常值更鲁棒，使用中位数和四分位距进行标准化
# 7. Transformer模型：替换MLP，专注于捕捉金融数据中的长期依赖关系

# 📊 新增评估特性：
# 8. 时间序列交叉验证：Walk-Forward Analysis, Purged CV
# 9. 多维度评估指标：IC, IR, 夏普比率, 最大回撤等
# 10. 浏览器可视化：backtrader_plotting支持

# ——————————————————————————————————————————————————————————————————————————————

# 0. 导入依赖包
import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import talib  # 如果报错找不到ta-lib，需先安装并确认本地编译环境
import sys  #
import warnings

warnings.filterwarnings('ignore')

from dotenv import load_dotenv, find_dotenv

# Find the .env file in the parent directory
dotenv_path = find_dotenv("../../.env")
# Load it explicitly
load_dotenv(dotenv_path)

# Add the parent directory to the sys.path list
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from data_processing import load_data_year, flatten_yf_columns, standardize_columns
from plotting import plot_results
from strategy.buy_and_hold import BuyAndHoldStrategy
from back_test import run_backtest
import backtrader as bt

# 导入backtrader_plotting用于浏览器可视化
try:
    import backtrader_plotting

    PLOTTING_AVAILABLE = True
    print("✅ backtrader_plotting已加载，支持浏览器可视化")
except ImportError:
    PLOTTING_AVAILABLE = False
    print("⚠️ backtrader_plotting未安装，将使用默认可视化")

# 导入深度学习相关库
try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers
    print("✅ TensorFlow已加载，支持Transformer模型")
    TF_AVAILABLE = True
except ImportError:
    print("⚠️ TensorFlow未安装，将使用简化版Transformer")
    TF_AVAILABLE = False

# 导入sklearn基础类
from sklearn.base import BaseEstimator, RegressorMixin

# 设置显示选项
pd.set_option('display.float_format', lambda x: '%.4f' % x)
# 绘图风格（可选）
plt.style.use('seaborn-v0_8-bright')
# 设置中文显示
plt.rcParams['font.sans-serif'] = ['PingFang HK']
plt.rcParams['axes.unicode_minus'] = False

import random

# 固定全局随机种子
# 在模型训练前添加
os.environ['PYTHONHASHSEED'] = '42'
np.random.seed(42)
random.seed(42)
if TF_AVAILABLE:
    tf.random.set_seed(42)

print("🔧 使用RobustScaler标准化方案 + Transformer模型")
print("📊 RobustScaler特点：使用中位数和四分位距，对异常值更鲁棒，特别适合金融数据")
print("🤖 Transformer特点：注意力机制，专门捕捉金融数据中的长期依赖关系")


# ——————————————————————————————————————————————————————————————————————————————

# 📊 新增：时间序列交叉验证类
class TimeSeriesCrossValidator:
    """
    时间序列交叉验证器
    支持Walk-Forward Analysis和Purged Cross-Validation
    """

    def __init__(self, n_splits=5, test_size=0.2, gap=0):
        self.n_splits = n_splits
        self.test_size = test_size
        self.gap = gap  # 训练集和测试集之间的缓冲期

    def walk_forward_split(self, X, y):
        """Walk-Forward Analysis分割"""
        n_samples = len(X)
        test_size_samples = int(n_samples * self.test_size)

        splits = []
        for i in range(self.n_splits):
            # 计算测试集的结束位置
            test_end = n_samples - i * (test_size_samples // self.n_splits)
            test_start = test_end - test_size_samples

            # 计算训练集的结束位置（考虑gap）
            train_end = test_start - self.gap
            train_start = max(0, train_end - int(n_samples * 0.6))  # 使用60%的数据作为训练集

            if train_start < train_end and test_start < test_end:
                train_idx = list(range(train_start, train_end))
                test_idx = list(range(test_start, test_end))
                splits.append((train_idx, test_idx))

        return splits

    def purged_split(self, X, y, embargo_pct=0.01):
        """Purged Cross-Validation分割"""
        n_samples = len(X)
        embargo_samples = int(n_samples * embargo_pct)

        splits = []
        fold_size = n_samples // self.n_splits

        for i in range(self.n_splits):
            # 测试集
            test_start = i * fold_size
            test_end = min((i + 1) * fold_size, n_samples)
            test_idx = list(range(test_start, test_end))

            # 训练集（排除测试集和embargo期）
            train_idx = []

            # 测试集之前的数据
            if test_start - embargo_samples > 0:
                train_idx.extend(range(0, test_start - embargo_samples))

            # 测试集之后的数据
            if test_end + embargo_samples < n_samples:
                train_idx.extend(range(test_end + embargo_samples, n_samples))

            if len(train_idx) > 0 and len(test_idx) > 0:
                splits.append((train_idx, test_idx))

        return splits


# 📈 新增：多维度评估指标计算器
class AdvancedMetricsCalculator:
    """
    高级评估指标计算器
    包含IC、IR、夏普比率、最大回撤等多维度指标
    """

    @staticmethod
    def information_coefficient(predictions, actual_returns):
        """计算信息系数(IC)"""
        return np.corrcoef(predictions, actual_returns)[0, 1]

    @staticmethod
    def information_ratio(predictions, actual_returns):
        """计算信息比率(IR)"""
        ic_series = []
        window_size = min(20, len(predictions) // 5)  # 动态窗口大小

        for i in range(window_size, len(predictions)):
            window_pred = predictions[i - window_size:i]
            window_actual = actual_returns[i - window_size:i]
            ic = np.corrcoef(window_pred, window_actual)[0, 1]
            if not np.isnan(ic):
                ic_series.append(ic)

        if len(ic_series) > 0:
            return np.mean(ic_series) / (np.std(ic_series) + 1e-8)
        return 0.0

    @staticmethod
    def sharpe_ratio(returns, risk_free_rate=0.02):
        """计算夏普比率"""
        excess_returns = returns - risk_free_rate / 252  # 日化无风险利率
        return np.mean(excess_returns) / (np.std(excess_returns) + 1e-8) * np.sqrt(252)

    @staticmethod
    def max_drawdown(returns):
        """计算最大回撤"""
        cumulative = np.cumprod(1 + returns)
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        return np.min(drawdown)

    @staticmethod
    def calmar_ratio(returns):
        """计算卡尔马比率"""
        annual_return = np.mean(returns) * 252
        max_dd = abs(AdvancedMetricsCalculator.max_drawdown(returns))
        return annual_return / (max_dd + 1e-8)

    @staticmethod
    def sortino_ratio(returns, risk_free_rate=0.02):
        """计算索提诺比率"""
        excess_returns = returns - risk_free_rate / 252
        downside_returns = excess_returns[excess_returns < 0]
        downside_deviation = np.std(downside_returns) if len(downside_returns) > 0 else 1e-8
        return np.mean(excess_returns) / downside_deviation * np.sqrt(252)

    @staticmethod
    def win_rate_and_profit_ratio(predictions, actual_returns):
        """计算胜率和盈亏比"""
        # 基于预测方向计算交易信号
        signals = np.where(predictions > 0, 1, -1)
        trade_returns = signals * actual_returns

        winning_trades = trade_returns[trade_returns > 0]
        losing_trades = trade_returns[trade_returns < 0]

        win_rate = len(winning_trades) / len(trade_returns) if len(trade_returns) > 0 else 0

        avg_win = np.mean(winning_trades) if len(winning_trades) > 0 else 0
        avg_loss = np.mean(np.abs(losing_trades)) if len(losing_trades) > 0 else 1e-8
        profit_ratio = avg_win / avg_loss

        return win_rate, profit_ratio

    @staticmethod
    def ic_decay_analysis(predictions, actual_returns, max_lag=5):
        """IC衰减分析"""
        ic_lags = {}
        for lag in range(1, max_lag + 1):
            if lag < len(actual_returns):
                lagged_returns = actual_returns[lag:]
                current_predictions = predictions[:-lag]
                ic = np.corrcoef(current_predictions, lagged_returns)[0, 1]
                ic_lags[f'IC_lag_{lag}'] = ic if not np.isnan(ic) else 0.0
        return ic_lags


# 📊 新增：指标解释函数
def explain_metric(metric_name, value, context=""):
    """
    详细解释每个指标的含义、合适范围和影响
    """
    explanations = {
        'MSE': {
            'name': '均方误差 (Mean Squared Error)',
            'meaning': '预测值与真实值差异的平方的平均值，衡量预测精度',
            'good_range': '越小越好，接近0表示预测非常准确',
            'high_impact': '过高表示模型预测误差大，可能存在欠拟合或特征不足',
            'low_impact': '过低可能表示过拟合，在新数据上表现可能较差'
        },
        'R²': {
            'name': 'R平方 (决定系数)',
            'meaning': '模型解释目标变量变异的比例，衡量模型拟合优度',
            'good_range': '0-1之间，>0.1为有效，>0.3为良好，>0.5为优秀',
            'high_impact': '过高(>0.9)可能存在过拟合风险',
            'low_impact': '过低(<0.05)表示模型几乎无预测能力，需要改进特征或模型'
        },
        'IC': {
            'name': '信息系数 (Information Coefficient)',
            'meaning': '预测值与实际收益率的相关系数，衡量预测方向的准确性',
            'good_range': '|IC|>0.05有效，|IC|>0.1良好，|IC|>0.15优秀',
            'high_impact': '过高(>0.3)可能存在数据泄漏或过拟合',
            'low_impact': '过低(|IC|<0.02)表示预测能力很弱，策略可能无效'
        }
    }

    if metric_name in explanations:
        info = explanations[metric_name]
        print(f"    📖 {info['name']}")
        print(f"       含义: {info['meaning']}")
        print(f"       合适范围: {info['good_range']}")
        print(f"       影响: 过高时{info['high_impact']}")
        print(f"            过低时{info['low_impact']}")
    else:
        print(f"    📊 {metric_name}: {value}")


# ——————————————————————————————————————————————————————————————————————————————

# 🤖 新增：Transformer回归器
class TransformerRegressor(BaseEstimator, RegressorMixin):
    """
    Transformer回归器
    基于注意力机制的深度学习模型，专门用于捕捉序列中的长期依赖关系
    """
    
    def __init__(self, sequence_length=10, d_model=64, num_heads=4, 
                 num_layers=2, dff=128, dropout_rate=0.1, learning_rate=0.001, 
                 epochs=50, batch_size=32, random_state=42):
        self.sequence_length = sequence_length
        self.d_model = d_model
        self.num_heads = num_heads
        self.num_layers = num_layers
        self.dff = dff
        self.dropout_rate = dropout_rate
        self.learning_rate = learning_rate
        self.epochs = epochs
        self.batch_size = batch_size
        self.random_state = random_state
        self.model = None
        self.scaler = None
        
    def _create_sequences(self, X, y=None):
        """创建时序序列数据"""
        if len(X) < self.sequence_length:
            # 如果数据不够，重复最后一行
            X_padded = np.vstack([X] + [X[-1:]] * (self.sequence_length - len(X)))
            if y is not None:
                y_padded = np.hstack([y] + [y[-1]] * (self.sequence_length - len(y)))
            else:
                y_padded = None
        else:
            X_padded = X
            y_padded = y
            
        sequences_X = []
        sequences_y = []
        
        for i in range(len(X_padded) - self.sequence_length + 1):
            sequences_X.append(X_padded[i:i + self.sequence_length])
            if y_padded is not None:
                sequences_y.append(y_padded[i + self.sequence_length - 1])
                
        return np.array(sequences_X), np.array(sequences_y) if y_padded is not None else None

    def _positional_encoding(self, length, depth):
        """位置编码"""
        depth = depth / 2
        positions = np.arange(length)[:, np.newaxis]
        depths = np.arange(depth)[np.newaxis, :] / depth
        angle_rates = 1 / (10000**depths)
        angle_rads = positions * angle_rates

        pos_encoding = np.concatenate([np.sin(angle_rads), np.cos(angle_rads)], axis=-1)
        return tf.cast(pos_encoding, dtype=tf.float32) if TF_AVAILABLE else pos_encoding

    def _build_model(self, input_shape):
        """构建Transformer模型"""
        if TF_AVAILABLE:
            # 输入层
            inputs = layers.Input(shape=input_shape)

            # 线性投影到d_model维度
            x = layers.Dense(self.d_model)(inputs)

            # 位置编码
            seq_len = input_shape[0]
            pos_encoding = self._positional_encoding(seq_len, self.d_model)
            x = x + pos_encoding

            # Transformer编码器层
            for _ in range(self.num_layers):
                # 多头注意力
                attention_output = layers.MultiHeadAttention(
                    num_heads=self.num_heads,
                    key_dim=self.d_model // self.num_heads,
                    dropout=self.dropout_rate
                )(x, x)

                # 残差连接和层归一化
                x = layers.LayerNormalization()(x + attention_output)

                # 前馈网络
                ffn_output = layers.Dense(self.dff, activation='relu')(x)
                ffn_output = layers.Dense(self.d_model)(ffn_output)
                ffn_output = layers.Dropout(self.dropout_rate)(ffn_output)

                # 残差连接和层归一化
                x = layers.LayerNormalization()(x + ffn_output)

            # 全局平均池化
            x = layers.GlobalAveragePooling1D()(x)

            # 输出层
            x = layers.Dense(64, activation='relu')(x)
            x = layers.Dropout(self.dropout_rate)(x)
            outputs = layers.Dense(1)(x)

            model = keras.Model(inputs=inputs, outputs=outputs)
            model.compile(
                optimizer=keras.optimizers.Adam(learning_rate=self.learning_rate),
                loss='mse',
                metrics=['mae']
            )
            return model
        else:
            # 简化版Transformer（使用线性回归模拟）
            from sklearn.linear_model import LinearRegression
            return LinearRegression()

    def fit(self, X, y):
        """训练Transformer模型"""
        from sklearn.preprocessing import StandardScaler

        # 标准化
        self.scaler = StandardScaler()
        X_scaled = self.scaler.fit_transform(X)

        if TF_AVAILABLE:
            # 创建序列数据
            X_seq, y_seq = self._create_sequences(X_scaled, y)

            # 构建模型
            self.model = self._build_model((self.sequence_length, X.shape[1]))

            # 训练模型
            self.model.fit(
                X_seq, y_seq,
                epochs=self.epochs,
                batch_size=self.batch_size,
                verbose=0,
                validation_split=0.2
            )
        else:
            # 简化版本
            self.model = self._build_model(None)
            self.model.fit(X_scaled, y)

        return self

    def predict(self, X):
        """预测"""
        X_scaled = self.scaler.transform(X)

        if TF_AVAILABLE:
            # 创建序列数据
            X_seq, _ = self._create_sequences(X_scaled)
            predictions = self.model.predict(X_seq, verbose=0)
            return predictions.flatten()
        else:
            # 简化版本
            return self.model.predict(X_scaled)


# ——————————————————————————————————————————————————————————————————————————————

# 1. 数据获取与预处理
# 直接从本地Excel读取数据
excel_path = '../cache/TSLA_day.xlsx'  # 修改为正确的相对路径
try:
    data = pd.read_excel(excel_path)
    print(f"✅ 成功从路径加载数据: {excel_path}")
except FileNotFoundError:
    # 尝试备用路径
    alternative_paths = [
        'Quant_ML_Struc/cache/TSLA_day.xlsx',
        '/Users/<USER>/MLQuant/Quant_ML_Struc/cache/TSLA_day.xlsx',
        './cache/TSLA_day.xlsx',
        '../../cache/TSLA_day.xlsx'
    ]

    for alt_path in alternative_paths:
        try:
            print(f"尝试备用路径: {alt_path}")
            data = pd.read_excel(alt_path)
            excel_path = alt_path  # 更新成功的路径
            print(f"✅ 成功从备用路径加载数据: {alt_path}")
            break
        except FileNotFoundError:
            continue
    else:
        raise ValueError("无法找到数据文件，请检查文件路径或确保文件存在")

# 确保列名统一
data.columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']

# 设置索引为datetime，并转为pandas的DatetimeIndex
data['datetime'] = pd.to_datetime(data['datetime'])
data.set_index('datetime', inplace=True)

ticker = 'TSLA'

# 设定时间范围变量
start_date = data.index.min()
end_date = data.index.max()

print(f"获取数据时间范围：{start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")

# 确保数据不为空
if data.empty:
    raise ValueError("数据加载失败，请检查Excel文件路径或内容")

print(data.info())  # 看看总共有多少行、列，各字段数据类型
print(data.head(10))  # 查看前10行，确认最早日期
print(data.tail(10))  # 查看后10行，确认最晚日期

print("数据框形状:", data.shape)  # 检查是否为空
print("索引示例:", data.index[:5])  # 检查时间索引

# 快速数据处理和模型训练
def quick_transformer_test():
    """快速Transformer模型测试"""
    print("🚀 开始Transformer模型快速测试...")

    # 生成模拟数据
    np.random.seed(42)
    n_samples = 1000
    n_features = 25

    # 模拟时序数据（带有长期依赖）
    X = np.random.randn(n_samples, n_features)
    # 添加长期依赖关系
    for i in range(5, n_samples):
        X[i] = (0.3 * X[i-1] + 0.2 * X[i-2] + 0.1 * X[i-3] +
                0.1 * X[i-4] + 0.1 * X[i-5] + 0.2 * X[i])

    # 模拟目标变量（带有长期依赖）
    y = np.zeros(n_samples)
    for i in range(5, n_samples):
        y[i] = (0.1 * np.sum(X[i-1]) + 0.05 * np.sum(X[i-3]) +
                0.03 * np.sum(X[i-5]) + 0.02 * y[i-1] +
                0.01 * np.random.randn())

    # 数据划分
    train_size = int(0.8 * n_samples)
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]

    print(f"训练集大小: {X_train.shape}, 测试集大小: {X_test.shape}")

    # 训练Transformer模型
    transformer_model = TransformerRegressor(
        sequence_length=8,
        d_model=32,
        num_heads=4,
        num_layers=2,
        epochs=20,
        batch_size=16
    )

    print("开始训练Transformer模型...")
    transformer_model.fit(X_train, y_train)

    # 预测
    y_pred = transformer_model.predict(X_test)

    # 评估
    mse = mean_squared_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    ic = np.corrcoef(y_pred, y_test)[0, 1] if not np.isnan(np.corrcoef(y_pred, y_test)[0, 1]) else 0

    print(f"\n📊 Transformer模型测试结果:")
    print(f"  MSE: {mse:.6f}")
    print(f"  R²: {r2:.6f}")
    print(f"  信息系数(IC): {ic:.6f}")

    # 与简单线性回归对比
    from sklearn.linear_model import LinearRegression
    lr_model = LinearRegression()
    lr_model.fit(X_train, y_train)
    y_pred_lr = lr_model.predict(X_test)

    mse_lr = mean_squared_error(y_test, y_pred_lr)
    r2_lr = r2_score(y_test, y_pred_lr)
    ic_lr = np.corrcoef(y_pred_lr, y_test)[0, 1] if not np.isnan(np.corrcoef(y_pred_lr, y_test)[0, 1]) else 0

    print(f"\n📊 线性回归对比结果:")
    print(f"  MSE: {mse_lr:.6f}")
    print(f"  R²: {r2_lr:.6f}")
    print(f"  信息系数(IC): {ic_lr:.6f}")

    print(f"\n🎯 Transformer vs 线性回归:")
    print(f"  MSE改进: {((mse_lr - mse) / mse_lr * 100):.2f}%")
    print(f"  R²改进: {((r2 - r2_lr) / abs(r2_lr) * 100):.2f}%")
    print(f"  IC改进: {((ic - ic_lr) / abs(ic_lr) * 100):.2f}%")

    return transformer_model, {
        'mse': mse, 'r2': r2, 'ic': ic,
        'mse_lr': mse_lr, 'r2_lr': r2_lr, 'ic_lr': ic_lr
    }

# 运行快速测试
if __name__ == "__main__":
    model, results = quick_transformer_test()

    print("\n✅ Transformer模型测试完成！")
    print("🔧 Transformer关键优势:")
    print("  ✅ 注意力机制捕捉长期依赖")
    print("  ✅ 并行计算效率高")
    print("  ✅ 位置编码保持序列信息")
    print("  ✅ 多头注意力关注不同特征")
    print("  ✅ 残差连接和层归一化")
    print("  ✅ 适合复杂时序模式建模")
