#!/usr/bin/env python3
"""
测试改进功能的脚本
验证警告处理、过拟合检测和回测验证功能
"""

import numpy as np
import pandas as pd
import warnings
from sklearn.linear_model import Ridge
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import train_test_split

# 设置警告过滤 - 只显示重要警告
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=DeprecationWarning)
warnings.filterwarnings('ignore', category=UserWarning, module='sklearn')
warnings.filterwarnings('ignore', message='.*feature names.*')

# ===== 改进的numpy兼容性补丁：避免deprecation warnings =====
def safe_numpy_patch():
    """安全地为numpy添加向后兼容的属性，避免警告"""
    try:
        # 只在属性不存在时添加，避免重复警告
        if not hasattr(np, 'bool8'):
            with warnings.catch_warnings():
                warnings.simplefilter("ignore", DeprecationWarning)
                np.bool8 = np.bool_
        
        if not hasattr(np, 'object'):
            with warnings.catch_warnings():
                warnings.simplefilter("ignore", FutureWarning)
                np.object = object
    except Exception as e:
        print(f"注意: numpy兼容性补丁应用失败: {e}")

# 应用安全补丁
safe_numpy_patch()

# 过拟合检测函数
def detect_overfitting_and_validate(models_dict, X_train, y_train, X_val, y_val, X_test, y_test, model_names):
    """检测过拟合并验证模型性能"""
    print("\n" + "="*80)
    print("                    过拟合检测与模型验证报告")
    print("="*80)
    
    validation_results = {}
    
    for model_name in model_names:
        if model_name not in models_dict:
            continue
            
        model = models_dict[model_name]
        
        # 计算各数据集上的性能
        train_pred = model.predict(X_train)
        val_pred = model.predict(X_val)
        test_pred = model.predict(X_test)
        
        train_r2 = r2_score(y_train, train_pred)
        val_r2 = r2_score(y_val, val_pred)
        test_r2 = r2_score(y_test, test_pred)
        
        # 过拟合检测指标
        overfitting_score = train_r2 - test_r2
        generalization_gap = train_r2 - val_r2
        
        # 判断过拟合程度
        if overfitting_score > 0.1:
            overfitting_level = "严重过拟合"
        elif overfitting_score > 0.05:
            overfitting_level = "轻微过拟合"
        elif overfitting_score > 0:
            overfitting_level = "正常"
        else:
            overfitting_level = "可能欠拟合"
        
        # 检测负R²
        negative_r2_warning = ""
        if test_r2 < 0:
            negative_r2_warning = " ⚠️ 测试集R²为负，表明严重过拟合！"
        if val_r2 < 0:
            negative_r2_warning += " ⚠️ 验证集R²为负，模型预测能力极差！"
        
        validation_results[model_name] = {
            'train_r2': train_r2,
            'val_r2': val_r2,
            'test_r2': test_r2,
            'overfitting_score': overfitting_score,
            'generalization_gap': generalization_gap,
            'overfitting_level': overfitting_level,
            'negative_r2_warning': negative_r2_warning
        }
        
        print(f"\n📊 {model_name} 详细分析:")
        print(f"   训练集 R²: {train_r2:8.4f}")
        print(f"   验证集 R²: {val_r2:8.4f}")
        print(f"   测试集 R²: {test_r2:8.4f}")
        print(f"   过拟合评分: {overfitting_score:6.4f} ({overfitting_level})")
        print(f"   泛化差距:   {generalization_gap:6.4f}")
        if negative_r2_warning:
            print(f"   {negative_r2_warning}")
    
    return validation_results

# 数据泄露检测函数
def check_data_leakage(df, target_col, feature_cols, split_dates):
    """检测潜在的数据泄露问题"""
    print(f"\n" + "="*80)
    print("                    数据泄露检测报告")
    print("="*80)
    
    leakage_report = {
        'time_leakage': [],
        'feature_leakage': [],
        'target_leakage': [],
        'recommendations': []
    }
    
    # 1. 时间序列完整性检查
    print(f"📅 时间序列完整性检查:")
    time_gaps = df.index.to_series().diff()
    large_gaps = time_gaps[time_gaps > pd.Timedelta(days=7)]
    
    if len(large_gaps) > 0:
        warning = f"发现 {len(large_gaps)} 个超过7天的时间断层"
        leakage_report['time_leakage'].append(warning)
        print(f"   ⚠️ {warning}")
    else:
        print(f"   ✅ 时间序列连续，无明显断层")
    
    # 2. 特征前瞻性检查
    print(f"\n🔍 特征前瞻性检查:")
    future_looking_features = []
    
    for feature in feature_cols:
        if 'future' in feature.lower() or 'next' in feature.lower():
            future_looking_features.append(feature)
    
    if future_looking_features:
        warning = f"发现可能的前瞻性特征: {future_looking_features}"
        leakage_report['feature_leakage'].append(warning)
        print(f"   ⚠️ {warning}")
    else:
        print(f"   ✅ 未发现明显的前瞻性特征")
    
    # 3. 目标变量泄露检查
    print(f"\n🎯 目标变量泄露检查:")
    if target_col in feature_cols:
        error = "目标变量被包含在特征中！"
        leakage_report['target_leakage'].append(error)
        print(f"   🚨 {error}")
    
    if 'shift' in target_col or 'future' in target_col:
        print(f"   ✅ 目标变量 '{target_col}' 使用了时间偏移")
    else:
        warning = "目标变量可能没有使用时间偏移"
        leakage_report['target_leakage'].append(warning)
        print(f"   ⚠️ {warning}")
    
    return leakage_report

# 测试函数
def test_improvements():
    """测试所有改进功能"""
    print("🚀 开始测试改进功能...")
    
    # 生成模拟数据
    np.random.seed(42)
    n_samples = 1000
    n_features = 5
    
    # 创建时间索引
    dates = pd.date_range('2020-01-01', periods=n_samples, freq='D')
    
    # 生成特征数据
    X = np.random.randn(n_samples, n_features)
    noise = np.random.randn(n_samples) * 0.1
    y = X[:, 0] * 0.5 + X[:, 1] * 0.3 + noise  # 简单的线性关系
    
    # 创建DataFrame
    feature_names = ['feature_1', 'feature_2', 'feature_3', 'feature_4', 'feature_5']
    df = pd.DataFrame(X, columns=feature_names, index=dates)
    df['future_ret_1d'] = y
    
    print(f"✅ 生成了 {n_samples} 个样本的模拟数据")
    
    # 数据集划分
    train_size = int(0.6 * n_samples)
    val_size = int(0.2 * n_samples)
    
    X_train = X[:train_size]
    X_val = X[train_size:train_size+val_size]
    X_test = X[train_size+val_size:]
    
    y_train = y[:train_size]
    y_val = y[train_size:train_size+val_size]
    y_test = y[train_size+val_size:]
    
    split_dates = [dates[train_size], dates[train_size+val_size]]
    
    # 训练模型
    models = {}
    
    # Ridge回归
    ridge = Pipeline([
        ('scaler', StandardScaler()),
        ('ridge', Ridge(alpha=1.0, random_state=42))
    ])
    ridge.fit(X_train, y_train)
    models['Ridge'] = ridge
    
    # 随机森林
    rf = RandomForestRegressor(n_estimators=50, max_depth=5, random_state=42)
    rf.fit(X_train, y_train)
    models['RandomForest'] = rf
    
    print(f"✅ 训练了 {len(models)} 个模型")
    
    # 测试过拟合检测
    print("\n" + "🔍" * 50)
    print("测试过拟合检测功能...")
    print("🔍" * 50)
    
    validation_results = detect_overfitting_and_validate(
        models_dict=models,
        X_train=X_train, y_train=y_train,
        X_val=X_val, y_val=y_val,
        X_test=X_test, y_test=y_test,
        model_names=['Ridge', 'RandomForest']
    )
    
    # 测试数据泄露检测
    print("\n" + "🔍" * 50)
    print("测试数据泄露检测功能...")
    print("🔍" * 50)
    
    leakage_report = check_data_leakage(
        df=df,
        target_col='future_ret_1d',
        feature_cols=feature_names,
        split_dates=split_dates
    )
    
    # 总结测试结果
    print(f"\n" + "="*80)
    print("                        测试结果总结")
    print("="*80)
    
    print("✅ 所有改进功能测试完成:")
    print("   1. numpy兼容性补丁 - 正常工作")
    print("   2. 警告过滤机制 - 正常工作")
    print("   3. 过拟合检测功能 - 正常工作")
    print("   4. 数据泄露检测功能 - 正常工作")
    
    # 检查模型性能
    best_model = max(validation_results.items(), key=lambda x: x[1]['test_r2'])
    print(f"\n📊 最佳模型: {best_model[0]} (测试R²: {best_model[1]['test_r2']:.4f})")
    
    # 检查是否有问题
    issues = 0
    for model_name, results in validation_results.items():
        if results['test_r2'] < 0:
            issues += 1
        elif results['overfitting_score'] > 0.1:
            issues += 1
    
    if issues == 0:
        print("✅ 所有模型通过验证，无严重问题")
    else:
        print(f"⚠️ 发现 {issues} 个模型存在问题")
    
    print("\n🎯 改进功能验证成功！")

if __name__ == "__main__":
    test_improvements()
